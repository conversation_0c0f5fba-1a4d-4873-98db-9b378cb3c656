<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>User Management - Admin Panel</title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/admin.css')); ?>" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Admin Sidebar -->
    <div class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
            <p>The Real World</p>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="<?php echo e(route('admin.dashboard')); ?>">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="<?php echo e(route('admin.users.index')); ?>" class="active">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="<?php echo e(route('admin.courses.index')); ?>">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="<?php echo e(route('admin.payments.index')); ?>">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="<?php echo e(route('admin.analytics')); ?>">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="<?php echo e(route('admin.settings')); ?>">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-secondary btn-sm">
                ← Back to Site
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Bar -->
        <div class="admin-topbar">
            <div class="topbar-left">
                <h1>User Management</h1>
                <p>Manage platform users, roles, and permissions</p>
            </div>
            <div class="topbar-right">
                <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                    + Add User
                </a>
                <div class="user-menu">
                    <span><?php echo e(Auth::user()->name); ?></span>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-link">Logout</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="content-section">
            <div class="filters-card">
                <form method="GET" action="<?php echo e(route('admin.users.index')); ?>" class="filters-form">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="search">Search Users</label>
                            <input type="text" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" 
                                   placeholder="Search by name or email..."
                                   class="search-input">
                        </div>
                        
                        <div class="filter-group">
                            <label for="role">Role</label>
                            <select id="role" name="role" class="filter-select">
                                <option value="">All Roles</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->name); ?>" 
                                            <?php echo e(request('role') === $role->name ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst($role->name)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="filter-select">
                                <option value="">All Status</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_from">From Date</label>
                            <input type="date" id="date_from" name="date_from" 
                                   value="<?php echo e(request('date_from')); ?>" class="filter-input">
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_to">To Date</label>
                            <input type="date" id="date_to" name="date_to" 
                                   value="<?php echo e(request('date_to')); ?>" class="filter-input">
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary">Clear</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="content-section">
            <div class="table-card">
                <div class="table-header">
                    <h3>Users (<?php echo e($users->total()); ?>)</h3>
                    <div class="table-actions">
                        <button onclick="exportData('users')" class="btn btn-secondary btn-sm">
                            📊 Export CSV
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th data-sortable>ID</th>
                                <th data-sortable>Name</th>
                                <th data-sortable>Email</th>
                                <th>Roles</th>
                                <th data-sortable>Status</th>
                                <th data-sortable>Courses</th>
                                <th data-sortable>Payments</th>
                                <th data-sortable>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr data-user-id="<?php echo e($user->id); ?>">
                                    <td><?php echo e($user->id); ?></td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar"><?php echo e(substr($user->name, 0, 1)); ?></div>
                                            <div class="user-details">
                                                <div class="user-name"><?php echo e($user->name); ?></div>
                                                <?php if($user->email_verified_at): ?>
                                                    <span class="verified-badge">✓ Verified</span>
                                                <?php else: ?>
                                                    <span class="unverified-badge">⚠ Unverified</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo e($user->email); ?></td>
                                    <td>
                                        <div class="user-roles">
                                            <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="role-badge role-<?php echo e($role->name); ?>">
                                                    <?php echo e(ucfirst($role->name)); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="user-status <?php echo e($user->is_active ? 'active' : 'inactive'); ?>">
                                            <?php echo e($user->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($user->enrolled_courses_count); ?></td>
                                    <td>$<?php echo e(number_format($user->payments_count * 99, 2)); ?></td>
                                    <td><?php echo e($user->created_at->format('M j, Y')); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="<?php echo e(route('admin.users.show', $user)); ?>" 
                                               class="btn btn-sm btn-secondary" title="View">
                                                👁️
                                            </a>
                                            <a href="<?php echo e(route('admin.users.edit', $user)); ?>" 
                                               class="btn btn-sm btn-primary" title="Edit">
                                                ✏️
                                            </a>
                                            <button onclick="toggleUserStatus(<?php echo e($user->id); ?>)" 
                                                    class="btn btn-sm btn-warning" title="Toggle Status">
                                                <?php echo e($user->is_active ? '🔒' : '🔓'); ?>

                                            </button>
                                            <?php if(!$user->hasRole('super-admin') && $user->id !== Auth::id()): ?>
                                                <form method="POST" action="<?php echo e(route('admin.users.impersonate', $user)); ?>" 
                                                      style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-info" title="Impersonate">
                                                        👤
                                                    </button>
                                                </form>
                                                <form method="POST" action="<?php echo e(route('admin.users.destroy', $user)); ?>" 
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        🗑️
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="empty-state">
                                        <div class="empty-content">
                                            <div class="empty-icon">👥</div>
                                            <h3>No users found</h3>
                                            <p>No users match your current filters.</p>
                                            <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                                                Add First User
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if($users->hasPages()): ?>
                    <div class="pagination-wrapper">
                        <?php echo e($users->appends(request()->query())->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="content-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h4><?php echo e($users->total()); ?></h4>
                        <p>Total Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h4><?php echo e($users->where('is_active', true)->count()); ?></h4>
                        <p>Active Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📧</div>
                    <div class="stat-content">
                        <h4><?php echo e($users->whereNotNull('email_verified_at')->count()); ?></h4>
                        <p>Verified Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h4><?php echo e($users->where('created_at', '>=', now()->subDays(30))->count()); ?></h4>
                        <p>New This Month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo e(asset('assets/js/admin.js')); ?>"></script>
    <script>
        // Additional user management specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit search form on input
            const searchInput = document.getElementById('search');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        this.form.submit();
                    }
                }, 500);
            });
        });
        
        // Toggle user status function
        function toggleUserStatus(userId) {
            if (confirm('Are you sure you want to change this user\'s status?')) {
                toggleUserStatus(userId);
            }
        }
    </script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/admin/users/index.blade.php ENDPATH**/ ?>