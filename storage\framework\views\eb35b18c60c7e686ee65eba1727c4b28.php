<?php $__env->startSection('title', 'Create Chat Room'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="create-chat-container">
        <div class="page-header">
            <h1>Create New Chat Room</h1>
            <p>Start a new conversation space for your community</p>
        </div>

        <div class="create-chat-card">
            <form method="POST" action="<?php echo e(route('chat.store')); ?>" class="create-chat-form">
                <?php echo csrf_field(); ?>

                <!-- Room Name -->
                <div class="form-group">
                    <label for="name" class="form-label">Room Name *</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           value="<?php echo e(old('name')); ?>" 
                           placeholder="Enter chat room name"
                           required>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Description -->
                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                              rows="4" 
                              placeholder="Describe what this chat room is for..."><?php echo e(old('description')); ?></textarea>
                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Room Type -->
                <div class="form-group">
                    <label class="form-label">Room Type *</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" 
                                   id="type_public" 
                                   name="type" 
                                   value="public" 
                                   <?php echo e(old('type', 'public') === 'public' ? 'checked' : ''); ?>>
                            <label for="type_public">
                                <div class="radio-content">
                                    <div class="radio-icon">🌍</div>
                                    <div class="radio-text">
                                        <h4>Public</h4>
                                        <p>Anyone can join and participate</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" 
                                   id="type_private" 
                                   name="type" 
                                   value="private" 
                                   <?php echo e(old('type') === 'private' ? 'checked' : ''); ?>>
                            <label for="type_private">
                                <div class="radio-content">
                                    <div class="radio-icon">🔒</div>
                                    <div class="radio-text">
                                        <h4>Private</h4>
                                        <p>Invitation only, members must be added</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Course Association (Optional) -->
                <div class="form-group">
                    <label for="course_id" class="form-label">Associate with Course (Optional)</label>
                    <select id="course_id" 
                            name="course_id" 
                            class="form-control <?php $__errorArgs = ['course_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">No course association</option>
                        <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($course->id); ?>" <?php echo e(old('course_id') == $course->id ? 'selected' : ''); ?>>
                                <?php echo e($course->title); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['course_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Room Rules -->
                <div class="form-group">
                    <label for="rules" class="form-label">Room Rules</label>
                    <textarea id="rules" 
                              name="rules" 
                              class="form-control <?php $__errorArgs = ['rules'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                              rows="3" 
                              placeholder="Set guidelines for this chat room..."><?php echo e(old('rules')); ?></textarea>
                    <?php $__errorArgs = ['rules'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="<?php echo e(route('chat.index')); ?>" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        🚀 Create Chat Room
                    </button>
                </div>
            </form>
        </div>

        <!-- Tips Section -->
        <div class="tips-section">
            <h3>💡 Tips for Creating Great Chat Rooms</h3>
            <div class="tips-grid">
                <div class="tip">
                    <div class="tip-icon">📝</div>
                    <h4>Clear Purpose</h4>
                    <p>Give your room a descriptive name and clear purpose so members know what to expect.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">👥</div>
                    <h4>Set Guidelines</h4>
                    <p>Establish clear rules to maintain a positive and productive environment.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🎯</div>
                    <h4>Stay Focused</h4>
                    <p>Keep conversations on-topic to provide value to all participants.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🤝</div>
                    <h4>Be Welcoming</h4>
                    <p>Create an inclusive space where everyone feels comfortable participating.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.create-chat-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #a0a0a0;
    font-size: 1.125rem;
}

.create-chat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
}

.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.radio-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.radio-option {
    position: relative;
}

.radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.radio-option label {
    display: block;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + label {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.radio-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.radio-icon {
    font-size: 2rem;
}

.radio-text h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.radio-text p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.tips-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.tips-section h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.tip {
    text-align: center;
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.tip h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .create-chat-container {
        padding: 1rem;
    }
    
    .create-chat-card {
        padding: 1.5rem;
    }
    
    .radio-group {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/chat/create.blade.php ENDPATH**/ ?>