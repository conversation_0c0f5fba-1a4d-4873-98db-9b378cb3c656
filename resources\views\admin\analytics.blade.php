@extends('layouts.app')

@section('title', 'Analytics Dashboard - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}" class="active">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>Analytics Dashboard</h1>
                    <p>Comprehensive platform insights and performance metrics</p>
                </div>
                <div class="header-actions">
                    <select class="date-range-selector">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                    <button type="button" class="btn btn-secondary" onclick="exportAnalytics()">
                        📊 Export Report
                    </button>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <!-- Key Metrics Overview -->
            <div class="metrics-overview">
                <div class="metric-card revenue">
                    <div class="metric-header">
                        <h3>Total Revenue</h3>
                        <span class="metric-icon">💰</span>
                    </div>
                    <div class="metric-value">${{ number_format($totalRevenue, 2) }}</div>
                    <div class="metric-change positive">
                        <span class="change-icon">📈</span>
                        +{{ number_format($revenueGrowth, 1) }}% vs last period
                    </div>
                </div>

                <div class="metric-card users">
                    <div class="metric-header">
                        <h3>Active Users</h3>
                        <span class="metric-icon">👥</span>
                    </div>
                    <div class="metric-value">{{ number_format($activeUsers) }}</div>
                    <div class="metric-change positive">
                        <span class="change-icon">📈</span>
                        +{{ number_format($userGrowth, 1) }}% vs last period
                    </div>
                </div>

                <div class="metric-card courses">
                    <div class="metric-header">
                        <h3>Course Enrollments</h3>
                        <span class="metric-icon">📚</span>
                    </div>
                    <div class="metric-value">{{ number_format($totalEnrollments) }}</div>
                    <div class="metric-change positive">
                        <span class="change-icon">📈</span>
                        +{{ number_format($enrollmentGrowth, 1) }}% vs last period
                    </div>
                </div>

                <div class="metric-card conversion">
                    <div class="metric-header">
                        <h3>Conversion Rate</h3>
                        <span class="metric-icon">🎯</span>
                    </div>
                    <div class="metric-value">{{ number_format($conversionRate, 1) }}%</div>
                    <div class="metric-change {{ $conversionChange >= 0 ? 'positive' : 'negative' }}">
                        <span class="change-icon">{{ $conversionChange >= 0 ? '📈' : '📉' }}</span>
                        {{ $conversionChange >= 0 ? '+' : '' }}{{ number_format($conversionChange, 1) }}% vs last period
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <!-- Revenue Chart -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Revenue Trend</h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" data-chart="revenue" data-period="daily">Daily</button>
                            <button class="chart-btn" data-chart="revenue" data-period="weekly">Weekly</button>
                            <button class="chart-btn" data-chart="revenue" data-period="monthly">Monthly</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- User Growth Chart -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>User Growth</h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" data-chart="users" data-period="daily">Daily</button>
                            <button class="chart-btn" data-chart="users" data-period="weekly">Weekly</button>
                            <button class="chart-btn" data-chart="users" data-period="monthly">Monthly</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="usersChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="performance-section">
                <div class="performance-grid">
                    <!-- Top Courses -->
                    <div class="performance-card">
                        <h3>Top Performing Courses</h3>
                        <div class="course-list">
                            @foreach($topCourses as $course)
                                <div class="course-item">
                                    <div class="course-info">
                                        <h4>{{ $course->title }}</h4>
                                        <p>{{ $course->instructor->name }}</p>
                                    </div>
                                    <div class="course-stats">
                                        <span class="stat">{{ $course->enrollments_count }} students</span>
                                        <span class="stat">${{ number_format($course->revenue, 2) }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Top Instructors -->
                    <div class="performance-card">
                        <h3>Top Instructors</h3>
                        <div class="instructor-list">
                            @foreach($topInstructors as $instructor)
                                <div class="instructor-item">
                                    <div class="instructor-info">
                                        <h4>{{ $instructor->name }}</h4>
                                        <p>{{ $instructor->courses_count }} courses</p>
                                    </div>
                                    <div class="instructor-stats">
                                        <span class="stat">{{ $instructor->total_students }} students</span>
                                        <span class="stat">${{ number_format($instructor->total_revenue, 2) }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- User Engagement -->
                    <div class="performance-card">
                        <h3>User Engagement</h3>
                        <div class="engagement-metrics">
                            <div class="engagement-item">
                                <span class="engagement-label">Daily Active Users</span>
                                <span class="engagement-value">{{ number_format($dailyActiveUsers) }}</span>
                            </div>
                            <div class="engagement-item">
                                <span class="engagement-label">Avg. Session Duration</span>
                                <span class="engagement-value">{{ $avgSessionDuration }} min</span>
                            </div>
                            <div class="engagement-item">
                                <span class="engagement-label">Course Completion Rate</span>
                                <span class="engagement-value">{{ number_format($completionRate, 1) }}%</span>
                            </div>
                            <div class="engagement-item">
                                <span class="engagement-label">Community Posts</span>
                                <span class="engagement-value">{{ number_format($communityPosts) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="performance-card">
                        <h3>Payment Method Distribution</h3>
                        <div class="payment-distribution">
                            <div class="payment-method">
                                <div class="method-info">
                                    <span class="method-icon">💳</span>
                                    <span class="method-name">Stripe</span>
                                </div>
                                <div class="method-stats">
                                    <span class="method-percentage">{{ number_format($stripePercentage, 1) }}%</span>
                                    <div class="method-bar">
                                        <div class="method-fill" style="width: {{ $stripePercentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="payment-method">
                                <div class="method-info">
                                    <span class="method-icon">₿</span>
                                    <span class="method-name">Bitcoin</span>
                                </div>
                                <div class="method-stats">
                                    <span class="method-percentage">{{ number_format($bitcoinPercentage, 1) }}%</span>
                                    <div class="method-bar">
                                        <div class="method-fill" style="width: {{ $bitcoinPercentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="payment-method">
                                <div class="method-info">
                                    <span class="method-icon">Ξ</span>
                                    <span class="method-name">Ethereum</span>
                                </div>
                                <div class="method-stats">
                                    <span class="method-percentage">{{ number_format($ethereumPercentage, 1) }}%</span>
                                    <div class="method-bar">
                                        <div class="method-fill" style="width: {{ $ethereumPercentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="payment-method">
                                <div class="method-info">
                                    <span class="method-icon">💎</span>
                                    <span class="method-name">Other Crypto</span>
                                </div>
                                <div class="method-stats">
                                    <span class="method-percentage">{{ number_format($otherCryptoPercentage, 1) }}%</span>
                                    <div class="method-bar">
                                        <div class="method-fill" style="width: {{ $otherCryptoPercentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Activity -->
            <div class="activity-section">
                <div class="activity-card">
                    <div class="activity-header">
                        <h3>Real-time Activity</h3>
                        <div class="activity-status">
                            <span class="status-indicator active"></span>
                            <span class="status-text">Live</span>
                        </div>
                    </div>
                    <div class="activity-feed" id="activityFeed">
                        @foreach($recentActivity as $activity)
                            <div class="activity-item">
                                <div class="activity-icon">
                                    @if($activity->type === 'enrollment')
                                        📚
                                    @elseif($activity->type === 'payment')
                                        💰
                                    @elseif($activity->type === 'registration')
                                        👤
                                    @else
                                        📝
                                    @endif
                                </div>
                                <div class="activity-content">
                                    <p>{{ $activity->description }}</p>
                                    <span class="activity-time">{{ $activity->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.metric-card.revenue::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.metric-card.users::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.metric-card.courses::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.metric-card.conversion::before {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.metric-header h3 {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

.metric-icon {
    font-size: 1.5rem;
}

.metric-value {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-change.positive {
    color: #22c55e;
}

.metric-change.negative {
    color: #ef4444;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #a0a0a0;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.chart-container {
    height: 300px;
    position: relative;
}

.performance-section {
    margin-bottom: 2rem;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.performance-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.performance-card h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.course-list, .instructor-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.course-item, .instructor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.course-info h4, .instructor-info h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.course-info p, .instructor-info p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.course-stats, .instructor-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.stat {
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 500;
}

.engagement-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.engagement-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.engagement-item:last-child {
    border-bottom: none;
}

.engagement-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.engagement-value {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
}

.payment-distribution {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.method-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.method-icon {
    font-size: 1.25rem;
}

.method-name {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.method-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.method-percentage {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.method-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.method-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.activity-section {
    margin-bottom: 2rem;
}

.activity-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.activity-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #22c55e;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 500;
}

.activity-feed {
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.activity-icon {
    font-size: 1.25rem;
    margin-top: 0.25rem;
}

.activity-content p {
    color: #ffffff;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .metrics-overview {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .performance-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function exportAnalytics() {
    // Implementation for analytics export
    console.log('Export analytics report');
}

// Chart initialization would go here
// This would typically use Chart.js or similar library
console.log('Analytics dashboard loaded');
</script>
@endsection
