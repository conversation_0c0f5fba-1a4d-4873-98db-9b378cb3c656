<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class AssignAdminRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:make-admin {email : The email of the user to make admin}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign admin role to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }
        
        // Ensure admin role exists
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        
        // Assign admin role to user
        if ($user->hasRole('admin')) {
            $this->info("User {$user->name} ({$user->email}) already has admin role.");
        } else {
            $user->assignRole('admin');
            $this->info("Admin role assigned to {$user->name} ({$user->email}) successfully!");
        }
        
        // Show user's current roles
        $roles = $user->roles->pluck('name')->toArray();
        $this->info("Current roles: " . implode(', ', $roles));
        
        return 0;
    }
}
