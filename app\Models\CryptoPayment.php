<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CryptoPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_id',
        'currency',
        'amount',
        'usd_amount',
        'exchange_rate',
        'payment_address',
        'transaction_hash',
        'status',
        'confirmed_at',
        'refunded_at',
        'expires_at',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'usd_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the payment that owns the crypto payment.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include confirmed payments.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope a query to only include expired payments.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if payment has expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' || $this->expires_at->isPast();
    }

    /**
     * Get formatted crypto amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        $decimals = match($this->currency) {
            'BTC' => 8,
            'ETH' => 6,
            'USDT', 'USDC' => 2,
            default => 8,
        };

        return number_format($this->amount, $decimals) . ' ' . $this->currency;
    }

    /**
     * Get currency symbol.
     */
    public function getCurrencySymbolAttribute(): string
    {
        return match($this->currency) {
            'BTC' => '₿',
            'ETH' => 'Ξ',
            'USDT' => '₮',
            'USDC' => '$',
            default => $this->currency,
        };
    }

    /**
     * Get currency name.
     */
    public function getCurrencyNameAttribute(): string
    {
        return match($this->currency) {
            'BTC' => 'Bitcoin',
            'ETH' => 'Ethereum',
            'USDT' => 'Tether USD',
            'USDC' => 'USD Coin',
            default => $this->currency,
        };
    }

    /**
     * Get blockchain explorer URL.
     */
    public function getExplorerUrlAttribute(): ?string
    {
        if (!$this->transaction_hash) {
            return null;
        }

        return match($this->currency) {
            'BTC' => 'https://blockchair.com/bitcoin/transaction/' . $this->transaction_hash,
            'ETH', 'USDT', 'USDC' => 'https://etherscan.io/tx/' . $this->transaction_hash,
            default => null,
        };
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'confirmed' => 'badge-success',
            'pending' => 'badge-warning',
            'expired' => 'badge-danger',
            'refunded' => 'badge-info',
            default => 'badge-secondary',
        };
    }

    /**
     * Get time remaining until expiration.
     */
    public function getTimeRemainingAttribute(): ?string
    {
        if ($this->status !== 'pending' || $this->expires_at->isPast()) {
            return null;
        }

        $diff = $this->expires_at->diffInMinutes(now());

        if ($diff < 1) {
            return 'Less than 1 minute';
        } elseif ($diff < 60) {
            return $diff . ' minutes';
        } else {
            $hours = floor($diff / 60);
            $minutes = $diff % 60;
            return $hours . 'h ' . $minutes . 'm';
        }
    }
}
