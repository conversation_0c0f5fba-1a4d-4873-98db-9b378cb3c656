@extends('layouts.app')

@section('title', 'Advanced Analytics')

@section('content')
<div class="analytics-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-info">
                <h1>📊 Advanced Analytics</h1>
                <p>Deep insights into your platform performance and user behavior</p>
            </div>
            <div class="header-actions">
                <div class="date-range-picker">
                    <select id="dateRange" onchange="updateDateRange()">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                        <option value="custom">Custom range</option>
                    </select>
                </div>
                <button type="button" class="btn btn-primary" onclick="exportReport()">
                    📊 Export Report
                </button>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="kpi-section">
        <div class="kpi-grid">
            <div class="kpi-card revenue">
                <div class="kpi-header">
                    <h3>Total Revenue</h3>
                    <span class="kpi-icon">💰</span>
                </div>
                <div class="kpi-value">${{ number_format($analytics['revenue']['total'], 2) }}</div>
                <div class="kpi-change {{ $analytics['revenue']['change'] >= 0 ? 'positive' : 'negative' }}">
                    <span class="change-icon">{{ $analytics['revenue']['change'] >= 0 ? '📈' : '📉' }}</span>
                    <span class="change-text">{{ abs($analytics['revenue']['change']) }}% vs last period</span>
                </div>
                <div class="kpi-breakdown">
                    <div class="breakdown-item">
                        <span class="breakdown-label">Courses</span>
                        <span class="breakdown-value">${{ number_format($analytics['revenue']['courses'], 2) }}</span>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Subscriptions</span>
                        <span class="breakdown-value">${{ number_format($analytics['revenue']['subscriptions'], 2) }}</span>
                    </div>
                </div>
            </div>

            <div class="kpi-card users">
                <div class="kpi-header">
                    <h3>Active Users</h3>
                    <span class="kpi-icon">👥</span>
                </div>
                <div class="kpi-value">{{ number_format($analytics['users']['active']) }}</div>
                <div class="kpi-change {{ $analytics['users']['change'] >= 0 ? 'positive' : 'negative' }}">
                    <span class="change-icon">{{ $analytics['users']['change'] >= 0 ? '📈' : '📉' }}</span>
                    <span class="change-text">{{ abs($analytics['users']['change']) }}% vs last period</span>
                </div>
                <div class="kpi-breakdown">
                    <div class="breakdown-item">
                        <span class="breakdown-label">New Users</span>
                        <span class="breakdown-value">{{ number_format($analytics['users']['new']) }}</span>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Returning</span>
                        <span class="breakdown-value">{{ number_format($analytics['users']['returning']) }}</span>
                    </div>
                </div>
            </div>

            <div class="kpi-card engagement">
                <div class="kpi-header">
                    <h3>Engagement Rate</h3>
                    <span class="kpi-icon">📊</span>
                </div>
                <div class="kpi-value">{{ number_format($analytics['engagement']['rate'], 1) }}%</div>
                <div class="kpi-change {{ $analytics['engagement']['change'] >= 0 ? 'positive' : 'negative' }}">
                    <span class="change-icon">{{ $analytics['engagement']['change'] >= 0 ? '📈' : '📉' }}</span>
                    <span class="change-text">{{ abs($analytics['engagement']['change']) }}% vs last period</span>
                </div>
                <div class="kpi-breakdown">
                    <div class="breakdown-item">
                        <span class="breakdown-label">Avg Session</span>
                        <span class="breakdown-value">{{ $analytics['engagement']['session_duration'] }}</span>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Pages/Session</span>
                        <span class="breakdown-value">{{ number_format($analytics['engagement']['pages_per_session'], 1) }}</span>
                    </div>
                </div>
            </div>

            <div class="kpi-card conversion">
                <div class="kpi-header">
                    <h3>Conversion Rate</h3>
                    <span class="kpi-icon">🎯</span>
                </div>
                <div class="kpi-value">{{ number_format($analytics['conversion']['rate'], 2) }}%</div>
                <div class="kpi-change {{ $analytics['conversion']['change'] >= 0 ? 'positive' : 'negative' }}">
                    <span class="change-icon">{{ $analytics['conversion']['change'] >= 0 ? '📈' : '📉' }}</span>
                    <span class="change-text">{{ abs($analytics['conversion']['change']) }}% vs last period</span>
                </div>
                <div class="kpi-breakdown">
                    <div class="breakdown-item">
                        <span class="breakdown-label">Visitors</span>
                        <span class="breakdown-value">{{ number_format($analytics['conversion']['visitors']) }}</span>
                    </div>
                    <div class="breakdown-item">
                        <span class="breakdown-label">Conversions</span>
                        <span class="breakdown-value">{{ number_format($analytics['conversion']['conversions']) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <!-- Revenue Trends -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>Revenue Trends</h3>
                <div class="chart-controls">
                    <button class="chart-btn active" data-metric="revenue">Revenue</button>
                    <button class="chart-btn" data-metric="orders">Orders</button>
                    <button class="chart-btn" data-metric="avg_order">Avg Order</button>
                </div>
            </div>
            <div class="chart-content">
                <canvas id="revenueChart" width="800" height="400"></canvas>
            </div>
        </div>

        <!-- User Analytics -->
        <div class="chart-container">
            <div class="chart-header">
                <h3>User Analytics</h3>
                <div class="chart-controls">
                    <button class="chart-btn active" data-metric="users">Users</button>
                    <button class="chart-btn" data-metric="sessions">Sessions</button>
                    <button class="chart-btn" data-metric="pageviews">Pageviews</button>
                </div>
            </div>
            <div class="chart-content">
                <canvas id="userChart" width="800" height="400"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="detailed-analytics">
        <!-- Course Performance -->
        <div class="analytics-card">
            <div class="card-header">
                <h3>🎓 Top Performing Courses</h3>
                <button type="button" class="view-all-btn" onclick="viewAllCourses()">View All</button>
            </div>
            <div class="performance-table">
                <div class="table-header">
                    <div class="header-cell">Course</div>
                    <div class="header-cell">Revenue</div>
                    <div class="header-cell">Enrollments</div>
                    <div class="header-cell">Completion Rate</div>
                    <div class="header-cell">Rating</div>
                </div>
                @foreach($analytics['top_courses'] as $course)
                    <div class="table-row">
                        <div class="table-cell course-info">
                            <div class="course-thumbnail">
                                @if($course['thumbnail'])
                                    <img src="{{ Storage::url($course['thumbnail']) }}" alt="{{ $course['title'] }}">
                                @else
                                    <div class="thumbnail-placeholder">📚</div>
                                @endif
                            </div>
                            <div class="course-details">
                                <h4>{{ $course['title'] }}</h4>
                                <p>{{ $course['instructor'] }}</p>
                            </div>
                        </div>
                        <div class="table-cell">${{ number_format($course['revenue'], 2) }}</div>
                        <div class="table-cell">{{ number_format($course['enrollments']) }}</div>
                        <div class="table-cell">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {{ $course['completion_rate'] }}%"></div>
                            </div>
                            <span class="progress-text">{{ number_format($course['completion_rate'], 1) }}%</span>
                        </div>
                        <div class="table-cell">
                            <div class="rating">
                                <span class="rating-value">{{ number_format($course['rating'], 1) }}</span>
                                <span class="rating-stars">⭐</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- User Behavior -->
        <div class="analytics-card">
            <div class="card-header">
                <h3>👤 User Behavior Insights</h3>
                <div class="behavior-filters">
                    <select id="behaviorSegment" onchange="updateBehaviorData()">
                        <option value="all">All Users</option>
                        <option value="new">New Users</option>
                        <option value="returning">Returning Users</option>
                        <option value="premium">Premium Users</option>
                    </select>
                </div>
            </div>
            <div class="behavior-grid">
                <div class="behavior-metric">
                    <div class="metric-icon">⏱️</div>
                    <div class="metric-content">
                        <h4>Avg Session Duration</h4>
                        <div class="metric-value">{{ $analytics['behavior']['session_duration'] }}</div>
                        <div class="metric-trend positive">+12% vs last period</div>
                    </div>
                </div>

                <div class="behavior-metric">
                    <div class="metric-icon">📄</div>
                    <div class="metric-content">
                        <h4>Pages per Session</h4>
                        <div class="metric-value">{{ number_format($analytics['behavior']['pages_per_session'], 1) }}</div>
                        <div class="metric-trend positive">+8% vs last period</div>
                    </div>
                </div>

                <div class="behavior-metric">
                    <div class="metric-icon">🚪</div>
                    <div class="metric-content">
                        <h4>Bounce Rate</h4>
                        <div class="metric-value">{{ number_format($analytics['behavior']['bounce_rate'], 1) }}%</div>
                        <div class="metric-trend negative">-5% vs last period</div>
                    </div>
                </div>

                <div class="behavior-metric">
                    <div class="metric-icon">🔄</div>
                    <div class="metric-content">
                        <h4>Return Rate</h4>
                        <div class="metric-value">{{ number_format($analytics['behavior']['return_rate'], 1) }}%</div>
                        <div class="metric-trend positive">+15% vs last period</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="analytics-card">
            <div class="card-header">
                <h3>🌍 Geographic Distribution</h3>
                <div class="geo-controls">
                    <button class="geo-btn active" data-view="countries">Countries</button>
                    <button class="geo-btn" data-view="cities">Cities</button>
                    <button class="geo-btn" data-view="regions">Regions</button>
                </div>
            </div>
            <div class="geo-content">
                <div class="geo-map">
                    <div id="worldMap" style="height: 300px; background: rgba(255,255,255,0.05); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: #a0a0a0;">
                        🗺️ Interactive World Map
                        <br><small>Geographic data visualization</small>
                    </div>
                </div>
                <div class="geo-stats">
                    @foreach($analytics['geographic'] as $location)
                        <div class="geo-item">
                            <div class="geo-flag">{{ $location['flag'] }}</div>
                            <div class="geo-info">
                                <h4>{{ $location['name'] }}</h4>
                                <p>{{ number_format($location['users']) }} users</p>
                            </div>
                            <div class="geo-percentage">{{ number_format($location['percentage'], 1) }}%</div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Analytics -->
    <div class="realtime-section">
        <div class="realtime-header">
            <h3>⚡ Real-time Analytics</h3>
            <div class="realtime-status">
                <span class="status-indicator active"></span>
                <span class="status-text">Live data</span>
            </div>
        </div>

        <div class="realtime-grid">
            <div class="realtime-card">
                <div class="realtime-metric">
                    <h4>Active Users</h4>
                    <div class="realtime-value" id="activeUsers">{{ $analytics['realtime']['active_users'] }}</div>
                </div>
                <div class="realtime-chart">
                    <canvas id="activeUsersChart" width="200" height="100"></canvas>
                </div>
            </div>

            <div class="realtime-card">
                <div class="realtime-metric">
                    <h4>Page Views</h4>
                    <div class="realtime-value" id="pageViews">{{ $analytics['realtime']['page_views'] }}</div>
                </div>
                <div class="realtime-chart">
                    <canvas id="pageViewsChart" width="200" height="100"></canvas>
                </div>
            </div>

            <div class="realtime-card">
                <div class="realtime-metric">
                    <h4>Revenue Today</h4>
                    <div class="realtime-value" id="revenueToday">${{ number_format($analytics['realtime']['revenue_today'], 2) }}</div>
                </div>
                <div class="realtime-chart">
                    <canvas id="revenueChart" width="200" height="100"></canvas>
                </div>
            </div>

            <div class="realtime-card">
                <div class="realtime-metric">
                    <h4>Conversions</h4>
                    <div class="realtime-value" id="conversions">{{ $analytics['realtime']['conversions'] }}</div>
                </div>
                <div class="realtime-chart">
                    <canvas id="conversionsChart" width="200" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.analytics-dashboard {
    max-width: 1600px;
    margin: 0 auto;
    padding: 2rem;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1.125rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.date-range-picker select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.kpi-section {
    margin-bottom: 2rem;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.kpi-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.kpi-header h3 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kpi-icon {
    font-size: 1.5rem;
}

.kpi-value {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.kpi-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.kpi-change.positive .change-text {
    color: #22c55e;
}

.kpi-change.negative .change-text {
    color: #ef4444;
}

.change-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.kpi-breakdown {
    display: flex;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.breakdown-item {
    text-align: center;
}

.breakdown-label {
    display: block;
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.breakdown-value {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #a0a0a0;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.chart-content {
    height: 300px;
}

.detailed-analytics {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.card-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.view-all-btn {
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #3b82f6;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: rgba(59, 130, 246, 0.3);
}

.performance-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-weight: 600;
    color: #ffffff;
    font-size: 0.875rem;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    align-items: center;
    transition: all 0.3s ease;
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.course-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.course-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.course-details h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.course-details p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.table-cell {
    color: #ffffff;
    font-size: 0.875rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.progress-text {
    font-size: 0.75rem;
    color: #a0a0a0;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.rating-value {
    font-weight: 600;
}

.behavior-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.behavior-metric {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-icon {
    font-size: 2.5rem;
}

.metric-content h4 {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.metric-value {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-trend {
    font-size: 0.75rem;
    font-weight: 500;
}

.metric-trend.positive {
    color: #22c55e;
}

.metric-trend.negative {
    color: #ef4444;
}

.geo-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.geo-controls {
    display: flex;
    gap: 0.5rem;
}

.geo-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #a0a0a0;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.geo-btn.active,
.geo-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.geo-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.geo-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.geo-flag {
    font-size: 1.5rem;
}

.geo-info {
    flex: 1;
}

.geo-info h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.geo-info p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.geo-percentage {
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 600;
}

.realtime-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.realtime-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.realtime-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.realtime-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #22c55e;
    animation: pulse 2s infinite;
}

.status-text {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 500;
}

.realtime-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.realtime-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
}

.realtime-metric h4 {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.realtime-value {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.realtime-chart {
    height: 80px;
}

@media (max-width: 768px) {
    .analytics-dashboard {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .geo-content {
        grid-template-columns: 1fr;
    }

    .realtime-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
// Initialize analytics dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    startRealTimeUpdates();
});

function initializeCharts() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        // Chart.js implementation would go here
        console.log('Initialize revenue chart');
    }

    // User Chart
    const userCtx = document.getElementById('userChart');
    if (userCtx) {
        // Chart.js implementation would go here
        console.log('Initialize user chart');
    }

    // Real-time charts
    initializeRealTimeCharts();
}

function initializeRealTimeCharts() {
    // Initialize small real-time charts
    console.log('Initialize real-time charts');
}

function startRealTimeUpdates() {
    // Update real-time metrics every 30 seconds
    setInterval(updateRealTimeMetrics, 30000);
}

function updateRealTimeMetrics() {
    // Fetch and update real-time data
    fetch('/api/analytics/realtime')
        .then(response => response.json())
        .then(data => {
            document.getElementById('activeUsers').textContent = data.active_users;
            document.getElementById('pageViews').textContent = data.page_views;
            document.getElementById('revenueToday').textContent = '$' + data.revenue_today.toFixed(2);
            document.getElementById('conversions').textContent = data.conversions;
        })
        .catch(error => console.error('Error updating real-time metrics:', error));
}

function updateDateRange() {
    const range = document.getElementById('dateRange').value;
    console.log('Update date range:', range);
    // Implement date range update logic
}

function exportReport() {
    console.log('Export analytics report');
    // Implement report export functionality
}

function viewAllCourses() {
    console.log('View all courses');
    // Navigate to detailed course analytics
}

function updateBehaviorData() {
    const segment = document.getElementById('behaviorSegment').value;
    console.log('Update behavior data for segment:', segment);
    // Implement behavior data update
}

// Chart control handlers
document.querySelectorAll('.chart-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const container = this.closest('.chart-container');
        container.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        const metric = this.dataset.metric;
        console.log('Switch chart metric:', metric);
        // Implement chart metric switching
    });
});

// Geographic view handlers
document.querySelectorAll('.geo-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const container = this.closest('.analytics-card');
        container.querySelectorAll('.geo-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        const view = this.dataset.view;
        console.log('Switch geographic view:', view);
        // Implement geographic view switching
    });
});
</script>
@endsection