<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\CryptoPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CryptoController extends Controller
{
    private $supportedCurrencies = [
        'BTC' => [
            'name' => 'Bitcoin',
            'symbol' => '₿',
            'decimals' => 8,
            'network' => 'bitcoin',
        ],
        'ETH' => [
            'name' => 'Ethereum',
            'symbol' => 'Ξ',
            'decimals' => 18,
            'network' => 'ethereum',
        ],
        'USDT' => [
            'name' => 'Tether USD',
            'symbol' => '₮',
            'decimals' => 6,
            'network' => 'ethereum', // USDT on Ethereum
        ],
        'USDC' => [
            'name' => 'USD Coin',
            'symbol' => '$',
            'decimals' => 6,
            'network' => 'ethereum',
        ],
    ];

    /**
     * Show crypto payment options.
     */
    public function showPaymentOptions(Payment $payment)
    {
        // Get current crypto prices
        $cryptoPrices = $this->getCryptoPrices();

        // Calculate amounts for each currency
        $cryptoAmounts = [];
        foreach ($this->supportedCurrencies as $currency => $details) {
            if (isset($cryptoPrices[$currency])) {
                $cryptoAmounts[$currency] = [
                    'amount' => $payment->amount / $cryptoPrices[$currency],
                    'price_usd' => $cryptoPrices[$currency],
                    'details' => $details,
                ];
            }
        }

        return view('payment.crypto-options', compact('payment', 'cryptoAmounts'));
    }

    /**
     * Process crypto payment.
     */
    public function processPayment(Payment $payment, Request $request)
    {
        $request->validate([
            'crypto_currency' => 'required|in:' . implode(',', array_keys($this->supportedCurrencies)),
        ]);

        try {
            $currency = $request->crypto_currency;
            $currencyDetails = $this->supportedCurrencies[$currency];

            // Get current price
            $cryptoPrices = $this->getCryptoPrices();
            if (!isset($cryptoPrices[$currency])) {
                throw new \Exception('Unable to get current price for ' . $currency);
            }

            $cryptoAmount = $payment->amount / $cryptoPrices[$currency];

            // Generate payment address (in production, use proper wallet generation)
            $paymentAddress = $this->generatePaymentAddress($currency);

            // Create crypto payment record
            $cryptoPayment = CryptoPayment::create([
                'payment_id' => $payment->id,
                'currency' => $currency,
                'amount' => $cryptoAmount,
                'usd_amount' => $payment->amount,
                'exchange_rate' => $cryptoPrices[$currency],
                'payment_address' => $paymentAddress,
                'status' => 'pending',
                'expires_at' => now()->addMinutes(30), // 30-minute payment window
            ]);

            // Update main payment record
            $payment->update([
                'status' => 'pending_crypto',
                'gateway_response' => [
                    'crypto_payment_id' => $cryptoPayment->id,
                    'currency' => $currency,
                    'amount' => $cryptoAmount,
                    'address' => $paymentAddress,
                ],
            ]);

            return response()->json([
                'success' => true,
                'crypto_payment' => $cryptoPayment,
                'payment_url' => route('payment.crypto.show', $cryptoPayment),
            ]);

        } catch (\Exception $e) {
            Log::error('Crypto payment processing failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Crypto payment processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show crypto payment details.
     */
    public function showCryptoPayment(CryptoPayment $cryptoPayment)
    {
        $cryptoPayment->load('payment.course');

        // Check if payment has expired
        if ($cryptoPayment->expires_at->isPast() && $cryptoPayment->status === 'pending') {
            $cryptoPayment->update(['status' => 'expired']);
            $cryptoPayment->payment->update(['status' => 'failed', 'failure_reason' => 'Payment expired']);
        }

        return view('payment.crypto-payment', compact('cryptoPayment'));
    }

    /**
     * Check payment status.
     */
    public function checkPaymentStatus(CryptoPayment $cryptoPayment)
    {
        try {
            // In production, you would check the blockchain for transactions
            // For demo purposes, we'll simulate payment verification
            $isConfirmed = $this->checkBlockchainTransaction($cryptoPayment);

            if ($isConfirmed) {
                $cryptoPayment->update([
                    'status' => 'confirmed',
                    'confirmed_at' => now(),
                    'transaction_hash' => Str::random(64), // Simulated transaction hash
                ]);

                $cryptoPayment->payment->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                ]);

                // Enroll user in course
                $cryptoPayment->payment->course->students()->attach($cryptoPayment->payment->user_id, [
                    'enrolled_at' => now(),
                    'progress' => 0,
                ]);

                return response()->json([
                    'success' => true,
                    'status' => 'confirmed',
                    'message' => 'Payment confirmed! You are now enrolled in the course.',
                    'redirect_url' => route('courses.show', $cryptoPayment->payment->course)
                ]);
            }

            return response()->json([
                'success' => true,
                'status' => $cryptoPayment->status,
                'message' => 'Payment is still pending confirmation.',
            ]);

        } catch (\Exception $e) {
            Log::error('Crypto payment status check failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to check payment status.'
            ], 500);
        }
    }

    /**
     * Process crypto refund.
     */
    public function processRefund(Payment $payment)
    {
        $cryptoPayment = $payment->cryptoPayment;

        if (!$cryptoPayment) {
            return response()->json([
                'success' => false,
                'message' => 'No crypto payment found for this payment.'
            ], 400);
        }

        try {
            // In production, you would initiate a refund transaction
            // For demo purposes, we'll mark it as refunded
            $cryptoPayment->update([
                'status' => 'refunded',
                'refunded_at' => now(),
            ]);

            $payment->update([
                'status' => 'refunded',
                'refunded_at' => now(),
            ]);

            // Remove user from course
            $payment->course->students()->detach($payment->user_id);

            return response()->json([
                'success' => true,
                'message' => 'Crypto refund initiated. Please allow 1-3 business days for processing.',
            ]);

        } catch (\Exception $e) {
            Log::error('Crypto refund processing failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current crypto prices from API.
     */
    private function getCryptoPrices()
    {
        try {
            // Using CoinGecko API for demo (free tier)
            $response = Http::timeout(10)->get('https://api.coingecko.com/api/v3/simple/price', [
                'ids' => 'bitcoin,ethereum,tether,usd-coin',
                'vs_currencies' => 'usd',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'BTC' => $data['bitcoin']['usd'] ?? 50000,
                    'ETH' => $data['ethereum']['usd'] ?? 3000,
                    'USDT' => $data['tether']['usd'] ?? 1,
                    'USDC' => $data['usd-coin']['usd'] ?? 1,
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to fetch crypto prices: ' . $e->getMessage());
        }

        // Fallback prices if API fails
        return [
            'BTC' => 50000,
            'ETH' => 3000,
            'USDT' => 1,
            'USDC' => 1,
        ];
    }

    /**
     * Generate payment address for crypto currency.
     */
    private function generatePaymentAddress($currency)
    {
        // In production, you would generate actual wallet addresses
        // For demo purposes, we'll generate fake addresses
        switch ($currency) {
            case 'BTC':
                return '1' . Str::random(33); // Bitcoin address format
            case 'ETH':
            case 'USDT':
            case 'USDC':
                return '0x' . Str::random(40); // Ethereum address format
            default:
                return Str::random(34);
        }
    }

    /**
     * Check blockchain for transaction confirmation.
     */
    private function checkBlockchainTransaction($cryptoPayment)
    {
        // In production, you would:
        // 1. Query blockchain APIs (like Blockchair, Etherscan, etc.)
        // 2. Check for transactions to the payment address
        // 3. Verify the amount matches
        // 4. Check for sufficient confirmations

        // For demo purposes, we'll simulate a 10% chance of confirmation
        // after 2 minutes have passed
        if ($cryptoPayment->created_at->diffInMinutes(now()) >= 2) {
            return rand(1, 10) <= 1; // 10% chance
        }

        return false;
    }

    /**
     * Get supported cryptocurrencies.
     */
    public function getSupportedCurrencies()
    {
        return response()->json([
            'success' => true,
            'currencies' => $this->supportedCurrencies,
            'prices' => $this->getCryptoPrices(),
        ]);
    }

    /**
     * Webhook handler for crypto payment notifications.
     */
    public function handleWebhook(Request $request)
    {
        // In production, you would handle webhooks from blockchain monitoring services
        // like BlockCypher, Alchemy, or your own node

        $request->validate([
            'transaction_hash' => 'required|string',
            'address' => 'required|string',
            'amount' => 'required|numeric',
            'confirmations' => 'required|integer',
        ]);

        try {
            $cryptoPayment = CryptoPayment::where('payment_address', $request->address)
                ->where('status', 'pending')
                ->first();

            if (!$cryptoPayment) {
                return response('Payment not found', 404);
            }

            // Verify amount matches (with some tolerance for fees)
            $tolerance = 0.001; // 0.1% tolerance
            if (abs($request->amount - $cryptoPayment->amount) > ($cryptoPayment->amount * $tolerance)) {
                Log::warning('Crypto payment amount mismatch', [
                    'expected' => $cryptoPayment->amount,
                    'received' => $request->amount,
                ]);
                return response('Amount mismatch', 400);
            }

            // Check if we have enough confirmations
            $requiredConfirmations = $cryptoPayment->currency === 'BTC' ? 3 : 12; // BTC: 3, ETH: 12

            if ($request->confirmations >= $requiredConfirmations) {
                $cryptoPayment->update([
                    'status' => 'confirmed',
                    'confirmed_at' => now(),
                    'transaction_hash' => $request->transaction_hash,
                ]);

                $cryptoPayment->payment->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                ]);

                // Enroll user in course
                $cryptoPayment->payment->course->students()->attach($cryptoPayment->payment->user_id, [
                    'enrolled_at' => now(),
                    'progress' => 0,
                ]);
            }

            return response('Webhook processed', 200);

        } catch (\Exception $e) {
            Log::error('Crypto webhook processing failed: ' . $e->getMessage());
            return response('Webhook processing failed', 500);
        }
    }
}
