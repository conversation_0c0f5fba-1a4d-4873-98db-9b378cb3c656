@extends('layouts.app')

@section('title', 'Live Stream - ' . $stream->title)

@section('content')
<div class="live-stream-container">
    <!-- Stream Header -->
    <div class="stream-header">
        <div class="stream-info">
            <div class="stream-status">
                <span class="live-indicator {{ $stream->is_live ? 'active' : 'offline' }}">
                    {{ $stream->is_live ? '🔴 LIVE' : '⚫ OFFLINE' }}
                </span>
                <span class="viewer-count">{{ $stream->viewer_count }} viewers</span>
            </div>
            <h1 class="stream-title">{{ $stream->title }}</h1>
            <div class="stream-meta">
                <span class="instructor">by {{ $stream->instructor->name }}</span>
                <span class="category">{{ $stream->course->category->name ?? 'General' }}</span>
                <span class="scheduled-time">
                    @if($stream->scheduled_at)
                        Scheduled: {{ $stream->scheduled_at->format('M j, Y g:i A') }}
                    @endif
                </span>
            </div>
        </div>
        
        <div class="stream-actions">
            @if(Auth::user()->id === $stream->instructor_id)
                <button type="button" class="btn btn-primary" onclick="toggleStream()">
                    {{ $stream->is_live ? '⏹️ End Stream' : '▶️ Start Stream' }}
                </button>
                <button type="button" class="btn btn-secondary" onclick="openStreamSettings()">
                    ⚙️ Settings
                </button>
            @else
                <button type="button" class="btn btn-outline" onclick="toggleNotifications()">
                    🔔 Notify Me
                </button>
                <button type="button" class="btn btn-secondary" onclick="shareStream()">
                    📤 Share
                </button>
            @endif
        </div>
    </div>

    <!-- Main Stream Layout -->
    <div class="stream-layout">
        <!-- Video Player -->
        <div class="video-section">
            <div class="video-container">
                @if($stream->is_live)
                    <div class="video-player" id="videoPlayer">
                        <!-- WebRTC Video Stream -->
                        <video id="liveVideo" autoplay muted playsinline></video>
                        
                        <!-- Stream Controls -->
                        <div class="video-controls">
                            <div class="control-group left">
                                <button class="control-btn" onclick="togglePlay()">
                                    <span id="playIcon">⏸️</span>
                                </button>
                                <button class="control-btn" onclick="toggleMute()">
                                    <span id="muteIcon">🔊</span>
                                </button>
                                <div class="volume-slider">
                                    <input type="range" id="volumeSlider" min="0" max="100" value="50">
                                </div>
                            </div>
                            
                            <div class="control-group center">
                                <span class="stream-time">{{ $stream->duration ?? '00:00' }}</span>
                            </div>
                            
                            <div class="control-group right">
                                <button class="control-btn" onclick="toggleQuality()">
                                    <span>HD</span>
                                </button>
                                <button class="control-btn" onclick="toggleFullscreen()">
                                    <span>⛶</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Stream Overlay -->
                        <div class="stream-overlay">
                            <div class="overlay-stats">
                                <span class="live-badge">🔴 LIVE</span>
                                <span class="viewer-badge">{{ $stream->viewer_count }} watching</span>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="stream-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">📺</div>
                            <h3>Stream Offline</h3>
                            <p>This stream is currently offline. Check back later or enable notifications to be alerted when it goes live.</p>
                            @if($stream->scheduled_at && $stream->scheduled_at->isFuture())
                                <div class="countdown-timer" data-target="{{ $stream->scheduled_at->toISOString() }}">
                                    <div class="countdown-item">
                                        <span class="countdown-number" id="days">00</span>
                                        <span class="countdown-label">Days</span>
                                    </div>
                                    <div class="countdown-item">
                                        <span class="countdown-number" id="hours">00</span>
                                        <span class="countdown-label">Hours</span>
                                    </div>
                                    <div class="countdown-item">
                                        <span class="countdown-number" id="minutes">00</span>
                                        <span class="countdown-label">Minutes</span>
                                    </div>
                                    <div class="countdown-item">
                                        <span class="countdown-number" id="seconds">00</span>
                                        <span class="countdown-label">Seconds</span>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Stream Description -->
            <div class="stream-description">
                <h3>About This Stream</h3>
                <p>{{ $stream->description }}</p>
                
                @if($stream->agenda)
                    <div class="stream-agenda">
                        <h4>📋 Agenda</h4>
                        <ul>
                            @foreach(json_decode($stream->agenda, true) ?? [] as $item)
                                <li>{{ $item }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                
                @if($stream->resources)
                    <div class="stream-resources">
                        <h4>📎 Resources</h4>
                        <div class="resource-links">
                            @foreach(json_decode($stream->resources, true) ?? [] as $resource)
                                <a href="{{ $resource['url'] }}" target="_blank" class="resource-link">
                                    <span class="resource-icon">🔗</span>
                                    <span class="resource-name">{{ $resource['name'] }}</span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Chat Sidebar -->
        <div class="chat-sidebar">
            <div class="chat-header">
                <h3>Live Chat</h3>
                <div class="chat-stats">
                    <span class="participant-count">{{ $stream->participants()->count() }} participants</span>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                @foreach($stream->chatMessages()->latest()->take(50)->get()->reverse() as $message)
                    <div class="chat-message">
                        <div class="message-header">
                            <span class="username">{{ $message->user->name }}</span>
                            @if($message->user->id === $stream->instructor_id)
                                <span class="instructor-badge">👑 Instructor</span>
                            @endif
                            <span class="timestamp">{{ $message->created_at->format('g:i A') }}</span>
                        </div>
                        <div class="message-content">{{ $message->message }}</div>
                    </div>
                @endforeach
            </div>
            
            <div class="chat-input">
                <form id="chatForm" onsubmit="sendMessage(event)">
                    <div class="input-group">
                        <input type="text" 
                               id="messageInput" 
                               placeholder="Type your message..." 
                               maxlength="500"
                               required>
                        <button type="submit" class="send-btn">
                            <span>📤</span>
                        </button>
                    </div>
                </form>
                
                <div class="chat-actions">
                    <button type="button" class="action-btn" onclick="toggleEmojis()">
                        😀
                    </button>
                    <button type="button" class="action-btn" onclick="raiseHand()">
                        ✋
                    </button>
                    <button type="button" class="action-btn" onclick="toggleReactions()">
                        ❤️
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Participants Panel -->
    <div class="participants-panel" id="participantsPanel" style="display: none;">
        <div class="panel-header">
            <h3>Participants ({{ $stream->participants()->count() }})</h3>
            <button type="button" class="close-btn" onclick="closeParticipants()">✕</button>
        </div>
        
        <div class="participants-list">
            @foreach($stream->participants as $participant)
                <div class="participant-item">
                    <div class="participant-avatar">
                        @if($participant->avatar)
                            <img src="{{ Storage::url($participant->avatar) }}" alt="{{ $participant->name }}">
                        @else
                            <div class="avatar-placeholder">
                                {{ strtoupper(substr($participant->name, 0, 2)) }}
                            </div>
                        @endif
                    </div>
                    <div class="participant-info">
                        <span class="participant-name">{{ $participant->name }}</span>
                        @if($participant->id === $stream->instructor_id)
                            <span class="role-badge instructor">👑 Instructor</span>
                        @elseif($participant->hasRole('admin'))
                            <span class="role-badge admin">🛡️ Admin</span>
                        @endif
                    </div>
                    <div class="participant-status">
                        <span class="status-indicator online"></span>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Stream Analytics (Instructor Only) -->
    @if(Auth::user()->id === $stream->instructor_id)
        <div class="stream-analytics">
            <div class="analytics-header">
                <h3>📊 Stream Analytics</h3>
                <button type="button" class="toggle-btn" onclick="toggleAnalytics()">
                    <span id="analyticsToggle">▼</span>
                </button>
            </div>
            
            <div class="analytics-content" id="analyticsContent" style="display: none;">
                <div class="analytics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">👥</div>
                        <div class="metric-value">{{ $stream->peak_viewers ?? 0 }}</div>
                        <div class="metric-label">Peak Viewers</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-value">{{ $stream->average_watch_time ?? '0m' }}</div>
                        <div class="metric-label">Avg Watch Time</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">💬</div>
                        <div class="metric-value">{{ $stream->chatMessages()->count() }}</div>
                        <div class="metric-label">Chat Messages</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">👍</div>
                        <div class="metric-value">{{ $stream->reactions()->where('type', 'like')->count() }}</div>
                        <div class="metric-label">Likes</div>
                    </div>
                </div>
                
                <div class="viewer-chart">
                    <canvas id="viewerChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.live-stream-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
}

.stream-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stream-status {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

.live-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.live-indicator.active {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    animation: pulse 2s infinite;
}

.live-indicator.offline {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.viewer-count {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.stream-title {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stream-meta {
    display: flex;
    gap: 1rem;
    color: #a0a0a0;
    font-size: 0.875rem;
}

.stream-actions {
    display: flex;
    gap: 1rem;
}

.stream-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 1rem;
    margin-bottom: 2rem;
}

.video-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.video-container {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
}

.video-player {
    position: relative;
    width: 100%;
    height: 100%;
}

#liveVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-player:hover .video-controls {
    opacity: 1;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.volume-slider input {
    width: 80px;
}

.stream-time {
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

.stream-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
    right: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.overlay-stats {
    display: flex;
    gap: 0.5rem;
}

.live-badge, .viewer-badge {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.stream-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1f2937, #111827);
}

.placeholder-content {
    text-align: center;
    color: white;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.placeholder-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.placeholder-content p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

.countdown-timer {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.countdown-item {
    text-align: center;
}

.countdown-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
}

.countdown-label {
    font-size: 0.875rem;
    color: #a0a0a0;
}

.stream-description {
    padding: 2rem;
}

.stream-description h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stream-description p {
    color: #a0a0a0;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.stream-agenda, .stream-resources {
    margin-bottom: 1.5rem;
}

.stream-agenda h4, .stream-resources h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.stream-agenda ul {
    list-style: none;
    padding: 0;
}

.stream-agenda li {
    color: #a0a0a0;
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.stream-agenda li::before {
    content: "•";
    color: #3b82f6;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.resource-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.resource-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    color: #3b82f6;
    text-decoration: none;
    transition: all 0.3s ease;
}

.resource-link:hover {
    background: rgba(59, 130, 246, 0.1);
}

.chat-sidebar {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    height: fit-content;
    max-height: 600px;
}

.chat-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.participant-count {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.chat-message {
    margin-bottom: 1rem;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.username {
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
}

.instructor-badge {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.timestamp {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-left: auto;
}

.message-content {
    color: #ffffff;
    font-size: 0.875rem;
    line-height: 1.4;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.input-group input {
    flex: 1;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.input-group input:focus {
    outline: none;
    border-color: #3b82f6;
}

.send-btn {
    padding: 0.75rem;
    background: #3b82f6;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background: #1d4ed8;
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.stream-analytics {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.analytics-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    color: #ffffff;
    cursor: pointer;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.metric-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.metric-value {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .stream-layout {
        grid-template-columns: 1fr;
    }
    
    .stream-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .stream-actions {
        justify-content: center;
    }
    
    .chat-sidebar {
        order: -1;
        max-height: 300px;
    }
}
</style>

<script>
// WebRTC and streaming functionality
let localStream = null;
let peerConnection = null;
let socket = null;

// Initialize stream
document.addEventListener('DOMContentLoaded', function() {
    initializeStream();
    initializeChat();
    initializeCountdown();
});

function initializeStream() {
    // WebRTC initialization would go here
    console.log('Initializing stream...');
}

function initializeChat() {
    // WebSocket chat initialization
    console.log('Initializing chat...');
}

function initializeCountdown() {
    const countdownElement = document.querySelector('.countdown-timer');
    if (countdownElement) {
        const targetDate = new Date(countdownElement.dataset.target);
        
        setInterval(() => {
            const now = new Date().getTime();
            const distance = targetDate.getTime() - now;
            
            if (distance > 0) {
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('days').textContent = days.toString().padStart(2, '0');
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            }
        }, 1000);
    }
}

function toggleStream() {
    // Implementation for starting/stopping stream
    console.log('Toggle stream');
}

function sendMessage(event) {
    event.preventDefault();
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (message) {
        // Send message via WebSocket
        console.log('Sending message:', message);
        input.value = '';
    }
}

function toggleAnalytics() {
    const content = document.getElementById('analyticsContent');
    const toggle = document.getElementById('analyticsToggle');
    
    if (content.style.display === 'none') {
        content.style.display = 'block';
        toggle.textContent = '▲';
    } else {
        content.style.display = 'none';
        toggle.textContent = '▼';
    }
}

// Video controls
function togglePlay() {
    const video = document.getElementById('liveVideo');
    const icon = document.getElementById('playIcon');
    
    if (video.paused) {
        video.play();
        icon.textContent = '⏸️';
    } else {
        video.pause();
        icon.textContent = '▶️';
    }
}

function toggleMute() {
    const video = document.getElementById('liveVideo');
    const icon = document.getElementById('muteIcon');
    
    video.muted = !video.muted;
    icon.textContent = video.muted ? '🔇' : '🔊';
}

function toggleFullscreen() {
    const player = document.getElementById('videoPlayer');
    
    if (!document.fullscreenElement) {
        player.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

// Chat actions
function raiseHand() {
    console.log('Raise hand');
    // Implementation for raising hand
}

function toggleReactions() {
    console.log('Toggle reactions');
    // Implementation for reactions
}

function shareStream() {
    if (navigator.share) {
        navigator.share({
            title: document.querySelector('.stream-title').textContent,
            url: window.location.href
        });
    } else {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href);
        alert('Stream link copied to clipboard!');
    }
}
</script>
@endsection
