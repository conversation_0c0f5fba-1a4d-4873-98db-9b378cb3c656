// Courses JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeCourses();
});

function initializeCourses() {
    // Setup course card interactions
    setupCourseCards();
    
    // Setup filters
    setupFilters();
    
    // Setup search
    setupSearch();
    
    console.log('Courses page initialized');
}

function setupCourseCards() {
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        // Track course views
        const viewButton = card.querySelector('.btn');
        if (viewButton) {
            viewButton.addEventListener('click', function() {
                const courseTitle = card.querySelector('.course-title a').textContent;
                trackEvent('course_viewed', { course: courseTitle });
            });
        }
    });
}

function setupFilters() {
    const filterForm = document.querySelector('.filters-form');
    const filterSelects = document.querySelectorAll('.filter-select');
    
    if (filterForm) {
        // Auto-submit on filter change
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
        
        // Clear filters functionality
        const clearButton = filterForm.querySelector('.btn-secondary');
        if (clearButton) {
            clearButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Clear all form fields
                const searchInput = filterForm.querySelector('.search-input');
                if (searchInput) searchInput.value = '';
                
                filterSelects.forEach(select => {
                    select.selectedIndex = 0;
                });
                
                // Submit form
                filterForm.submit();
            });
        }
    }
}

function setupSearch() {
    const searchInput = document.querySelector('.search-input');
    
    if (searchInput) {
        // Add search icon
        const searchIcon = document.createElement('span');
        searchIcon.innerHTML = '🔍';
        searchIcon.className = 'search-icon';
        searchInput.parentElement.style.position = 'relative';
        searchInput.parentElement.appendChild(searchIcon);
        
        // Debounced search
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
        
        // Search on enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
        });
    }
}

// Course enrollment functionality
function enrollInCourse(courseId, isAuthenticated) {
    if (!isAuthenticated) {
        window.location.href = '/login';
        return;
    }
    
    const enrollButton = document.querySelector(`[data-course-id="${courseId}"] .enroll-btn`);
    if (enrollButton) {
        enrollButton.textContent = 'Enrolling...';
        enrollButton.disabled = true;
        
        fetch(`/courses/${courseId}/enroll`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                enrollButton.textContent = '✅ Enrolled';
                enrollButton.classList.remove('btn-primary');
                enrollButton.classList.add('btn-success');
                
                showNotification('Successfully enrolled in course!', 'success');
                
                // Redirect to course page after a delay
                setTimeout(() => {
                    window.location.href = `/courses/${courseId}`;
                }, 1500);
            } else {
                throw new Error(data.message || 'Enrollment failed');
            }
        })
        .catch(error => {
            console.error('Enrollment error:', error);
            enrollButton.textContent = 'Enroll Now';
            enrollButton.disabled = false;
            showNotification('Enrollment failed. Please try again.', 'error');
        });
    }
}

// Course rating functionality
function rateCourse(courseId, rating) {
    fetch(`/courses/${courseId}/rate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ rating: rating })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCourseRating(courseId, data.newRating, data.ratingCount);
            showNotification('Thank you for rating this course!', 'success');
        }
    })
    .catch(error => {
        console.error('Rating error:', error);
        showNotification('Failed to submit rating. Please try again.', 'error');
    });
}

function updateCourseRating(courseId, newRating, ratingCount) {
    const ratingElement = document.querySelector(`[data-course-id="${courseId}"] .course-rating`);
    if (ratingElement) {
        ratingElement.textContent = `⭐ ${newRating} (${ratingCount} reviews)`;
    }
}

// Course comparison functionality
let comparisonList = [];

function addToComparison(courseId, courseTitle) {
    if (comparisonList.length >= 3) {
        showNotification('You can only compare up to 3 courses at a time.', 'warning');
        return;
    }
    
    if (comparisonList.includes(courseId)) {
        showNotification('Course is already in comparison list.', 'info');
        return;
    }
    
    comparisonList.push(courseId);
    updateComparisonUI();
    showNotification(`${courseTitle} added to comparison.`, 'success');
}

function removeFromComparison(courseId) {
    comparisonList = comparisonList.filter(id => id !== courseId);
    updateComparisonUI();
}

function updateComparisonUI() {
    const comparisonButton = document.querySelector('.comparison-button');
    if (comparisonButton) {
        if (comparisonList.length > 0) {
            comparisonButton.style.display = 'block';
            comparisonButton.textContent = `Compare Courses (${comparisonList.length})`;
        } else {
            comparisonButton.style.display = 'none';
        }
    }
}

function showComparison() {
    if (comparisonList.length < 2) {
        showNotification('Please select at least 2 courses to compare.', 'warning');
        return;
    }
    
    const comparisonUrl = `/courses/compare?courses=${comparisonList.join(',')}`;
    window.open(comparisonUrl, '_blank');
}

// Course wishlist functionality
function toggleWishlist(courseId, courseTitle) {
    const wishlistButton = document.querySelector(`[data-course-id="${courseId}"] .wishlist-btn`);
    
    fetch(`/courses/${courseId}/wishlist`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.added) {
                wishlistButton.innerHTML = '❤️';
                wishlistButton.classList.add('active');
                showNotification(`${courseTitle} added to wishlist.`, 'success');
            } else {
                wishlistButton.innerHTML = '🤍';
                wishlistButton.classList.remove('active');
                showNotification(`${courseTitle} removed from wishlist.`, 'info');
            }
        }
    })
    .catch(error => {
        console.error('Wishlist error:', error);
        showNotification('Failed to update wishlist. Please try again.', 'error');
    });
}

// Utility functions
function trackEvent(eventName, properties = {}) {
    console.log('Event tracked:', eventName, properties);
    
    // Integration with analytics services
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Add search icon styles
const searchStyles = document.createElement('style');
searchStyles.textContent = `
    .search-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #666666;
        pointer-events: none;
    }
    
    .search-input {
        padding-right: 3rem !important;
    }
    
    .comparison-button {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        background: #3b82f6;
        color: white;
        border: none;
        padding: 1rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        display: none;
        z-index: 1000;
    }
    
    .comparison-button:hover {
        background: #2563eb;
        transform: translateY(-2px);
    }
    
    .wishlist-btn {
        background: none;
        border: none;
        font-size: 1.25rem;
        cursor: pointer;
        transition: transform 0.3s ease;
    }
    
    .wishlist-btn:hover {
        transform: scale(1.2);
    }
    
    .wishlist-btn.active {
        animation: heartbeat 0.6s ease-in-out;
    }
    
    @keyframes heartbeat {
        0% { transform: scale(1); }
        50% { transform: scale(1.3); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(searchStyles);
