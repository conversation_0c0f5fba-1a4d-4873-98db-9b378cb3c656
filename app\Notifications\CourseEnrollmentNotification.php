<?php

namespace App\Notifications;

use App\Models\Course;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CourseEnrollmentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $course;

    /**
     * Create a new notification instance.
     */
    public function __construct(Course $course)
    {
        $this->course = $course;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Welcome to ' . $this->course->title . '!')
            ->greeting('Congratulations, ' . $notifiable->name . '!')
            ->line('You have successfully enrolled in the course: ' . $this->course->title)
            ->line('Instructor: ' . $this->course->instructor->name)
            ->line('Course Duration: ' . $this->course->duration . ' minutes')
            ->line('You can now access all course materials and start learning immediately.')
            ->action('Start Learning', route('courses.show', $this->course))
            ->line('If you have any questions, feel free to reach out to your instructor or our support team.')
            ->line('Welcome to The Real World community!')
            ->salutation('Best regards, The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'type' => 'course_enrollment',
            'course_id' => $this->course->id,
            'course_title' => $this->course->title,
            'instructor_name' => $this->course->instructor->name,
            'message' => 'You have successfully enrolled in ' . $this->course->title,
            'action_url' => route('courses.show', $this->course),
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'course_enrollment',
            'course_id' => $this->course->id,
            'course_title' => $this->course->title,
            'instructor_name' => $this->course->instructor->name,
            'message' => 'You have successfully enrolled in ' . $this->course->title,
            'action_url' => route('courses.show', $this->course),
        ];
    }
}
