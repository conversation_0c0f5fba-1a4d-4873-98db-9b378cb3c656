<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the notification center.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get all notifications (read and unread)
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        
        // Get unread count
        $unreadCount = $user->unreadNotifications()->count();
        
        return view('notifications.index', compact('notifications', 'unreadCount'));
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead($id)
    {
        $user = Auth::user();
        $notification = $user->notifications()->findOrFail($id);
        
        $notification->markAsRead();
        
        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        $user->unreadNotifications->markAsRead();
        
        return response()->json(['success' => true]);
    }

    /**
     * Delete a notification.
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $notification = $user->notifications()->findOrFail($id);
        
        $notification->delete();
        
        return response()->json(['success' => true]);
    }

    /**
     * Get unread notifications count (AJAX).
     */
    public function getUnreadCount()
    {
        $count = Auth::user()->unreadNotifications()->count();
        
        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications (AJAX).
     */
    public function getRecent()
    {
        $notifications = Auth::user()->notifications()
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();
        
        return response()->json([
            'notifications' => $notifications->map(function($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at->diffForHumans(),
                    'icon' => $this->getNotificationIcon($notification->type),
                    'url' => $this->getNotificationUrl($notification),
                ];
            })
        ]);
    }

    /**
     * Get notification icon based on type.
     */
    private function getNotificationIcon($type)
    {
        $icons = [
            'App\Notifications\CourseEnrolled' => '📚',
            'App\Notifications\NewMessage' => '💬',
            'App\Notifications\PaymentReceived' => '💰',
            'App\Notifications\CourseCompleted' => '🎉',
            'App\Notifications\LiveStreamStarted' => '🎥',
            'App\Notifications\CommunityInvite' => '🏘️',
            'App\Notifications\SystemUpdate' => '🔔',
            'default' => '📢',
        ];

        return $icons[$type] ?? $icons['default'];
    }

    /**
     * Get notification URL based on type and data.
     */
    private function getNotificationUrl($notification)
    {
        $data = $notification->data;
        
        switch ($notification->type) {
            case 'App\Notifications\CourseEnrolled':
                return route('courses.show', $data['course_id'] ?? '#');
                
            case 'App\Notifications\NewMessage':
                return route('chat.show', $data['chat_room_id'] ?? '#');
                
            case 'App\Notifications\PaymentReceived':
                return route('instructor.earnings');
                
            case 'App\Notifications\CourseCompleted':
                return route('courses.show', $data['course_id'] ?? '#');
                
            case 'App\Notifications\LiveStreamStarted':
                return route('streams.show', $data['stream_id'] ?? '#');
                
            case 'App\Notifications\CommunityInvite':
                return route('communities.show', $data['community_id'] ?? '#');
                
            default:
                return route('notifications.index');
        }
    }
}
