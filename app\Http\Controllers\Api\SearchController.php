<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\User;
use App\Models\Community;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    /**
     * Get search suggestions.
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json(['suggestions' => []]);
        }

        $suggestions = collect();

        // Course suggestions
        $courses = Course::where('title', 'like', "%{$query}%")
            ->where('status', 'published')
            ->take(3)
            ->get(['id', 'title']);

        foreach ($courses as $course) {
            $suggestions->push([
                'text' => $course->title,
                'type' => 'Course',
                'icon' => '📚',
                'url' => route('courses.show', $course),
            ]);
        }

        // Instructor suggestions
        $instructors = User::role('instructor')
            ->where('name', 'like', "%{$query}%")
            ->take(2)
            ->get(['id', 'name']);

        foreach ($instructors as $instructor) {
            $suggestions->push([
                'text' => $instructor->name,
                'type' => 'Instructor',
                'icon' => '👨‍🏫',
                'url' => route('instructors.show', $instructor),
            ]);
        }

        // Community suggestions
        $communities = Community::where('name', 'like', "%{$query}%")
            ->where('is_public', true)
            ->take(2)
            ->get(['id', 'name']);

        foreach ($communities as $community) {
            $suggestions->push([
                'text' => $community->name,
                'type' => 'Community',
                'icon' => '🏘️',
                'url' => route('communities.show', $community),
            ]);
        }

        // Lesson suggestions
        $lessons = Lesson::where('title', 'like', "%{$query}%")
            ->whereHas('course', function($q) {
                $q->where('status', 'published');
            })
            ->take(2)
            ->get(['id', 'title', 'course_id']);

        foreach ($lessons as $lesson) {
            $suggestions->push([
                'text' => $lesson->title,
                'type' => 'Lesson',
                'icon' => '📖',
                'url' => route('lessons.show', [$lesson->course_id, $lesson]),
            ]);
        }

        return response()->json([
            'suggestions' => $suggestions->take(8)->values()
        ]);
    }

    /**
     * Perform advanced search.
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', []);
        $category = $request->get('category');
        $difficulty = $request->get('difficulty');
        $priceMin = $request->get('price_min');
        $priceMax = $request->get('price_max');
        $duration = $request->get('duration', []);
        $rating = $request->get('rating');
        $sort = $request->get('sort', 'relevance');

        $results = collect();

        // Search courses if requested
        if (empty($type) || in_array('courses', $type)) {
            $courseQuery = Course::where('status', 'published');

            if ($query) {
                $courseQuery->where(function($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('short_description', 'like', "%{$query}%");
                });
            }

            if ($category) {
                $courseQuery->where('category_id', $category);
            }

            if ($difficulty) {
                $courseQuery->where('difficulty_level', $difficulty);
            }

            if ($priceMin !== null) {
                $courseQuery->where('price', '>=', $priceMin);
            }

            if ($priceMax !== null) {
                $courseQuery->where('price', '<=', $priceMax);
            }

            if (!empty($duration)) {
                $courseQuery->where(function($q) use ($duration) {
                    foreach ($duration as $d) {
                        switch ($d) {
                            case '0-2':
                                $q->orWhere('duration_hours', '<=', 2);
                                break;
                            case '3-6':
                                $q->orWhereBetween('duration_hours', [3, 6]);
                                break;
                            case '7-17':
                                $q->orWhereBetween('duration_hours', [7, 17]);
                                break;
                            case '18+':
                                $q->orWhere('duration_hours', '>=', 18);
                                break;
                        }
                    }
                });
            }

            if ($rating) {
                $courseQuery->where('rating', '>=', $rating);
            }

            // Apply sorting
            switch ($sort) {
                case 'price_low':
                    $courseQuery->orderBy('price', 'asc');
                    break;
                case 'price_high':
                    $courseQuery->orderBy('price', 'desc');
                    break;
                case 'rating':
                    $courseQuery->orderBy('rating', 'desc');
                    break;
                case 'newest':
                    $courseQuery->orderBy('created_at', 'desc');
                    break;
                case 'popular':
                    $courseQuery->withCount('enrollments')->orderBy('enrollments_count', 'desc');
                    break;
                default: // relevance
                    if ($query) {
                        $courseQuery->orderByRaw("
                            CASE 
                                WHEN title LIKE ? THEN 1
                                WHEN short_description LIKE ? THEN 2
                                WHEN description LIKE ? THEN 3
                                ELSE 4
                            END
                        ", ["%{$query}%", "%{$query}%", "%{$query}%"]);
                    } else {
                        $courseQuery->orderBy('created_at', 'desc');
                    }
                    break;
            }

            $courses = $courseQuery->with(['instructor', 'category'])
                ->paginate(12);

            foreach ($courses as $course) {
                $results->push([
                    'type' => 'course',
                    'id' => $course->id,
                    'title' => $course->title,
                    'description' => $course->short_description,
                    'thumbnail' => $course->thumbnail,
                    'instructor' => $course->instructor->name,
                    'category' => $course->category->name,
                    'price' => $course->price,
                    'rating' => $course->rating,
                    'students' => $course->enrollments_count ?? 0,
                    'url' => route('courses.show', $course),
                ]);
            }
        }

        // Search instructors if requested
        if (empty($type) || in_array('instructors', $type)) {
            $instructorQuery = User::role('instructor');

            if ($query) {
                $instructorQuery->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('bio', 'like', "%{$query}%")
                      ->orWhere('expertise', 'like', "%{$query}%");
                });
            }

            $instructors = $instructorQuery->withCount('courses')
                ->take(6)
                ->get();

            foreach ($instructors as $instructor) {
                $results->push([
                    'type' => 'instructor',
                    'id' => $instructor->id,
                    'name' => $instructor->name,
                    'bio' => $instructor->bio,
                    'avatar' => $instructor->avatar,
                    'courses_count' => $instructor->courses_count,
                    'rating' => $instructor->average_rating ?? 0,
                    'url' => route('instructors.show', $instructor),
                ]);
            }
        }

        // Search communities if requested
        if (empty($type) || in_array('communities', $type)) {
            $communityQuery = Community::where('is_public', true);

            if ($query) {
                $communityQuery->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                });
            }

            $communities = $communityQuery->withCount('members')
                ->take(6)
                ->get();

            foreach ($communities as $community) {
                $results->push([
                    'type' => 'community',
                    'id' => $community->id,
                    'name' => $community->name,
                    'description' => $community->description,
                    'image' => $community->image,
                    'members_count' => $community->members_count,
                    'url' => route('communities.show', $community),
                ]);
            }
        }

        return response()->json([
            'results' => $results->values(),
            'total' => $results->count(),
            'query' => $query,
            'filters' => [
                'type' => $type,
                'category' => $category,
                'difficulty' => $difficulty,
                'price_range' => [$priceMin, $priceMax],
                'duration' => $duration,
                'rating' => $rating,
                'sort' => $sort,
            ]
        ]);
    }

    /**
     * Get popular searches.
     */
    public function popular()
    {
        // This would typically come from a search analytics table
        // For now, return some static popular searches
        $popularSearches = [
            'Web Development',
            'Data Science',
            'Machine Learning',
            'Digital Marketing',
            'Graphic Design',
            'Photography',
            'Business Strategy',
            'Personal Development',
        ];

        return response()->json([
            'popular_searches' => $popularSearches
        ]);
    }

    /**
     * Get search filters data.
     */
    public function filters()
    {
        $categories = DB::table('course_categories')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        $difficulties = [
            'beginner' => 'Beginner',
            'intermediate' => 'Intermediate',
            'advanced' => 'Advanced',
            'expert' => 'Expert',
        ];

        $durations = [
            '0-2' => '0-2 hours',
            '3-6' => '3-6 hours',
            '7-17' => '7-17 hours',
            '18+' => '18+ hours',
        ];

        $priceRanges = [
            ['min' => 0, 'max' => 25, 'label' => 'Free - $25'],
            ['min' => 25, 'max' => 50, 'label' => '$25 - $50'],
            ['min' => 50, 'max' => 100, 'label' => '$50 - $100'],
            ['min' => 100, 'max' => 200, 'label' => '$100 - $200'],
            ['min' => 200, 'max' => null, 'label' => '$200+'],
        ];

        return response()->json([
            'categories' => $categories,
            'difficulties' => $difficulties,
            'durations' => $durations,
            'price_ranges' => $priceRanges,
        ]);
    }
}
