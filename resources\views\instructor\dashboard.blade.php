@extends('layouts.app')

@section('title', 'Instructor Dashboard')

@section('content')
<div class="container">
    <div class="instructor-dashboard">
        <!-- Header -->
        <div class="dashboard-header">
            <div class="header-content">
                <div class="instructor-info">
                    <h1>Welcome back, {{ Auth::user()->name }}! 👋</h1>
                    <p>Manage your courses and track your teaching success</p>
                </div>
                <div class="header-actions">
                    <a href="{{ route('courses.create') }}" class="btn btn-primary">
                        ➕ Create New Course
                    </a>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-overview">
            <div class="metric-card students">
                <div class="metric-icon">👥</div>
                <div class="metric-content">
                    <h3>Total Students</h3>
                    <div class="metric-value">{{ $totalStudents }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📈</span>
                        <span class="change-text">+{{ $newStudentsThisMonth }} this month</span>
                    </div>
                </div>
            </div>

            <div class="metric-card revenue">
                <div class="metric-icon">💰</div>
                <div class="metric-content">
                    <h3>Total Revenue</h3>
                    <div class="metric-value">${{ number_format($totalRevenue, 2) }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📈</span>
                        <span class="change-text">${{ number_format($revenueThisMonth, 2) }} this month</span>
                    </div>
                </div>
            </div>

            <div class="metric-card courses">
                <div class="metric-icon">📚</div>
                <div class="metric-content">
                    <h3>Active Courses</h3>
                    <div class="metric-value">{{ $activeCourses }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📊</span>
                        <span class="change-text">{{ $totalCourses }} total courses</span>
                    </div>
                </div>
            </div>

            <div class="metric-card rating">
                <div class="metric-icon">⭐</div>
                <div class="metric-content">
                    <h3>Average Rating</h3>
                    <div class="metric-value">{{ number_format($averageRating, 1) }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📊</span>
                        <span class="change-text">{{ $totalReviews }} reviews</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2>Quick Actions</h2>
            <div class="actions-grid">
                <a href="{{ route('courses.create') }}" class="action-card">
                    <div class="action-icon">➕</div>
                    <h3>Create Course</h3>
                    <p>Start building a new course</p>
                </a>
                
                <a href="{{ route('instructor.students') }}" class="action-card">
                    <div class="action-icon">👥</div>
                    <h3>Manage Students</h3>
                    <p>View and interact with your students</p>
                </a>
                
                <a href="{{ route('instructor.analytics') }}" class="action-card">
                    <div class="action-icon">📊</div>
                    <h3>View Analytics</h3>
                    <p>Track your teaching performance</p>
                </a>
                
                <a href="{{ route('instructor.earnings') }}" class="action-card">
                    <div class="action-icon">💰</div>
                    <h3>Earnings Report</h3>
                    <p>Monitor your revenue and payouts</p>
                </a>
            </div>
        </div>

        <!-- Course Management -->
        <div class="course-management">
            <div class="section-header">
                <h2>Your Courses</h2>
                <div class="section-actions">
                    <select class="filter-select" onchange="filterCourses(this.value)">
                        <option value="all">All Courses</option>
                        <option value="published">Published</option>
                        <option value="draft">Drafts</option>
                        <option value="archived">Archived</option>
                    </select>
                    <a href="{{ route('courses.index') }}" class="btn btn-secondary">
                        View All Courses
                    </a>
                </div>
            </div>

            <div class="courses-grid">
                @forelse($courses as $course)
                    <div class="course-card" data-status="{{ $course->status }}">
                        <div class="course-thumbnail">
                            @if($course->thumbnail)
                                <img src="{{ Storage::url($course->thumbnail) }}" alt="{{ $course->title }}">
                            @else
                                <div class="thumbnail-placeholder">
                                    <span class="placeholder-icon">📚</span>
                                </div>
                            @endif
                            
                            <div class="course-status">
                                <span class="status-badge status-{{ $course->status }}">
                                    @if($course->status === 'published')
                                        ✅ Published
                                    @elseif($course->status === 'draft')
                                        📝 Draft
                                    @else
                                        📦 Archived
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="course-content">
                            <h3 class="course-title">{{ $course->title }}</h3>
                            <p class="course-category">{{ $course->category->name }}</p>
                            
                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-icon">👥</span>
                                    <span class="stat-text">{{ $course->enrollments()->count() }} students</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">💰</span>
                                    <span class="stat-text">${{ number_format($course->price, 2) }}</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-text">{{ number_format($course->rating ?? 0, 1) }}</span>
                                </div>
                            </div>

                            <div class="course-progress">
                                <div class="progress-info">
                                    <span class="progress-label">Course Completion</span>
                                    <span class="progress-percentage">{{ number_format($course->completion_rate ?? 0, 1) }}%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ $course->completion_rate ?? 0 }}%"></div>
                                </div>
                            </div>

                            <div class="course-actions">
                                <a href="{{ route('courses.show', $course) }}" class="btn btn-sm btn-secondary">
                                    👁️ View
                                </a>
                                <a href="{{ route('courses.edit', $course) }}" class="btn btn-sm btn-primary">
                                    ✏️ Edit
                                </a>
                                <a href="{{ route('courses.lessons.index', $course) }}" class="btn btn-sm btn-outline">
                                    📚 Lessons
                                </a>
                                <a href="{{ route('course.stats', $course) }}" class="btn btn-sm btn-outline">
                                    📊 Analytics
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-courses">
                        <div class="empty-icon">📚</div>
                        <h3>No Courses Yet</h3>
                        <p>Create your first course and start sharing your knowledge with the world!</p>
                        <a href="{{ route('courses.create') }}" class="btn btn-primary">
                            ➕ Create Your First Course
                        </a>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <h2>Recent Activity</h2>
            <div class="activity-feed">
                @forelse($recentActivity as $activity)
                    <div class="activity-item">
                        <div class="activity-icon">
                            @if($activity->type === 'enrollment')
                                👥
                            @elseif($activity->type === 'review')
                                ⭐
                            @elseif($activity->type === 'completion')
                                🏆
                            @elseif($activity->type === 'question')
                                ❓
                            @else
                                📝
                            @endif
                        </div>
                        <div class="activity-content">
                            <p>{{ $activity->description }}</p>
                            <span class="activity-time">{{ $activity->created_at ? $activity->created_at->diffForHumans() : 'recently' }}</span>
                        </div>
                        @if($activity->actionable)
                            <div class="activity-action">
                                <a href="{{ $activity->action_url }}" class="btn btn-sm btn-outline">
                                    View
                                </a>
                            </div>
                        @endif
                    </div>
                @empty
                    <div class="no-activity">
                        <p>No recent activity to show.</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Teaching Tips -->
        <div class="teaching-tips">
            <h2>💡 Teaching Tips</h2>
            <div class="tips-carousel">
                <div class="tip-card active">
                    <div class="tip-icon">🎯</div>
                    <h3>Engage Your Students</h3>
                    <p>Use interactive elements like quizzes and assignments to keep students engaged throughout your course.</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">📊</div>
                    <h3>Track Progress</h3>
                    <p>Regularly check your course analytics to understand how students are progressing and where they might need help.</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">💬</div>
                    <h3>Respond to Questions</h3>
                    <p>Actively respond to student questions and comments to build a strong learning community.</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🔄</div>
                    <h3>Update Content</h3>
                    <p>Keep your courses fresh by regularly updating content based on student feedback and industry changes.</p>
                </div>
            </div>
            <div class="carousel-controls">
                <button class="carousel-btn prev" onclick="previousTip()">‹</button>
                <div class="carousel-dots">
                    <span class="dot active" onclick="showTip(0)"></span>
                    <span class="dot" onclick="showTip(1)"></span>
                    <span class="dot" onclick="showTip(2)"></span>
                    <span class="dot" onclick="showTip(3)"></span>
                </div>
                <button class="carousel-btn next" onclick="nextTip()">›</button>
            </div>
        </div>
    </div>
</div>

<style>
.instructor-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.instructor-info h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.instructor-info p {
    color: #a0a0a0;
    font-size: 1.125rem;
}

.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.metric-icon {
    font-size: 3rem;
}

.metric-content h3 {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.metric-value {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.change-text {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 500;
}

.quick-actions {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.quick-actions h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.action-card h3 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.action-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.course-management {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.course-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.course-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-icon {
    font-size: 4rem;
    color: white;
}

.course-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-published {
    background: rgba(34, 197, 94, 0.9);
    color: white;
}

.status-draft {
    background: rgba(245, 158, 11, 0.9);
    color: white;
}

.status-archived {
    background: rgba(107, 114, 128, 0.9);
    color: white;
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.course-category {
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.course-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-icon {
    font-size: 1rem;
}

.stat-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.course-progress {
    margin-bottom: 1.5rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.progress-percentage {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.course-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.course-actions .btn {
    font-size: 0.875rem;
    padding: 0.5rem;
}

.empty-courses {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-courses h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-courses p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

.recent-activity {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.recent-activity h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    color: #ffffff;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.teaching-tips {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.teaching-tips h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.tips-carousel {
    position: relative;
    height: 200px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.tip-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
}

.tip-card.active {
    opacity: 1;
    transform: translateX(0);
}

.tip-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.tip-card h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tip-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.carousel-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.25rem;
}

.carousel-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.carousel-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #3b82f6;
}

@media (max-width: 768px) {
    .instructor-dashboard {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .metrics-overview {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .course-actions {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
let currentTip = 0;
const totalTips = 4;

function showTip(index) {
    const tips = document.querySelectorAll('.tip-card');
    const dots = document.querySelectorAll('.dot');
    
    // Hide all tips
    tips.forEach(tip => tip.classList.remove('active'));
    dots.forEach(dot => dot.classList.remove('active'));
    
    // Show selected tip
    tips[index].classList.add('active');
    dots[index].classList.add('active');
    
    currentTip = index;
}

function nextTip() {
    currentTip = (currentTip + 1) % totalTips;
    showTip(currentTip);
}

function previousTip() {
    currentTip = (currentTip - 1 + totalTips) % totalTips;
    showTip(currentTip);
}

function filterCourses(status) {
    const cards = document.querySelectorAll('.course-card');
    
    cards.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Auto-rotate tips every 5 seconds
setInterval(nextTip, 5000);
</script>
@endsection
