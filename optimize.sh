#!/bin/bash

# The Real World LMS - Performance Optimization Script
# This script optimizes the application for production performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in production
check_environment() {
    if [ "$APP_ENV" != "production" ]; then
        warning "Not running in production environment. Some optimizations may not apply."
    fi
}

# Clear all caches
clear_caches() {
    log "Clearing all caches..."
    
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan event:clear
    
    # Clear OPcache if available
    if command -v php-fpm &> /dev/null; then
        sudo systemctl reload php8.2-fpm
    fi
    
    log "✅ Caches cleared"
}

# Optimize for production
optimize_laravel() {
    log "Optimizing Laravel for production..."
    
    # Cache configuration
    php artisan config:cache
    info "Configuration cached"
    
    # Cache routes
    php artisan route:cache
    info "Routes cached"
    
    # Cache views
    php artisan view:cache
    info "Views cached"
    
    # Cache events
    php artisan event:cache
    info "Events cached"
    
    # Optimize autoloader
    composer dump-autoload --optimize --no-dev
    info "Autoloader optimized"
    
    log "✅ Laravel optimization complete"
}

# Optimize database
optimize_database() {
    log "Optimizing database..."
    
    # Run migrations
    php artisan migrate --force
    
    # Optimize database tables
    php artisan db:optimize
    
    # Update database statistics
    mysql -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE -e "ANALYZE TABLE courses, users, course_enrollments, payments, communities, chat_rooms, messages;"
    
    log "✅ Database optimized"
}

# Optimize assets
optimize_assets() {
    log "Optimizing frontend assets..."
    
    # Install production dependencies
    npm ci --production
    
    # Build optimized assets
    npm run build
    
    # Compress CSS and JS files
    if command -v gzip &> /dev/null; then
        find public/build -name "*.css" -exec gzip -k {} \;
        find public/build -name "*.js" -exec gzip -k {} \;
        info "Assets compressed with gzip"
    fi
    
    log "✅ Assets optimized"
}

# Configure web server
optimize_webserver() {
    log "Optimizing web server configuration..."
    
    # Enable gzip compression in Nginx
    if [ -f "/etc/nginx/nginx.conf" ]; then
        if ! grep -q "gzip on" /etc/nginx/nginx.conf; then
            warning "Gzip compression not enabled in Nginx. Please enable it manually."
        fi
    fi
    
    # Set proper file permissions
    sudo chown -R www-data:www-data storage bootstrap/cache
    chmod -R 775 storage bootstrap/cache
    
    # Restart web server
    if systemctl is-active --quiet nginx; then
        sudo systemctl restart nginx
        info "Nginx restarted"
    fi
    
    if systemctl is-active --quiet apache2; then
        sudo systemctl restart apache2
        info "Apache restarted"
    fi
    
    log "✅ Web server optimized"
}

# Optimize PHP configuration
optimize_php() {
    log "Optimizing PHP configuration..."
    
    # Check OPcache status
    if php -m | grep -q "Zend OPcache"; then
        info "OPcache is enabled"
    else
        warning "OPcache is not enabled. Please enable it for better performance."
    fi
    
    # Check memory limit
    MEMORY_LIMIT=$(php -r "echo ini_get('memory_limit');")
    info "PHP memory limit: $MEMORY_LIMIT"
    
    if [ "$MEMORY_LIMIT" = "128M" ]; then
        warning "Consider increasing PHP memory limit to 256M or higher"
    fi
    
    # Restart PHP-FPM
    if systemctl is-active --quiet php8.2-fpm; then
        sudo systemctl restart php8.2-fpm
        info "PHP-FPM restarted"
    fi
    
    log "✅ PHP optimization complete"
}

# Setup Redis caching
setup_redis() {
    log "Setting up Redis caching..."
    
    if systemctl is-active --quiet redis; then
        # Test Redis connection
        if redis-cli ping | grep -q "PONG"; then
            info "Redis is running and accessible"
            
            # Clear Redis cache
            redis-cli FLUSHALL
            info "Redis cache cleared"
        else
            error "Redis is not responding"
        fi
    else
        warning "Redis is not running. Please start Redis for better performance."
    fi
    
    log "✅ Redis setup complete"
}

# Setup queue workers
setup_queues() {
    log "Setting up queue workers..."
    
    # Create systemd service for queue worker
    if [ ! -f "/etc/systemd/system/laravel-worker.service" ]; then
        cat > /tmp/laravel-worker.service << EOF
[Unit]
Description=Laravel queue worker
After=redis.service

[Service]
User=www-data
Group=www-data
Restart=always
ExecStart=/usr/bin/php $(pwd)/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
        
        sudo mv /tmp/laravel-worker.service /etc/systemd/system/
        sudo systemctl daemon-reload
        sudo systemctl enable laravel-worker
        sudo systemctl start laravel-worker
        
        info "Queue worker service created and started"
    else
        sudo systemctl restart laravel-worker
        info "Queue worker restarted"
    fi
    
    log "✅ Queue workers setup complete"
}

# Setup scheduled tasks
setup_scheduler() {
    log "Setting up task scheduler..."
    
    # Add Laravel scheduler to crontab
    CRON_ENTRY="* * * * * cd $(pwd) && php artisan schedule:run >> /dev/null 2>&1"
    
    if ! crontab -l 2>/dev/null | grep -q "artisan schedule:run"; then
        (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
        info "Laravel scheduler added to crontab"
    else
        info "Laravel scheduler already in crontab"
    fi
    
    log "✅ Scheduler setup complete"
}

# Monitor performance
monitor_performance() {
    log "Setting up performance monitoring..."
    
    # Create performance monitoring script
    cat > storage/logs/performance-monitor.sh << 'EOF'
#!/bin/bash
# Performance monitoring script

LOG_FILE="storage/logs/performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')

# Check disk usage
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')

# Check CPU load
CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

# Check MySQL connections
if command -v mysql &> /dev/null; then
    MYSQL_CONNECTIONS=$(mysql -u $DB_USERNAME -p$DB_PASSWORD -e "SHOW STATUS LIKE 'Threads_connected';" | tail -1 | awk '{print $2}')
else
    MYSQL_CONNECTIONS="N/A"
fi

# Log performance metrics
echo "$DATE,Memory:${MEMORY_USAGE}%,Disk:${DISK_USAGE}%,CPU:${CPU_LOAD},MySQL:${MYSQL_CONNECTIONS}" >> $LOG_FILE

# Alert if thresholds exceeded
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "HIGH MEMORY USAGE: ${MEMORY_USAGE}%" | logger -t "laravel-performance"
fi

if [ "$DISK_USAGE" -gt 90 ]; then
    echo "HIGH DISK USAGE: ${DISK_USAGE}%" | logger -t "laravel-performance"
fi
EOF
    
    chmod +x storage/logs/performance-monitor.sh
    
    # Add to crontab to run every 5 minutes
    MONITOR_CRON="*/5 * * * * $(pwd)/storage/logs/performance-monitor.sh"
    
    if ! crontab -l 2>/dev/null | grep -q "performance-monitor.sh"; then
        (crontab -l 2>/dev/null; echo "$MONITOR_CRON") | crontab -
        info "Performance monitoring added to crontab"
    fi
    
    log "✅ Performance monitoring setup complete"
}

# Generate performance report
generate_report() {
    log "Generating performance report..."
    
    REPORT_FILE="storage/logs/optimization-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $REPORT_FILE << EOF
The Real World LMS - Performance Optimization Report
Generated: $(date)

System Information:
==================
OS: $(uname -a)
PHP Version: $(php -r "echo PHP_VERSION;")
Laravel Version: $(php artisan --version)
MySQL Version: $(mysql --version)
Redis Version: $(redis-cli --version)
Nginx Version: $(nginx -v 2>&1)

Optimization Status:
===================
✓ Laravel caches optimized
✓ Database optimized
✓ Assets optimized
✓ Web server configured
✓ PHP optimized
✓ Redis caching enabled
✓ Queue workers running
✓ Task scheduler configured
✓ Performance monitoring active

Performance Metrics:
===================
Memory Usage: $(free | grep Mem | awk '{printf "%.2f%%", $3/$2 * 100.0}')
Disk Usage: $(df / | tail -1 | awk '{print $5}')
CPU Load: $(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

Recommendations:
===============
1. Monitor performance logs regularly
2. Review slow query logs
3. Consider CDN for static assets
4. Implement database query optimization
5. Set up automated backups
6. Configure SSL/HTTPS
7. Enable HTTP/2
8. Implement security headers

Next Steps:
===========
1. Test application performance
2. Run load testing
3. Monitor error logs
4. Set up alerting
5. Document optimization changes
EOF
    
    log "✅ Performance report generated: $REPORT_FILE"
}

# Main optimization function
main() {
    log "🚀 Starting The Real World LMS optimization..."
    
    check_environment
    clear_caches
    optimize_laravel
    optimize_database
    optimize_assets
    optimize_webserver
    optimize_php
    setup_redis
    setup_queues
    setup_scheduler
    monitor_performance
    generate_report
    
    log "🎉 Optimization completed successfully!"
    log "The Real World LMS is now optimized for production!"
    
    info "Next steps:"
    info "1. Test the application thoroughly"
    info "2. Monitor performance metrics"
    info "3. Set up SSL certificates"
    info "4. Configure backups"
    info "5. Set up monitoring alerts"
}

# Script usage
case "$1" in
    cache)
        clear_caches
        ;;
    laravel)
        optimize_laravel
        ;;
    database)
        optimize_database
        ;;
    assets)
        optimize_assets
        ;;
    webserver)
        optimize_webserver
        ;;
    php)
        optimize_php
        ;;
    redis)
        setup_redis
        ;;
    queues)
        setup_queues
        ;;
    scheduler)
        setup_scheduler
        ;;
    monitor)
        monitor_performance
        ;;
    report)
        generate_report
        ;;
    *)
        main
        ;;
esac
