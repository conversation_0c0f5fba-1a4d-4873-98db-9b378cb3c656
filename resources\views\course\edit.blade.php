@extends('layouts.app')

@section('title', 'Edit Course - ' . $course->title)

@section('content')
<div class="container">
    <div class="edit-course-container">
        <div class="page-header">
            <h1>Edit Course</h1>
            <p>Update your course information and settings</p>
            <div class="course-status">
                <span class="status-badge status-{{ $course->status }}">
                    @if($course->status === 'published')
                        ✅ Published
                    @elseif($course->status === 'draft')
                        📝 Draft
                    @else
                        📦 Archived
                    @endif
                </span>
            </div>
        </div>

        <form method="POST" action="{{ route('courses.update', $course) }}" enctype="multipart/form-data" class="edit-course-form">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="form-section">
                <h2>📚 Basic Information</h2>
                
                <div class="form-group">
                    <label for="title" class="form-label">Course Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="form-control @error('title') is-invalid @enderror" 
                           value="{{ old('title', $course->title) }}" 
                           required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="short_description" class="form-label">Short Description *</label>
                    <textarea id="short_description" 
                              name="short_description" 
                              class="form-control @error('short_description') is-invalid @enderror" 
                              rows="3" 
                              required>{{ old('short_description', $course->short_description) }}</textarea>
                    @error('short_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Full Description *</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control @error('description') is-invalid @enderror" 
                              rows="8" 
                              required>{{ old('description', $course->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="category_id" class="form-label">Category *</label>
                        <select id="category_id" name="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
                            <option value="">Select a category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $course->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="difficulty_level" class="form-label">Difficulty Level *</label>
                        <select id="difficulty_level" name="difficulty_level" class="form-control @error('difficulty_level') is-invalid @enderror" required>
                            <option value="">Select difficulty</option>
                            <option value="beginner" {{ old('difficulty_level', $course->difficulty_level) === 'beginner' ? 'selected' : '' }}>🟢 Beginner</option>
                            <option value="intermediate" {{ old('difficulty_level', $course->difficulty_level) === 'intermediate' ? 'selected' : '' }}>🟡 Intermediate</option>
                            <option value="advanced" {{ old('difficulty_level', $course->difficulty_level) === 'advanced' ? 'selected' : '' }}>🔴 Advanced</option>
                        </select>
                        @error('difficulty_level')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Course Media -->
            <div class="form-section">
                <h2>🎨 Course Media</h2>
                
                <div class="form-group">
                    <label for="thumbnail" class="form-label">Course Thumbnail</label>
                    <div class="file-upload-area" onclick="document.getElementById('thumbnail').click()">
                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" class="file-input" onchange="previewImage(this, 'thumbnail-preview')">
                        
                        @if($course->thumbnail)
                            <div class="current-image">
                                <img src="{{ Storage::url($course->thumbnail) }}" alt="Current thumbnail">
                                <div class="image-overlay">
                                    <span>Click to change thumbnail</span>
                                </div>
                            </div>
                        @else
                            <div class="upload-placeholder" id="thumbnail-placeholder">
                                <span class="upload-icon">🖼️</span>
                                <span class="upload-text">Click to upload course thumbnail</span>
                                <span class="upload-hint">Recommended: 1280x720px, JPG or PNG</span>
                            </div>
                        @endif
                        
                        <div class="image-preview" id="thumbnail-preview" style="display: none;">
                            <img src="" alt="Thumbnail preview">
                            <button type="button" class="remove-image" onclick="removeImage('thumbnail', 'thumbnail-preview')">✕</button>
                        </div>
                    </div>
                    @error('thumbnail')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Course Details -->
            <div class="form-section">
                <h2>⚙️ Course Details</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price" class="form-label">Price (USD) *</label>
                        <div class="input-group">
                            <span class="input-prefix">$</span>
                            <input type="number" 
                                   id="price" 
                                   name="price" 
                                   class="form-control @error('price') is-invalid @enderror" 
                                   value="{{ old('price', $course->price) }}" 
                                   min="0" 
                                   step="0.01"
                                   required>
                        </div>
                        @error('price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="duration_hours" class="form-label">Duration (Hours) *</label>
                        <input type="number" 
                               id="duration_hours" 
                               name="duration_hours" 
                               class="form-control @error('duration_hours') is-invalid @enderror" 
                               value="{{ old('duration_hours', $course->duration_hours) }}" 
                               min="1" 
                               required>
                        @error('duration_hours')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group">
                    <label for="requirements" class="form-label">Requirements</label>
                    <textarea id="requirements" 
                              name="requirements" 
                              class="form-control @error('requirements') is-invalid @enderror" 
                              rows="4">{{ old('requirements', $course->requirements) }}</textarea>
                    @error('requirements')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="what_you_will_learn" class="form-label">What Students Will Learn</label>
                    <textarea id="what_you_will_learn" 
                              name="what_you_will_learn" 
                              class="form-control @error('what_you_will_learn') is-invalid @enderror" 
                              rows="4">{{ old('what_you_will_learn', $course->what_you_will_learn) }}</textarea>
                    @error('what_you_will_learn')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Course Settings -->
            <div class="form-section">
                <h2>🔧 Course Settings</h2>
                
                <div class="settings-grid">
                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="is_featured" value="1" {{ old('is_featured', $course->is_featured) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">⭐ Featured Course</span>
                                <span class="toggle-desc">Display this course prominently on the homepage</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="allow_comments" value="1" {{ old('allow_comments', $course->allow_comments) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">💬 Allow Comments</span>
                                <span class="toggle-desc">Students can leave comments and reviews</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="certificate_enabled" value="1" {{ old('certificate_enabled', $course->certificate_enabled) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">🏆 Certificate of Completion</span>
                                <span class="toggle-desc">Award certificates when students complete the course</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="drip_content" value="1" {{ old('drip_content', $course->drip_content) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">⏰ Drip Content</span>
                                <span class="toggle-desc">Release lessons gradually over time</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="status" class="form-label">Course Status</label>
                    <select id="status" name="status" class="form-control">
                        <option value="draft" {{ $course->status === 'draft' ? 'selected' : '' }}>📝 Draft</option>
                        <option value="published" {{ $course->status === 'published' ? 'selected' : '' }}>✅ Published</option>
                        <option value="archived" {{ $course->status === 'archived' ? 'selected' : '' }}>📦 Archived</option>
                    </select>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="{{ route('courses.show', $course) }}" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    💾 Update Course
                </button>
            </div>
        </form>

        <!-- Course Statistics -->
        <div class="course-stats">
            <h3>📊 Course Statistics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <h4>{{ $course->enrollments()->count() }}</h4>
                        <p>Students Enrolled</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-info">
                        <h4>{{ number_format($course->rating ?? 0, 1) }}</h4>
                        <p>Average Rating</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                        <h4>${{ number_format($course->enrollments()->sum('amount_paid') ?? 0, 2) }}</h4>
                        <p>Total Revenue</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-info">
                        <h4>{{ $course->created_at->format('M j, Y') }}</h4>
                        <p>Created Date</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.edit-course-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.page-header h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.course-status {
    display: flex;
    justify-content: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-published {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-draft {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-archived {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.edit-course-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
}

.current-image {
    position: relative;
    display: inline-block;
}

.current-image img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.current-image:hover .image-overlay {
    opacity: 1;
}

.course-stats {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.course-stats h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-info h4 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

/* Reuse styles from create.blade.php for consistency */
.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-group {
    position: relative;
}

.input-prefix {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
    font-weight: 500;
    z-index: 1;
}

.input-group .form-control {
    padding-left: 2.5rem;
}

.file-upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.setting-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-content {
    display: flex;
    flex-direction: column;
}

.toggle-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toggle-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .edit-course-container {
        padding: 1rem;
    }
    
    .edit-course-form {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById(previewId);
            const currentImage = document.querySelector('.current-image');
            
            preview.querySelector('img').src = e.target.result;
            preview.style.display = 'block';
            
            if (currentImage) {
                currentImage.style.display = 'none';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeImage(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    const currentImage = document.querySelector('.current-image');
    
    input.value = '';
    preview.style.display = 'none';
    
    if (currentImage) {
        currentImage.style.display = 'block';
    }
}
</script>
@endsection
