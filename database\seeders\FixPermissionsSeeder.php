<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class FixPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔧 Fixing permissions and roles...');

        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Ensure all permissions exist
        $this->createMissingPermissions();

        // Ensure all roles exist
        $this->createMissingRoles();

        // Fix admin user permissions
        $this->fixAdminUser();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        $this->command->info('✅ Permissions and roles fixed successfully!');
    }

    private function createMissingPermissions()
    {
        $permissions = [
            // User management
            'view users', 'create users', 'edit users', 'delete users', 'ban users', 'unban users', 'impersonate users',

            // Course management
            'view courses', 'create courses', 'edit courses', 'delete courses', 'publish courses', 'unpublish courses',
            'approve courses', 'reject courses', 'view course analytics', 'manage course enrollments',

            // Lesson management
            'view lessons', 'create lessons', 'edit lessons', 'delete lessons', 'publish lessons', 'unpublish lessons', 'reorder lessons',

            // Community management
            'view communities', 'create communities', 'edit communities', 'delete communities', 'moderate communities',
            'pin posts', 'unpin posts', 'lock posts', 'unlock posts',

            // Chat management
            'view chats', 'create chats', 'edit chats', 'delete chats', 'moderate chats', 'mute users', 'unmute users', 'kick users',

            // Payment management
            'view payments', 'process payments', 'refund payments', 'view financial reports', 'manage payment methods', 'handle disputes',

            // Live streaming
            'create streams', 'manage streams', 'moderate streams', 'view stream analytics',

            // Notifications
            'send notifications', 'manage notification settings', 'view notification analytics',

            // Analytics & Reports
            'view analytics', 'view detailed analytics', 'export reports', 'view financial analytics', 'view user analytics',

            // Admin functions
            'access admin panel', 'manage settings', 'manage roles', 'manage permissions', 'view system logs',
            'manage system', 'backup system', 'restore system',

            // Content moderation
            'moderate content', 'approve content', 'reject content', 'flag content', 'review reports',

            // API access
            'access api', 'manage api keys', 'view api logs',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        $this->command->info('📋 Permissions created/verified');
    }

    private function createMissingRoles()
    {
        $roles = ['super-admin', 'admin', 'instructor', 'moderator', 'student'];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }

        $this->command->info('🎭 Roles created/verified');
    }

    private function fixAdminUser()
    {
        // Create or update admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'username' => 'superadmin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        // Ensure admin has super-admin role
        if (!$admin->hasRole('super-admin')) {
            $admin->assignRole('super-admin');
        }

        $this->command->info('👑 Admin user fixed: <EMAIL> / password');
    }

    private function assignPermissionsToRoles()
    {
        // Super Admin gets all permissions
        $superAdmin = Role::findByName('super-admin');
        $superAdmin->syncPermissions(Permission::all());

        // Admin gets most permissions
        $admin = Role::findByName('admin');
        $adminPermissions = Permission::whereNotIn('name', [
            'manage roles', 'manage permissions', 'manage system', 'backup system', 'restore system'
        ])->get();
        $admin->syncPermissions($adminPermissions);

        // Instructor permissions
        $instructor = Role::findByName('instructor');
        $instructorPermissions = [
            'view courses', 'create courses', 'edit courses', 'publish courses', 'unpublish courses',
            'view course analytics', 'manage course enrollments',
            'view lessons', 'create lessons', 'edit lessons', 'publish lessons', 'unpublish lessons', 'reorder lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
            'create streams', 'manage streams', 'view stream analytics',
            'view payments', 'view analytics', 'send notifications',
        ];
        $instructor->syncPermissions($instructorPermissions);

        // Moderator permissions
        $moderator = Role::findByName('moderator');
        $moderatorPermissions = [
            'view courses', 'view lessons', 'view users',
            'view communities', 'moderate communities', 'pin posts', 'unpin posts', 'lock posts', 'unlock posts',
            'view chats', 'moderate chats', 'mute users', 'unmute users', 'kick users',
            'moderate content', 'approve content', 'reject content', 'flag content', 'review reports',
            'moderate streams',
        ];
        $moderator->syncPermissions($moderatorPermissions);

        // Student permissions
        $student = Role::findByName('student');
        $studentPermissions = [
            'view courses', 'view lessons', 'view communities', 'view chats', 'create chats',
        ];
        $student->syncPermissions($studentPermissions);

        $this->command->info('🔑 Permissions assigned to roles');
    }
}
