@extends('layouts.app')

@section('title', 'Search Results')

@push('styles')
    <link href="{{ asset('assets/css/search.css') }}" rel="stylesheet">
@endpush

@section('content')
    <!-- Main Content -->
    <div class="search-main">
        <div class="container">
            <!-- Search Header -->
            <div class="search-header">
                <h1>Search Results</h1>
                @if($query)
                    <p class="search-query">Results for: <strong>"{{ $query }}"</strong></p>
                    <p class="search-count">{{ $total }} {{ Str::plural('result', $total) }} found</p>
                @endif
            </div>

            <!-- Search Form -->
            <div class="search-form-container">
                <form method="GET" action="{{ route('search.index') }}" class="search-form">
                    <div class="search-input-group">
                        <input type="text" name="q" value="{{ $query }}" 
                               placeholder="Search courses, communities, and more..." 
                               class="search-input" autofocus>
                        <button type="submit" class="search-button">
                            🔍 Search
                        </button>
                    </div>
                    
                    <div class="search-filters">
                        <label class="filter-option">
                            <input type="radio" name="type" value="all" {{ $type === 'all' ? 'checked' : '' }}>
                            All
                        </label>
                        <label class="filter-option">
                            <input type="radio" name="type" value="courses" {{ $type === 'courses' ? 'checked' : '' }}>
                            Courses
                        </label>
                        <label class="filter-option">
                            <input type="radio" name="type" value="communities" {{ $type === 'communities' ? 'checked' : '' }}>
                            Communities
                        </label>
                        @if(Auth::user()->hasRole(['admin', 'instructor']))
                            <label class="filter-option">
                                <input type="radio" name="type" value="users" {{ $type === 'users' ? 'checked' : '' }}>
                                Users
                            </label>
                        @endif
                    </div>
                </form>
            </div>

            <!-- Search Results -->
            <div class="search-results">
                @if($query && $results->count() > 0)
                    @foreach($results as $result)
                        <div class="search-result-item {{ $result['type'] }}">
                            <div class="result-content">
                                <div class="result-header">
                                    <div class="result-type">
                                        @switch($result['type'])
                                            @case('course')
                                                📚 Course
                                                @break
                                            @case('community')
                                                👥 Community
                                                @break
                                            @case('user')
                                                👤 User
                                                @break
                                            @default
                                                📄 Content
                                        @endswitch
                                    </div>
                                    <div class="result-date">
                                        {{ $result['created_at']->diffForHumans() }}
                                    </div>
                                </div>
                                
                                <h3 class="result-title">
                                    <a href="{{ $result['url'] }}">{{ $result['title'] }}</a>
                                </h3>
                                
                                <p class="result-description">{{ $result['description'] }}</p>
                                
                                <div class="result-meta">
                                    @if($result['type'] === 'course')
                                        <span class="meta-item">
                                            👨‍🏫 {{ $result['meta']['instructor'] }}
                                        </span>
                                        <span class="meta-item">
                                            📂 {{ $result['meta']['category'] }}
                                        </span>
                                        <span class="meta-item">
                                            💰 ${{ number_format($result['meta']['price'], 2) }}
                                        </span>
                                        <span class="meta-item">
                                            👥 {{ $result['meta']['students'] }} students
                                        </span>
                                    @elseif($result['type'] === 'community')
                                        <span class="meta-item">
                                            👤 Created by {{ $result['meta']['creator'] }}
                                        </span>
                                        <span class="meta-item">
                                            👥 {{ $result['meta']['members'] }} members
                                        </span>
                                        <span class="meta-item">
                                            💬 {{ $result['meta']['posts'] }} posts
                                        </span>
                                        <span class="meta-item">
                                            🏷️ {{ $result['meta']['type'] }}
                                        </span>
                                    @elseif($result['type'] === 'user')
                                        <span class="meta-item">
                                            🏷️ {{ $result['meta']['roles'] }}
                                        </span>
                                        <span class="meta-item">
                                            📊 {{ $result['meta']['status'] }}
                                        </span>
                                        <span class="meta-item">
                                            ✅ Verified: {{ $result['meta']['verified'] }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            @if($result['image'])
                                <div class="result-image">
                                    <img src="{{ Storage::url($result['image']) }}" alt="{{ $result['title'] }}">
                                </div>
                            @endif
                        </div>
                    @endforeach
                @elseif($query)
                    <div class="no-results">
                        <div class="no-results-icon">🔍</div>
                        <h3>No results found</h3>
                        <p>We couldn't find anything matching "<strong>{{ $query }}</strong>"</p>
                        <div class="search-suggestions">
                            <h4>Try:</h4>
                            <ul>
                                <li>Checking your spelling</li>
                                <li>Using different keywords</li>
                                <li>Searching for more general terms</li>
                                <li>Browsing our <a href="{{ route('courses.index') }}">course catalog</a></li>
                                <li>Exploring our <a href="{{ route('communities.index') }}">communities</a></li>
                            </ul>
                        </div>
                    </div>
                @else
                    <div class="search-welcome">
                        <div class="welcome-icon">🔍</div>
                        <h3>Search The Real World</h3>
                        <p>Find courses, communities, and content to help you escape the matrix.</p>
                        
                        <div class="popular-searches">
                            <h4>Popular Searches:</h4>
                            <div class="search-tags">
                                <a href="{{ route('search.index', ['q' => 'business']) }}" class="search-tag">Business</a>
                                <a href="{{ route('search.index', ['q' => 'crypto']) }}" class="search-tag">Cryptocurrency</a>
                                <a href="{{ route('search.index', ['q' => 'fitness']) }}" class="search-tag">Fitness</a>
                                <a href="{{ route('search.index', ['q' => 'mindset']) }}" class="search-tag">Mindset</a>
                                <a href="{{ route('search.index', ['q' => 'marketing']) }}" class="search-tag">Marketing</a>
                                <a href="{{ route('search.index', ['q' => 'investing']) }}" class="search-tag">Investing</a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Quick Links -->
            <div class="search-quick-links">
                <h4>Quick Links</h4>
                <div class="quick-links-grid">
                    <a href="{{ route('courses.index') }}" class="quick-link">
                        <span class="link-icon">📚</span>
                        <span class="link-text">Browse All Courses</span>
                    </a>
                    <a href="{{ route('communities.index') }}" class="quick-link">
                        <span class="link-icon">👥</span>
                        <span class="link-text">Join Communities</span>
                    </a>
                    <a href="{{ route('chat.index') }}" class="quick-link">
                        <span class="link-icon">💬</span>
                        <span class="link-text">Chat Rooms</span>
                    </a>
                    <a href="{{ route('dashboard') }}" class="quick-link">
                        <span class="link-icon">📊</span>
                        <span class="link-text">My Dashboard</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
        // Auto-complete functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        fetchAutoComplete(query);
                    }, 300);
                }
            });

            function fetchAutoComplete(query) {
                fetch(`/api/v1/search/autocomplete?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        showAutoComplete(data);
                    })
                    .catch(error => console.error('Autocomplete error:', error));
            }

            function showAutoComplete(suggestions) {
                // Implementation for showing autocomplete suggestions
                console.log('Autocomplete suggestions:', suggestions);
            }

            // Filter change handler
            document.querySelectorAll('input[name="type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (searchInput.value.trim()) {
                        this.form.submit();
                    }
                });
            });
        });
</script>
@endpush
