<?php

namespace App\Http\Controllers;

use App\Models\Stream;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class StreamController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of streams.
     */
    public function index()
    {
        $streams = Stream::with(['instructor', 'course'])
            ->where('status', 'live')
            ->orWhere('scheduled_at', '>', now())
            ->orderBy('scheduled_at')
            ->paginate(12);

        return view('streams.index', compact('streams'));
    }

    /**
     * Show the form for creating a new stream.
     */
    public function create()
    {
        $this->authorize('create streams');

        $courses = Course::where('instructor_id', Auth::id())
            ->where('status', 'published')
            ->get();

        return view('streams.create', compact('courses'));
    }

    /**
     * Store a newly created stream.
     */
    public function store(Request $request)
    {
        $this->authorize('create streams');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'course_id' => 'nullable|exists:courses,id',
            'scheduled_at' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_participants' => 'nullable|integer|min:1|max:1000',
            'is_public' => 'boolean',
            'allow_chat' => 'boolean',
            'allow_recording' => 'boolean',
        ]);

        $stream = Stream::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'instructor_id' => Auth::id(),
            'course_id' => $validated['course_id'] ?? null,
            'scheduled_at' => $validated['scheduled_at'],
            'duration_minutes' => $validated['duration_minutes'],
            'max_participants' => $validated['max_participants'] ?? 100,
            'is_public' => $validated['is_public'] ?? true,
            'allow_chat' => $validated['allow_chat'] ?? true,
            'allow_recording' => $validated['allow_recording'] ?? false,
            'stream_key' => Str::random(32),
            'status' => 'scheduled',
        ]);

        return redirect()->route('instructor.streams.show', $stream)
            ->with('success', 'Stream scheduled successfully!');
    }

    /**
     * Display the specified stream.
     */
    public function show(Stream $stream)
    {
        $stream->load(['instructor', 'course', 'participants']);

        // Check if user can view this stream
        if (!$stream->is_public && !$this->canViewStream($stream)) {
            abort(403, 'You do not have permission to view this stream.');
        }

        // Check if stream is live or scheduled
        $canJoin = $this->canJoinStream($stream);

        return view('streams.show', compact('stream', 'canJoin'));
    }

    /**
     * Show the form for editing the stream.
     */
    public function edit(Stream $stream)
    {
        $this->authorize('manage streams');
        
        if ($stream->instructor_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            abort(403, 'You can only edit your own streams.');
        }

        $courses = Course::where('instructor_id', $stream->instructor_id)
            ->where('status', 'published')
            ->get();

        return view('streams.edit', compact('stream', 'courses'));
    }

    /**
     * Update the specified stream.
     */
    public function update(Request $request, Stream $stream)
    {
        $this->authorize('manage streams');
        
        if ($stream->instructor_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            abort(403, 'You can only edit your own streams.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'course_id' => 'nullable|exists:courses,id',
            'scheduled_at' => 'required|date',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_participants' => 'nullable|integer|min:1|max:1000',
            'is_public' => 'boolean',
            'allow_chat' => 'boolean',
            'allow_recording' => 'boolean',
        ]);

        // Don't allow editing if stream is live
        if ($stream->status === 'live') {
            return redirect()->back()->with('error', 'Cannot edit a live stream.');
        }

        $stream->update($validated);

        return redirect()->route('instructor.streams.show', $stream)
            ->with('success', 'Stream updated successfully!');
    }

    /**
     * Remove the specified stream.
     */
    public function destroy(Stream $stream)
    {
        $this->authorize('manage streams');
        
        if ($stream->instructor_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            abort(403, 'You can only delete your own streams.');
        }

        // Don't allow deletion if stream is live
        if ($stream->status === 'live') {
            return redirect()->back()->with('error', 'Cannot delete a live stream.');
        }

        $stream->delete();

        return redirect()->route('instructor.streams.index')
            ->with('success', 'Stream deleted successfully!');
    }

    /**
     * Start the stream.
     */
    public function start(Stream $stream)
    {
        $this->authorize('manage streams');
        
        if ($stream->instructor_id !== Auth::id()) {
            abort(403, 'You can only start your own streams.');
        }

        if ($stream->status !== 'scheduled') {
            return redirect()->back()->with('error', 'Stream cannot be started.');
        }

        $stream->update([
            'status' => 'live',
            'started_at' => now(),
        ]);

        // Here you would integrate with your streaming service
        // For example, create a WebRTC room, start RTMP stream, etc.

        return redirect()->route('streams.show', $stream)
            ->with('success', 'Stream started successfully!');
    }

    /**
     * End the stream.
     */
    public function end(Stream $stream)
    {
        $this->authorize('manage streams');
        
        if ($stream->instructor_id !== Auth::id()) {
            abort(403, 'You can only end your own streams.');
        }

        if ($stream->status !== 'live') {
            return redirect()->back()->with('error', 'Stream is not live.');
        }

        $stream->update([
            'status' => 'ended',
            'ended_at' => now(),
        ]);

        return redirect()->route('instructor.streams.index')
            ->with('success', 'Stream ended successfully!');
    }

    /**
     * Join a stream as a participant.
     */
    public function join(Stream $stream)
    {
        if (!$this->canJoinStream($stream)) {
            abort(403, 'You cannot join this stream.');
        }

        // Add user as participant if not already joined
        if (!$stream->participants()->where('user_id', Auth::id())->exists()) {
            $stream->participants()->attach(Auth::id(), [
                'joined_at' => now(),
            ]);
        }

        return view('streams.viewer', compact('stream'));
    }

    /**
     * Leave a stream.
     */
    public function leave(Stream $stream)
    {
        $stream->participants()->updateExistingPivot(Auth::id(), [
            'left_at' => now(),
        ]);

        return redirect()->route('streams.index')
            ->with('success', 'Left stream successfully!');
    }

    /**
     * Get stream analytics.
     */
    public function analytics(Stream $stream)
    {
        $this->authorize('view stream analytics');
        
        if ($stream->instructor_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            abort(403, 'You can only view analytics for your own streams.');
        }

        $analytics = [
            'total_participants' => $stream->participants()->count(),
            'peak_concurrent' => $stream->peak_concurrent_viewers ?? 0,
            'average_watch_time' => $this->calculateAverageWatchTime($stream),
            'engagement_rate' => $this->calculateEngagementRate($stream),
            'chat_messages' => $stream->chatMessages()->count(),
        ];

        return view('streams.analytics', compact('stream', 'analytics'));
    }

    /**
     * Check if user can view the stream.
     */
    private function canViewStream(Stream $stream)
    {
        $user = Auth::user();

        // Instructor can always view their own streams
        if ($stream->instructor_id === $user->id) {
            return true;
        }

        // Admins can view all streams
        if ($user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // If stream is linked to a course, check enrollment
        if ($stream->course_id) {
            return $stream->course->enrollments()->where('user_id', $user->id)->exists();
        }

        // Public streams can be viewed by anyone
        return $stream->is_public;
    }

    /**
     * Check if user can join the stream.
     */
    private function canJoinStream(Stream $stream)
    {
        // Stream must be live or starting soon (within 15 minutes)
        if ($stream->status !== 'live' && $stream->scheduled_at->diffInMinutes(now()) > 15) {
            return false;
        }

        // Check if stream has reached max participants
        if ($stream->max_participants && $stream->participants()->count() >= $stream->max_participants) {
            return false;
        }

        return $this->canViewStream($stream);
    }

    /**
     * Calculate average watch time for the stream.
     */
    private function calculateAverageWatchTime(Stream $stream)
    {
        $participants = $stream->participants()
            ->wherePivot('left_at', '!=', null)
            ->get();

        if ($participants->isEmpty()) {
            return 0;
        }

        $totalWatchTime = $participants->sum(function ($participant) {
            $joinedAt = $participant->pivot->joined_at;
            $leftAt = $participant->pivot->left_at;
            
            if ($joinedAt && $leftAt) {
                return $leftAt->diffInMinutes($joinedAt);
            }
            
            return 0;
        });

        return $totalWatchTime / $participants->count();
    }

    /**
     * Calculate engagement rate for the stream.
     */
    private function calculateEngagementRate(Stream $stream)
    {
        $totalParticipants = $stream->participants()->count();
        $activeParticipants = $stream->chatMessages()
            ->distinct('user_id')
            ->count();

        if ($totalParticipants === 0) {
            return 0;
        }

        return ($activeParticipants / $totalParticipants) * 100;
    }
}
