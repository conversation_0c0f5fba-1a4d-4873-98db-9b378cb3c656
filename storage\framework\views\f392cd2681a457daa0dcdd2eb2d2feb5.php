<?php $__env->startSection('title', 'Role Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="roles-management">
    <!-- Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>🛡️ Role Management</h1>
                <p>Manage user roles and permissions across the platform</p>
            </div>
            <div class="header-actions">
                <a href="<?php echo e(route('admin.roles.create')); ?>" class="btn btn-primary">
                    ➕ Create New Role
                </a>
                <a href="<?php echo e(route('admin.roles.export')); ?>" class="btn btn-outline">
                    📊 Export Roles
                </a>
            </div>
        </div>
    </div>

    <!-- Roles Overview -->
    <div class="roles-overview">
        <div class="overview-grid">
            <div class="overview-card">
                <div class="card-icon">🛡️</div>
                <div class="card-content">
                    <h3>Total Roles</h3>
                    <p class="card-value"><?php echo e($roles->count()); ?></p>
                </div>
            </div>
            <div class="overview-card">
                <div class="card-icon">👥</div>
                <div class="card-content">
                    <h3>Total Users</h3>
                    <p class="card-value"><?php echo e($roles->sum('users_count')); ?></p>
                </div>
            </div>
            <div class="overview-card">
                <div class="card-icon">🔑</div>
                <div class="card-content">
                    <h3>Total Permissions</h3>
                    <p class="card-value"><?php echo e($permissions->flatten()->count()); ?></p>
                </div>
            </div>
            <div class="overview-card">
                <div class="card-icon">⚙️</div>
                <div class="card-content">
                    <h3>Permission Groups</h3>
                    <p class="card-value"><?php echo e($permissions->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles List -->
    <div class="roles-list">
        <div class="list-header">
            <h2>System Roles</h2>
            <div class="list-controls">
                <div class="search-box">
                    <input type="text" id="roleSearch" placeholder="Search roles...">
                    <span class="search-icon">🔍</span>
                </div>
                <select id="roleFilter" class="filter-select">
                    <option value="">All Roles</option>
                    <option value="system">System Roles</option>
                    <option value="custom">Custom Roles</option>
                </select>
            </div>
        </div>

        <div class="roles-grid">
            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="role-card <?php echo e(in_array($role->name, ['super-admin', 'admin', 'instructor', 'student']) ? 'system-role' : 'custom-role'); ?>" 
                     data-role-name="<?php echo e($role->name); ?>">
                    
                    <div class="role-header">
                        <div class="role-info">
                            <h3 class="role-name">
                                <?php switch($role->name):
                                    case ('super-admin'): ?>
                                        👑 <?php echo e(ucwords(str_replace('-', ' ', $role->name))); ?>

                                        <?php break; ?>
                                    <?php case ('admin'): ?>
                                        🛡️ <?php echo e(ucwords(str_replace('-', ' ', $role->name))); ?>

                                        <?php break; ?>
                                    <?php case ('instructor'): ?>
                                        👨‍🏫 <?php echo e(ucwords(str_replace('-', ' ', $role->name))); ?>

                                        <?php break; ?>
                                    <?php case ('student'): ?>
                                        🎓 <?php echo e(ucwords(str_replace('-', ' ', $role->name))); ?>

                                        <?php break; ?>
                                    <?php default: ?>
                                        🔧 <?php echo e(ucwords(str_replace('-', ' ', $role->name))); ?>

                                <?php endswitch; ?>
                            </h3>
                            <?php if(in_array($role->name, ['super-admin', 'admin', 'instructor', 'student'])): ?>
                                <span class="role-badge system">System Role</span>
                            <?php else: ?>
                                <span class="role-badge custom">Custom Role</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="role-actions">
                            <a href="<?php echo e(route('admin.roles.show', $role)); ?>" class="action-btn view" title="View Details">
                                👁️
                            </a>
                            <?php if(!in_array($role->name, ['super-admin']) || auth()->user()->hasRole('super-admin')): ?>
                                <a href="<?php echo e(route('admin.roles.edit', $role)); ?>" class="action-btn edit" title="Edit Role">
                                    ✏️
                                </a>
                            <?php endif; ?>
                            <?php if(!in_array($role->name, ['super-admin', 'admin', 'instructor', 'student'])): ?>
                                <button type="button" class="action-btn delete" onclick="deleteRole(<?php echo e($role->id); ?>)" title="Delete Role">
                                    🗑️
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if($role->description): ?>
                        <p class="role-description"><?php echo e($role->description); ?></p>
                    <?php endif; ?>

                    <div class="role-stats">
                        <div class="stat">
                            <span class="stat-icon">👥</span>
                            <span class="stat-text"><?php echo e($role->users_count); ?> users</span>
                        </div>
                        <div class="stat">
                            <span class="stat-icon">🔑</span>
                            <span class="stat-text"><?php echo e($role->permissions->count()); ?> permissions</span>
                        </div>
                    </div>

                    <div class="role-permissions">
                        <h4>Key Permissions:</h4>
                        <div class="permissions-list">
                            <?php $__currentLoopData = $role->permissions->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="permission-tag"><?php echo e($permission->name); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($role->permissions->count() > 5): ?>
                                <span class="permission-tag more">+<?php echo e($role->permissions->count() - 5); ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="role-footer">
                        <small class="role-created">
                            Created <?php echo e($role->created_at->diffForHumans()); ?>

                        </small>
                        <div class="role-users-preview">
                            <?php $__currentLoopData = $role->users->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="user-avatar" title="<?php echo e($user->name); ?>">
                                    <?php if($user->avatar): ?>
                                        <img src="<?php echo e(Storage::url($user->avatar)); ?>" alt="<?php echo e($user->name); ?>">
                                    <?php else: ?>
                                        <div class="avatar-placeholder">
                                            <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($role->users_count > 3): ?>
                                <div class="user-avatar more">+<?php echo e($role->users_count - 3); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Permission Groups Overview -->
    <div class="permissions-overview">
        <h2>Permission Groups</h2>
        <div class="permissions-grid">
            <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="permission-group">
                    <h3 class="group-name">
                        <?php switch($group):
                            case ('users'): ?>
                                👥 User Management
                                <?php break; ?>
                            <?php case ('courses'): ?>
                                📚 Course Management
                                <?php break; ?>
                            <?php case ('payments'): ?>
                                💰 Payment Management
                                <?php break; ?>
                            <?php case ('communities'): ?>
                                🏘️ Community Management
                                <?php break; ?>
                            <?php case ('chats'): ?>
                                💬 Chat Management
                                <?php break; ?>
                            <?php case ('analytics'): ?>
                                📊 Analytics
                                <?php break; ?>
                            <?php case ('admin'): ?>
                                ⚙️ Admin Functions
                                <?php break; ?>
                            <?php default: ?>
                                🔧 <?php echo e(ucwords($group)); ?>

                        <?php endswitch; ?>
                    </h3>
                    <p class="group-count"><?php echo e($groupPermissions->count()); ?> permissions</p>
                    <div class="group-permissions">
                        <?php $__currentLoopData = $groupPermissions->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="permission-item"><?php echo e($permission->name); ?></span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($groupPermissions->count() > 3): ?>
                            <span class="permission-item more">+<?php echo e($groupPermissions->count() - 3); ?> more</span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>🗑️ Delete Role</h3>
            <button type="button" class="close-btn" onclick="closeDeleteModal()">✕</button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this role? This action cannot be undone.</p>
            <p><strong>Note:</strong> Users with this role will lose their permissions.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
            <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Role</button>
        </div>
    </div>
</div>

<style>
.roles-management {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.roles-overview {
    margin-bottom: 2rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.overview-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    font-size: 2.5rem;
}

.card-content h3 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-value {
    color: #3b82f6;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.roles-list {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.list-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.list-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.role-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.role-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.role-card.system-role {
    border-left: 4px solid #3b82f6;
}

.role-card.custom-role {
    border-left: 4px solid #10b981;
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.role-name {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.role-badge.system {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.role-badge.custom {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.role-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.875rem;
}

.action-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
}

.role-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.role-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-icon {
    font-size: 1rem;
}

.stat-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.role-permissions h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.permission-tag {
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #a0a0a0;
    font-size: 0.75rem;
}

.permission-tag.more {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.role-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.role-created {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.role-users-preview {
    display: flex;
    gap: 0.25rem;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
}

.user-avatar.more {
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a0a0a0;
    font-size: 0.625rem;
    font-weight: 600;
}

.permissions-overview {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.permissions-overview h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.permission-group {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
}

.group-name {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.group-count {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.group-permissions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.permission-item {
    color: #a0a0a0;
    font-size: 0.875rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.permission-item.more {
    color: #3b82f6;
    font-weight: 500;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.modal-body {
    padding: 2rem;
}

.modal-body p {
    color: #a0a0a0;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .roles-management {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-controls {
        justify-content: space-between;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .roles-grid {
        grid-template-columns: 1fr;
    }
    
    .permissions-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
let deleteRoleId = null;

// Search functionality
document.getElementById('roleSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const roleCards = document.querySelectorAll('.role-card');
    
    roleCards.forEach(card => {
        const roleName = card.dataset.roleName.toLowerCase();
        if (roleName.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// Filter functionality
document.getElementById('roleFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const roleCards = document.querySelectorAll('.role-card');
    
    roleCards.forEach(card => {
        if (filterValue === '') {
            card.style.display = 'block';
        } else if (filterValue === 'system' && card.classList.contains('system-role')) {
            card.style.display = 'block';
        } else if (filterValue === 'custom' && card.classList.contains('custom-role')) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

function deleteRole(roleId) {
    deleteRoleId = roleId;
    document.getElementById('deleteModal').style.display = 'flex';
}

function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    deleteRoleId = null;
}

function confirmDelete() {
    if (deleteRoleId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/roles/${deleteRoleId}`;
        
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '<?php echo e(csrf_token()); ?>';
        
        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/admin/roles/index.blade.php ENDPATH**/ ?>