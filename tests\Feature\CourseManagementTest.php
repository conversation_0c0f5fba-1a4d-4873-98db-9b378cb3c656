<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseLesson;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class CourseManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'student']);
        Role::create(['name' => 'instructor']);
        Role::create(['name' => 'admin']);
    }

    /** @test */
    public function users_can_view_courses_index()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->get('/courses');

        $response->assertStatus(200);
        $response->assertSee($course->title);
    }

    /** @test */
    public function users_can_view_course_details()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->get("/courses/{$course->id}");

        $response->assertStatus(200);
        $response->assertSee($course->title);
        $response->assertSee($course->description);
    }

    /** @test */
    public function instructors_can_create_courses()
    {
        $instructor = User::factory()->create();
        $instructor->assignRole('instructor');

        $category = CourseCategory::factory()->create();

        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description',
            'category_id' => $category->id,
            'price' => 99.99,
            'difficulty' => 'beginner',
            'duration' => 120,
            'status' => 'draft'
        ];

        $response = $this->actingAs($instructor)->post('/courses', $courseData);

        $response->assertRedirect();
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course',
            'instructor_id' => $instructor->id
        ]);
    }

    /** @test */
    public function students_cannot_create_courses()
    {
        $student = User::factory()->create();
        $student->assignRole('student');

        $category = CourseCategory::factory()->create();

        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description',
            'category_id' => $category->id,
            'price' => 99.99,
            'difficulty' => 'beginner',
            'duration' => 120,
            'status' => 'draft'
        ];

        $response = $this->actingAs($student)->post('/courses', $courseData);

        $response->assertStatus(403);
    }

    /** @test */
    public function users_can_enroll_in_free_courses()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'price' => 0,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->post("/courses/{$course->id}/enroll");

        $response->assertRedirect();
        $this->assertDatabaseHas('course_enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id
        ]);
    }

    /** @test */
    public function users_cannot_enroll_in_paid_courses_without_payment()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'price' => 99.99,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->post("/courses/{$course->id}/enroll");

        $response->assertRedirect();
        $this->assertDatabaseMissing('course_enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id
        ]);
    }

    /** @test */
    public function course_search_works_correctly()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category = CourseCategory::factory()->create();

        $course1 = Course::factory()->create([
            'title' => 'Laravel Development',
            'category_id' => $category->id,
            'status' => 'published'
        ]);

        $course2 = Course::factory()->create([
            'title' => 'React Frontend',
            'category_id' => $category->id,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->get('/courses?search=Laravel');

        $response->assertStatus(200);
        $response->assertSee($course1->title);
        $response->assertDontSee($course2->title);
    }

    /** @test */
    public function course_filtering_by_category_works()
    {
        $user = User::factory()->create();
        $user->assignRole('student');

        $category1 = CourseCategory::factory()->create(['name' => 'Programming']);
        $category2 = CourseCategory::factory()->create(['name' => 'Design']);

        $course1 = Course::factory()->create([
            'category_id' => $category1->id,
            'status' => 'published'
        ]);

        $course2 = Course::factory()->create([
            'category_id' => $category2->id,
            'status' => 'published'
        ]);

        $response = $this->actingAs($user)->get("/courses?category={$category1->id}");

        $response->assertStatus(200);
        $response->assertSee($course1->title);
        $response->assertDontSee($course2->title);
    }

    /** @test */
    public function instructors_can_add_lessons_to_their_courses()
    {
        $instructor = User::factory()->create();
        $instructor->assignRole('instructor');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'instructor_id' => $instructor->id,
            'category_id' => $category->id
        ]);

        $lessonData = [
            'title' => 'Introduction to Laravel',
            'content' => 'This lesson covers Laravel basics',
            'video_url' => 'https://example.com/video.mp4',
            'duration' => 30,
            'order' => 1
        ];

        $response = $this->actingAs($instructor)
                        ->post("/courses/{$course->id}/lessons", $lessonData);

        $response->assertRedirect();
        $this->assertDatabaseHas('course_lessons', [
            'course_id' => $course->id,
            'title' => 'Introduction to Laravel'
        ]);
    }

    /** @test */
    public function only_course_instructors_can_edit_their_courses()
    {
        $instructor1 = User::factory()->create();
        $instructor2 = User::factory()->create();
        $instructor1->assignRole('instructor');
        $instructor2->assignRole('instructor');

        $category = CourseCategory::factory()->create();
        $course = Course::factory()->create([
            'instructor_id' => $instructor1->id,
            'category_id' => $category->id
        ]);

        $updateData = ['title' => 'Updated Course Title'];

        // Instructor 1 (owner) should be able to edit
        $response1 = $this->actingAs($instructor1)
                         ->put("/courses/{$course->id}", array_merge($course->toArray(), $updateData));
        $response1->assertRedirect();

        // Instructor 2 (not owner) should not be able to edit
        $response2 = $this->actingAs($instructor2)
                         ->put("/courses/{$course->id}", array_merge($course->toArray(), $updateData));
        $response2->assertStatus(403);
    }
}
