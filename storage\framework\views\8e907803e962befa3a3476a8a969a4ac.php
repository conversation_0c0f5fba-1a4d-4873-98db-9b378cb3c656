<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($community->name); ?> - <?php echo e(config('app.name')); ?></title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/community.css')); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="<?php echo e(route('home')); ?>">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                <li><a href="<?php echo e(route('communities.index')); ?>" class="active">Community</a></li>
                <li><a href="<?php echo e(route('chat.index')); ?>">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="community-detail-main">
        <div class="container">
            <!-- Community Header -->
            <div class="community-header">
                <?php if($community->banner): ?>
                    <div class="community-banner">
                        <img src="<?php echo e(Storage::url($community->banner)); ?>" alt="<?php echo e($community->name); ?>">
                    </div>
                <?php endif; ?>
                
                <div class="community-info">
                    <div class="community-meta">
                        <h1><?php echo e($community->name); ?></h1>
                        <p class="community-description"><?php echo e($community->description); ?></p>
                        
                        <div class="community-stats">
                            <div class="stat">
                                <span class="stat-value"><?php echo e(number_format($stats['total_members'])); ?></span>
                                <span class="stat-label">Members</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value"><?php echo e(number_format($stats['total_posts'])); ?></span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value"><?php echo e(number_format($stats['active_members'])); ?></span>
                                <span class="stat-label">Active This Week</span>
                            </div>
                        </div>
                        
                        <?php if($community->course): ?>
                            <div class="community-course">
                                <span class="course-label">Related Course:</span>
                                <a href="<?php echo e(route('courses.show', $community->course)); ?>" class="course-link">
                                    <?php echo e($community->course->title); ?>

                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="community-actions">
                        <?php if($isMember): ?>
                            <button class="btn btn-success" disabled>
                                ✅ Member
                            </button>
                            <form method="POST" action="<?php echo e(route('communities.leave', $community)); ?>" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-secondary" 
                                        onclick="return confirm('Are you sure you want to leave this community?')">
                                    Leave Community
                                </button>
                            </form>
                        <?php else: ?>
                            <form method="POST" action="<?php echo e(route('communities.join', $community)); ?>" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-primary">
                                    Join Community
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Community Content -->
            <div class="community-content-layout">
                <!-- Main Content -->
                <div class="community-posts">
                    <?php if($isMember): ?>
                        <!-- Create Post Form -->
                        <div class="create-post-card">
                            <form method="POST" action="<?php echo e(route('communities.posts.store', $community)); ?>" class="create-post-form">
                                <?php echo csrf_field(); ?>
                                <div class="form-group">
                                    <textarea name="content" placeholder="Share something with the community..." 
                                              class="post-textarea" rows="3" required></textarea>
                                </div>
                                <div class="form-actions">
                                    <div class="post-options">
                                        <label class="file-upload">
                                            📎 Attach File
                                            <input type="file" name="attachments[]" multiple style="display: none;">
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Post</button>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>

                    <!-- Posts List -->
                    <div class="posts-list">
                        <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="post-card">
                                <div class="post-header">
                                    <div class="post-author">
                                        <div class="author-avatar">
                                            <?php echo e(substr($post->user->name, 0, 1)); ?>

                                        </div>
                                        <div class="author-info">
                                            <h4><?php echo e($post->user->name); ?></h4>
                                            <span class="post-time"><?php echo e($post->created_at ? $post->created_at->diffForHumans() : 'recently'); ?></span>
                                        </div>
                                    </div>
                                    
                                    <?php if($post->is_pinned): ?>
                                        <div class="post-badge pinned">📌 Pinned</div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="post-content">
                                    <?php if($post->title): ?>
                                        <h3 class="post-title"><?php echo e($post->title); ?></h3>
                                    <?php endif; ?>
                                    <div class="post-text"><?php echo e($post->content); ?></div>
                                    
                                    <?php if($post->attachments): ?>
                                        <div class="post-attachments">
                                            <?php $__currentLoopData = $post->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="attachment">
                                                    <a href="<?php echo e(Storage::url($attachment['path'])); ?>" target="_blank">
                                                        📎 <?php echo e($attachment['name']); ?>

                                                    </a>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="post-actions">
                                    <button class="post-action like-btn" data-post-id="<?php echo e($post->id); ?>">
                                        👍 <span class="like-count"><?php echo e($post->likes_count); ?></span>
                                    </button>
                                    <button class="post-action reply-btn" data-post-id="<?php echo e($post->id); ?>">
                                        💬 Reply (<?php echo e($post->replies_count); ?>)
                                    </button>
                                    <button class="post-action share-btn">
                                        🔗 Share
                                    </button>
                                </div>
                                
                                <!-- Replies -->
                                <?php if($post->replies->count() > 0): ?>
                                    <div class="post-replies">
                                        <?php $__currentLoopData = $post->replies->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="reply-item">
                                                <div class="reply-author"><?php echo e($reply->user->name); ?></div>
                                                <div class="reply-content"><?php echo e($reply->content); ?></div>
                                                <div class="reply-time"><?php echo e($reply->created_at ? $reply->created_at->diffForHumans() : 'recently'); ?></div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        
                                        <?php if($post->replies->count() > 3): ?>
                                            <button class="load-more-replies">
                                                Load <?php echo e($post->replies->count() - 3); ?> more replies
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="empty-posts">
                                <div class="empty-icon">💬</div>
                                <h3>No posts yet</h3>
                                <p>Be the first to start a conversation in this community!</p>
                                <?php if($isMember): ?>
                                    <button class="btn btn-primary" onclick="document.querySelector('.post-textarea').focus()">
                                        Create First Post
                                    </button>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if($posts->hasPages()): ?>
                        <div class="pagination-wrapper">
                            <?php echo e($posts->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="community-sidebar">
                    <!-- Community Rules -->
                    <div class="sidebar-card">
                        <h4>Community Rules</h4>
                        <ul class="rules-list">
                            <li>🤝 Be respectful to all members</li>
                            <li>💡 Share valuable insights and knowledge</li>
                            <li>🚫 No spam, advertising, or off-topic content</li>
                            <li>🎯 Stay focused on growth and success</li>
                            <li>💪 Support each other's journey</li>
                        </ul>
                    </div>

                    <!-- Recent Members -->
                    <div class="sidebar-card">
                        <h4>Recent Members</h4>
                        <div class="recent-members">
                            <?php $__currentLoopData = $community->members()->latest('pivot_joined_at')->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="member-item">
                                    <div class="member-avatar"><?php echo e(substr($member->name, 0, 1)); ?></div>
                                    <div class="member-info">
                                        <div class="member-name"><?php echo e($member->name); ?></div>
                                        <div class="member-joined">Joined <?php echo e($member->pivot->joined_at ? \Carbon\Carbon::parse($member->pivot->joined_at)->diffForHumans() : 'recently'); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Community Stats -->
                    <div class="sidebar-card">
                        <h4>Community Activity</h4>
                        <div class="activity-stats">
                            <div class="activity-stat">
                                <span class="stat-number"><?php echo e($stats['total_posts']); ?></span>
                                <span class="stat-label">Total Posts</span>
                            </div>
                            <div class="activity-stat">
                                <span class="stat-number"><?php echo e($stats['active_members']); ?></span>
                                <span class="stat-label">Active This Week</span>
                            </div>
                            <div class="activity-stat">
                                <span class="stat-number"><?php echo e($community->created_at->diffInDays(now())); ?></span>
                                <span class="stat-label">Days Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/community.js')); ?>"></script>
    <script>
        // Initialize community page
        document.addEventListener('DOMContentLoaded', function() {
            // Handle post likes
            document.querySelectorAll('.like-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const postId = this.getAttribute('data-post-id');
                    togglePostLike(postId);
                });
            });
            
            // Handle reply buttons
            document.querySelectorAll('.reply-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const postId = this.getAttribute('data-post-id');
                    showReplyForm(postId);
                });
            });
        });
        
        function togglePostLike(postId) {
            fetch(`/communities/posts/${postId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const likeButton = document.querySelector(`[data-post-id="${postId}"] .like-count`);
                    likeButton.textContent = data.likes_count;
                }
            })
            .catch(error => console.error('Error:', error));
        }
        
        function showReplyForm(postId) {
            // Implementation for reply form
            console.log('Show reply form for post:', postId);
        }
    </script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/community/show.blade.php ENDPATH**/ ?>