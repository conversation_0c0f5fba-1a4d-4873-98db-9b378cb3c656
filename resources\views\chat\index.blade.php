@extends('layouts.app')

@section('title', 'Chat')

@push('styles')
    <link href="{{ asset('assets/css/chat.css') }}" rel="stylesheet">
@endpush

@section('content')
    <!-- Main Content -->
    <div class="chat-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Real-Time Chat</h1>
                    <p>Connect instantly with the community</p>
                </div>
                @can('create chats')
                    <div class="header-actions">
                        <a href="{{ route('chat.create') }}" class="btn btn-primary">
                            Create Chat Room
                        </a>
                    </div>
                @endcan
            </div>

            <!-- Chat Rooms Layout -->
            <div class="chat-layout">
                <!-- My Chat Rooms -->
                @if($joinedRooms->count() > 0)
                    <div class="chat-section">
                        <h3 class="section-title">My Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            @foreach($joinedRooms as $room)
                                <div class="chat-room-card active-room">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="{{ route('chat.show', $room) }}">{{ $room->name }}</a>
                                            </h4>
                                            <span class="room-type">{{ ucfirst($room->type) }}</span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count">{{ $room->member_count }}</span>
                                        </div>
                                    </div>
                                    
                                    @if($room->description)
                                        <p class="room-description">{{ Str::limit($room->description, 100) }}</p>
                                    @endif
                                    
                                    @if($room->course)
                                        <div class="room-course">
                                            <span class="course-label">Course:</span>
                                            <span class="course-name">{{ $room->course->title }}</span>
                                        </div>
                                    @endif
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value">{{ $room->member_count }} members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value">{{ $room->message_count }} messages</span>
                                        </div>
                                        @if($room->last_message_at)
                                            <div class="stat">
                                                <span class="stat-icon">🕒</span>
                                                <span class="stat-value">{{ $room->last_message_at->diffForHumans() }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="{{ route('chat.show', $room) }}" class="btn btn-primary btn-block">
                                            Enter Chat
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Public Chat Rooms -->
                @if($publicRooms->count() > 0)
                    <div class="chat-section">
                        <h3 class="section-title">Public Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            @foreach($publicRooms as $room)
                                <div class="chat-room-card">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="{{ route('chat.show', $room) }}">{{ $room->name }}</a>
                                            </h4>
                                            <span class="room-type">{{ ucfirst($room->type) }}</span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count">{{ $room->member_count }}</span>
                                        </div>
                                    </div>
                                    
                                    @if($room->description)
                                        <p class="room-description">{{ Str::limit($room->description, 100) }}</p>
                                    @endif
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value">{{ $room->member_count }} members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value">{{ $room->message_count }} messages</span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="{{ route('chat.show', $room) }}" class="btn btn-secondary btn-block">
                                            Join & Chat
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Course Chat Rooms -->
                @if($courseRooms->count() > 0)
                    <div class="chat-section">
                        <h3 class="section-title">Course Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            @foreach($courseRooms as $room)
                                <div class="chat-room-card course-room">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="{{ route('chat.show', $room) }}">{{ $room->name }}</a>
                                            </h4>
                                            <span class="room-type">Course Chat</span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count">{{ $room->member_count }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-course">
                                        <span class="course-label">Course:</span>
                                        <a href="{{ route('courses.show', $room->course) }}" class="course-link">
                                            {{ $room->course->title }}
                                        </a>
                                    </div>
                                    
                                    @if($room->description)
                                        <p class="room-description">{{ Str::limit($room->description, 100) }}</p>
                                    @endif
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value">{{ $room->member_count }} members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value">{{ $room->message_count }} messages</span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="{{ route('chat.show', $room) }}" class="btn btn-primary btn-block">
                                            Enter Chat
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Empty State -->
                @if($joinedRooms->count() === 0 && $publicRooms->count() === 0 && $courseRooms->count() === 0)
                    <div class="empty-state">
                        <div class="empty-icon">💬</div>
                        <h3>No chat rooms available</h3>
                        <p>Start by creating a chat room or enrolling in courses to access course-specific chats</p>
                        <div class="empty-actions">
                            @can('create chats')
                                <a href="{{ route('chat.create') }}" class="btn btn-primary">
                                    Create Chat Room
                                </a>
                            @endcan
                            <a href="{{ route('courses.index') }}" class="btn btn-secondary">
                                Browse Courses
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Chat Guidelines -->
            <div class="chat-guidelines">
                <h4>Chat Guidelines</h4>
                <div class="guidelines-grid">
                    <div class="guideline-item">
                        <span class="guideline-icon">🤝</span>
                        <span class="guideline-text">Be respectful to all members</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">💡</span>
                        <span class="guideline-text">Share valuable insights and knowledge</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">🚫</span>
                        <span class="guideline-text">No spam, advertising, or off-topic content</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">🎯</span>
                        <span class="guideline-text">Stay focused on growth and success</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
