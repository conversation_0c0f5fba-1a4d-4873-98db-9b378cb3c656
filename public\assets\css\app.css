/* The Real World - Main Application Styles */

:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --dark-bg: #0f0f0f;
    --dark-surface: #1a1a1a;
    --dark-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
}

/* Layout */
.app-wrapper {
    min-height: 100vh;
    background: var(--dark-bg);
    position: relative;
}

.main-content {
    min-height: calc(100vh - 70px);
    padding-top: 2rem;
}

.page-header {
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--dark-border);
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: var(--text-primary);
    margin: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* Grid System */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* Navigation */
.main-navigation {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--dark-border);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-logo .logo-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-icon {
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Search Bar */
.nav-search {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--dark-border);
    border-radius: 25px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.search-form:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    width: 200px;
    font-size: 0.875rem;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Notifications */
.nav-notifications {
    position: relative;
}

.notification-btn {
    position: relative;
    color: var(--text-secondary);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
}

.notification-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--error-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

/* User Dropdown */
.nav-user-dropdown {
    position: relative;
}

.user-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--dark-border);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.user-name {
    font-weight: 500;
    font-size: 0.875rem;
}

.dropdown-arrow {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.user-dropdown-btn:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: rgba(26, 26, 26, 0.98);
    backdrop-filter: blur(10px);
    border: 1px solid var(--dark-border);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    min-width: 220px;
    z-index: 1000;
}

.dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid var(--dark-border);
}

.dropdown-header .user-name {
    color: var(--text-primary);
    font-weight: 600;
    display: block;
}

.dropdown-header .user-email {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.item-icon {
    font-size: 1rem;
}

.dropdown-divider {
    height: 1px;
    background: var(--dark-border);
    margin: 0.5rem 0;
}

.dropdown-form {
    margin: 0;
}

.logout-btn {
    color: var(--error-color);
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* Auth Links */
.nav-auth {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.auth-link {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.login-link {
    color: var(--text-secondary);
}

.login-link:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.register-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.register-link:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid var(--dark-border);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-header {
    border-bottom: 1px solid var(--dark-border);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid var(--dark-border);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--dark-border);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.12);
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* Alerts */
.alert {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    border-left: 4px solid;
    position: relative;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
    font-weight: 500;
}

.alert-close {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.alert-success {
    background: rgba(16, 185, 129, 0.15);
    border-color: var(--success-color);
    color: #34d399;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.15);
    border-color: var(--warning-color);
    color: #fbbf24;
}

.alert-error,
.alert-danger {
    background: rgba(239, 68, 68, 0.15);
    border-color: var(--error-color);
    color: #f87171;
}

.alert-info {
    background: rgba(59, 130, 246, 0.15);
    border-color: var(--primary-color);
    color: #60a5fa;
}

.error-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.error-list li {
    margin-bottom: 0.25rem;
}

.error-list li:before {
    content: "• ";
    color: inherit;
    margin-right: 0.5rem;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 15, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(59, 130, 246, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 15px;
    }

    .search-input {
        width: 150px;
    }

    .nav-links {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .nav-content {
        height: 60px;
    }

    .nav-left {
        gap: 1rem;
    }

    .nav-links {
        display: none;
    }

    .nav-search {
        display: none;
    }

    .search-input {
        width: 120px;
    }

    .user-dropdown-btn {
        padding: 0.375rem 0.75rem;
    }

    .user-name {
        display: none;
    }

    .row {
        margin: 0;
    }

    .col {
        padding: 0;
        margin-bottom: 1rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    .alert {
        padding: 0.75rem 1rem;
        margin-bottom: 0.75rem;
    }

    .alert-content {
        font-size: 0.875rem;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }

    .card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 10px;
    }

    .nav-content {
        height: 55px;
    }

    .logo-text {
        display: none;
    }

    .nav-right {
        gap: 0.5rem;
    }

    .user-dropdown-menu {
        right: -10px;
        min-width: 200px;
    }

    .container {
        padding: 0 10px;
    }

    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .card {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .alert {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }
}

/* Mobile Navigation Menu */
.mobile-nav-toggle {
    display: none;
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.98);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--dark-border);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.mobile-nav-menu.active {
    display: block;
}

.mobile-nav-links {
    padding: 1rem;
}

.mobile-nav-link {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.mobile-search {
    padding: 1rem;
    border-top: 1px solid var(--dark-border);
}

.mobile-search .search-form {
    border-radius: 8px;
}

@media (max-width: 768px) {
    .mobile-nav-toggle {
        display: block;
    }
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 10000;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States */
.form-group.focused input,
.form-group.focused textarea,
.form-group.focused select {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading Button States */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Smooth Transitions */
.main-navigation {
    transition: transform 0.3s ease;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

.hidden { display: none !important; }
.block { display: block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }
