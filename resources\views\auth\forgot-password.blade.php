@extends('layouts.app')

@section('title', 'Forgot Password')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <h1>The Real World</h1>
            </div>
            <h2>Reset Your Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
        </div>

        @if (session('status'))
            <div class="alert alert-success">
                {{ session('status') }}
            </div>
        @endif

        <form method="POST" action="{{ route('password.email') }}" class="auth-form">
            @csrf

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       class="form-control @error('email') is-invalid @enderror" 
                       value="{{ old('email') }}" 
                       required 
                       autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <button type="submit" class="btn btn-primary btn-full">
                📧 Send Reset Link
            </button>
        </form>

        <div class="auth-footer">
            <p>Remember your password? <a href="{{ route('login') }}">Sign in</a></p>
            <p>Don't have an account? <a href="{{ route('register') }}">Create one</a></p>
        </div>
    </div>

    <div class="auth-info">
        <div class="info-card">
            <div class="info-icon">🔒</div>
            <h3>Secure Reset Process</h3>
            <p>We'll send you a secure link to reset your password. The link will expire in 60 minutes for your security.</p>
        </div>
        
        <div class="info-card">
            <div class="info-icon">📧</div>
            <h3>Check Your Email</h3>
            <p>Make sure to check your spam folder if you don't see the email in your inbox within a few minutes.</p>
        </div>
        
        <div class="info-card">
            <div class="info-icon">🆘</div>
            <h3>Need Help?</h3>
            <p>If you're still having trouble, contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</div>

<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 4rem;
}

.auth-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 400px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.form-control.is-invalid {
    border-color: #ef4444;
}

.invalid-feedback {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-full {
    width: 100%;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.auth-footer a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.auth-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 300px;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
}

.info-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.info-card h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.info-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

.info-card a {
    color: #3b82f6;
    text-decoration: none;
}

.info-card a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-info {
        max-width: 100%;
    }
}
</style>
@endsection
