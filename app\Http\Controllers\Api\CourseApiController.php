<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CourseApiController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['instructor', 'category'])
            ->where('status', 'published');

        // Apply filters
        if ($request->has('category')) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->has('difficulty')) {
            $query->where('difficulty_level', $request->difficulty);
        }

        if ($request->has('price_min')) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->has('price_max')) {
            $query->where('price', '<=', $request->price_max);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('tags', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['title', 'price', 'rating', 'total_students', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $courses = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $courses->items(),
            'pagination' => [
                'current_page' => $courses->currentPage(),
                'last_page' => $courses->lastPage(),
                'per_page' => $courses->perPage(),
                'total' => $courses->total(),
            ],
        ]);
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        if ($course->status !== 'published' &&
            (!Auth::check() || (Auth::user()->id !== $course->instructor_id && !Auth::user()->hasRole(['admin', 'super-admin'])))) {
            return response()->json([
                'success' => false,
                'message' => 'Course not found or not available',
            ], 404);
        }

        $course->load(['instructor', 'category', 'lessons' => function($query) {
            $query->orderBy('order');
        }]);

        // Check if user is enrolled (if authenticated)
        $isEnrolled = false;
        if (Auth::check()) {
            $isEnrolled = CourseEnrollment::where('user_id', Auth::id())
                ->where('course_id', $course->id)
                ->where('status', 'active')
                ->exists();
        }

        return response()->json([
            'success' => true,
            'data' => array_merge($course->toArray(), [
                'is_enrolled' => $isEnrolled,
                'can_enroll' => Auth::check() && !$isEnrolled && $course->instructor_id !== Auth::id(),
            ]),
        ]);
    }

    /**
     * Store a newly created course.
     */
    public function store(Request $request)
    {
        if (!Auth::user()->hasRole(['instructor', 'admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Instructor privileges required.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:course_categories,id',
            'price' => 'required|numeric|min:0',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'nullable|integer|min:1',
            'total_lessons' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $course = Course::create(array_merge($validator->validated(), [
            'instructor_id' => Auth::id(),
            'slug' => \Str::slug($request->title),
            'status' => 'draft',
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Course created successfully',
            'data' => $course->load(['instructor', 'category']),
        ], 201);
    }

    /**
     * Update the specified course.
     */
    public function update(Request $request, Course $course)
    {
        if ($course->instructor_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'sometimes|required|exists:course_categories,id',
            'price' => 'sometimes|required|numeric|min:0',
            'difficulty_level' => 'sometimes|required|in:beginner,intermediate,advanced',
            'duration_hours' => 'nullable|integer|min:1',
            'total_lessons' => 'nullable|integer|min:1',
            'status' => 'sometimes|in:draft,published,archived',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $updateData = $validator->validated();
        if (isset($updateData['title'])) {
            $updateData['slug'] = \Str::slug($updateData['title']);
        }

        $course->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Course updated successfully',
            'data' => $course->load(['instructor', 'category']),
        ]);
    }
}
