/* Payment Styles */

.payment-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

/* Course Summary */
.course-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    margin-bottom: 3rem;
}

.course-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.course-info p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.course-details {
    display: flex;
    gap: 2rem;
    font-size: 0.875rem;
    color: #a0a0a0;
}

.course-instructor,
.course-duration,
.course-level {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.course-instructor::before {
    content: "👨‍🏫";
}

.course-duration::before {
    content: "⏱️";
}

.course-level::before {
    content: "📊";
}

.course-price {
    text-align: center;
}

.price-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.currency {
    font-size: 1.5rem;
    color: #3b82f6;
    font-weight: 600;
}

.amount {
    font-size: 3rem;
    color: #ffffff;
    font-weight: 700;
    margin-left: 0.25rem;
}

.price-note {
    color: #a0a0a0;
    font-size: 0.875rem;
}

/* Payment Options */
.payment-options {
    margin-bottom: 3rem;
}

.payment-options h2 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

.payment-method {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
}

.payment-method.active {
    border-color: #3b82f6;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

.method-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.method-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 12px;
}

.method-info {
    flex: 1;
}

.method-info h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.method-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.method-badges {
    display: flex;
    gap: 0.5rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.75rem;
    color: #ffffff;
    font-weight: 500;
}

.method-select-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.method-select-btn:hover {
    background: #2563eb;
}

/* Payment Forms */
.payment-form {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control option {
    background: #1a1a1a;
    color: #ffffff;
}

/* Stripe Elements */
.stripe-element {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.stripe-element:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Crypto Amount Display */
.crypto-amount {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
    margin-bottom: 1rem;
}

.amount-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.crypto-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.crypto-value {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
}

.crypto-symbol {
    color: #3b82f6;
    font-size: 1.25rem;
    font-weight: 600;
}

.usd-equivalent {
    color: #a0a0a0;
    font-size: 0.875rem;
}

/* Saved Payment Methods */
.saved-methods {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.saved-methods h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.saved-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.saved-method .method-info {
    flex: 1;
}

.method-type {
    color: #ffffff;
    font-weight: 600;
    text-transform: capitalize;
}

.method-details {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

/* Security Info */
.security-info {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-icon {
    font-size: 1.25rem;
}

.badge-text {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.security-note {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

/* Error Messages */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: #ef4444;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    display: none;
}

/* Buttons */
.btn-block {
    width: 100%;
    text-align: center;
}

.btn-primary {
    background: #3b82f6;
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-primary:disabled {
    background: #666666;
    cursor: not-allowed;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .course-summary {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .course-details {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .method-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .method-badges {
        justify-content: center;
    }
    
    .security-badges {
        flex-direction: column;
        gap: 1rem;
    }
    
    .saved-method {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .payment-main {
        padding: 1rem 0;
    }
    
    .course-summary,
    .payment-method,
    .security-info {
        padding: 1.5rem;
    }
    
    .amount {
        font-size: 2.5rem;
    }
    
    .method-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }
}
