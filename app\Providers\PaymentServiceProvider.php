<?php

namespace App\Providers;

use App\Services\PaymentService;
use App\Services\StripePaymentService;
use App\Services\CryptoPaymentService;
use Illuminate\Support\ServiceProvider;
use Stripe\StripeClient;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Stripe client
        $this->app->singleton(StripeClient::class, function ($app) {
            $apiKey = config('services.stripe.secret');
            if (empty($apiKey)) {
                $apiKey = 'sk_test_dummy_key_for_development';
            }
            return new StripeClient($apiKey);
        });

        // Register payment services
        $this->app->singleton(StripePaymentService::class, function ($app) {
            return new StripePaymentService($app->make(StripeClient::class));
        });

        $this->app->singleton(CryptoPaymentService::class, function ($app) {
            return new CryptoPaymentService();
        });

        // Register main payment service
        $this->app->singleton(PaymentService::class, function ($app) {
            return new PaymentService(
                $app->make(StripePaymentService::class),
                $app->make(CryptoPaymentService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set Stripe API version
        \Stripe\Stripe::setApiVersion('2023-10-16');

        // Set Stripe API key
        $apiKey = config('services.stripe.secret');
        if (!empty($apiKey)) {
            \Stripe\Stripe::setApiKey($apiKey);
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            StripeClient::class,
            StripePaymentService::class,
            CryptoPaymentService::class,
            PaymentService::class,
        ];
    }
}
