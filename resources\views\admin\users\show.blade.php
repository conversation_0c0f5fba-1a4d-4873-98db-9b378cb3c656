@extends('layouts.app')

@section('title', 'User Details - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}" class="active">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>User Details</h1>
                    <p>{{ $user->name }} - {{ $user->email }}</p>
                </div>
                <div class="header-actions">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                        ← Back to Users
                    </a>
                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                        ✏️ Edit User
                    </a>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <div class="user-details-container">
                <!-- User Profile Card -->
                <div class="user-profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            @if($user->avatar)
                                <img src="{{ Storage::url($user->avatar) }}" alt="{{ $user->name }}">
                            @else
                                <div class="avatar-placeholder">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </div>
                            @endif
                        </div>
                        <div class="profile-info">
                            <h2>{{ $user->name }}</h2>
                            <p class="user-email">{{ $user->email }}</p>
                            <div class="user-status">
                                @if($user->email_verified_at)
                                    <span class="status-badge verified">✓ Verified</span>
                                @else
                                    <span class="status-badge unverified">⚠ Unverified</span>
                                @endif
                                
                                @if($user->deleted_at)
                                    <span class="status-badge deleted">🚫 Deleted</span>
                                @else
                                    <span class="status-badge active">✅ Active</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="profile-details">
                        <div class="detail-row">
                            <span class="detail-label">User ID:</span>
                            <span class="detail-value">#{{ $user->id }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Joined:</span>
                            <span class="detail-value">{{ $user->created_at->format('M j, Y g:i A') }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Last Login:</span>
                            <span class="detail-value">
                                {{ $user->last_login_at ? $user->last_login_at->format('M j, Y g:i A') : 'Never' }}
                            </span>
                        </div>
                        @if($user->bio)
                            <div class="detail-row">
                                <span class="detail-label">Bio:</span>
                                <span class="detail-value">{{ $user->bio }}</span>
                            </div>
                        @endif
                        @if($user->location)
                            <div class="detail-row">
                                <span class="detail-label">Location:</span>
                                <span class="detail-value">{{ $user->location }}</span>
                            </div>
                        @endif
                        @if($user->website)
                            <div class="detail-row">
                                <span class="detail-label">Website:</span>
                                <span class="detail-value">
                                    <a href="{{ $user->website }}" target="_blank">{{ $user->website }}</a>
                                </span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Roles & Permissions -->
                <div class="user-roles-card">
                    <h3>Roles & Permissions</h3>
                    <div class="roles-list">
                        @forelse($user->roles as $role)
                            <div class="role-item">
                                <div class="role-icon">
                                    @if($role->name === 'super-admin')
                                        👑
                                    @elseif($role->name === 'admin')
                                        🛡️
                                    @elseif($role->name === 'instructor')
                                        🎓
                                    @else
                                        👤
                                    @endif
                                </div>
                                <div class="role-info">
                                    <h4>{{ ucfirst($role->name) }}</h4>
                                    <p>{{ $role->description ?? 'Standard ' . $role->name . ' privileges' }}</p>
                                </div>
                            </div>
                        @empty
                            <p class="no-roles">No roles assigned</p>
                        @endforelse
                    </div>
                </div>

                <!-- Statistics -->
                <div class="user-stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-info">
                            <h4>{{ $user->enrollments()->count() }}</h4>
                            <p>Courses Enrolled</p>
                        </div>
                    </div>
                    
                    @if($user->hasRole('instructor'))
                        <div class="stat-card">
                            <div class="stat-icon">🎓</div>
                            <div class="stat-info">
                                <h4>{{ $user->courses()->count() }}</h4>
                                <p>Courses Created</p>
                            </div>
                        </div>
                    @endif
                    
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-info">
                            <h4>{{ $user->communityMemberships()->count() }}</h4>
                            <p>Communities Joined</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-info">
                            <h4>${{ number_format($user->payments()->sum('amount'), 2) }}</h4>
                            <p>Total Spent</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="user-activity-card">
                    <h3>Recent Activity</h3>
                    <div class="activity-list">
                        @forelse($user->activities()->latest()->take(10)->get() as $activity)
                            <div class="activity-item">
                                <div class="activity-icon">
                                    @if($activity->type === 'course_enrolled')
                                        📚
                                    @elseif($activity->type === 'payment_made')
                                        💰
                                    @elseif($activity->type === 'community_joined')
                                        👥
                                    @else
                                        📝
                                    @endif
                                </div>
                                <div class="activity-info">
                                    <p>{{ $activity->description }}</p>
                                    <span class="activity-time">{{ $activity->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        @empty
                            <p class="no-activity">No recent activity</p>
                        @endforelse
                    </div>
                </div>

                <!-- Enrolled Courses -->
                @if($user->enrollments()->count() > 0)
                    <div class="user-courses-card">
                        <h3>Enrolled Courses</h3>
                        <div class="courses-list">
                            @foreach($user->enrollments()->with('course')->latest()->take(5)->get() as $enrollment)
                                <div class="course-item">
                                    <div class="course-info">
                                        <h4>{{ $enrollment->course->title }}</h4>
                                        <p>Enrolled: {{ $enrollment->created_at->format('M j, Y') }}</p>
                                        <div class="course-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                            </div>
                                            <span class="progress-text">{{ $enrollment->progress_percentage }}% Complete</span>
                                        </div>
                                    </div>
                                    <div class="course-actions">
                                        <a href="{{ route('admin.courses.show', $enrollment->course) }}" class="btn btn-sm btn-secondary">
                                            View Course
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Admin Actions -->
                <div class="admin-actions-card">
                    <h3>Admin Actions</h3>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-warning" onclick="resetPassword('{{ $user->id }}')">
                            🔑 Reset Password
                        </button>
                        
                        @if($user->email_verified_at)
                            <button type="button" class="btn btn-secondary" onclick="unverifyEmail('{{ $user->id }}')">
                                ❌ Unverify Email
                            </button>
                        @else
                            <button type="button" class="btn btn-success" onclick="verifyEmail('{{ $user->id }}')">
                                ✅ Verify Email
                            </button>
                        @endif
                        
                        @if($user->deleted_at)
                            <button type="button" class="btn btn-success" onclick="restoreUser('{{ $user->id }}')">
                                🔄 Restore User
                            </button>
                        @else
                            <button type="button" class="btn btn-danger" onclick="suspendUser('{{ $user->id }}')">
                                🚫 Suspend User
                            </button>
                        @endif
                        
                        <button type="button" class="btn btn-info" onclick="loginAsUser('{{ $user->id }}')">
                            👤 Login as User
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.user-details-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    gap: 2rem;
}

.user-profile-card, .user-roles-card, .user-activity-card, .user-courses-card, .admin-actions-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.profile-info h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.user-email {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.user-status {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.verified {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.unverified {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-badge.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.deleted {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.profile-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: #a0a0a0;
    font-weight: 500;
}

.detail-value {
    color: #ffffff;
}

.detail-value a {
    color: #3b82f6;
    text-decoration: none;
}

.roles-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.role-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.role-icon {
    font-size: 2rem;
}

.role-info h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.role-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.user-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-info h4 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.activity-list, .courses-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item, .course-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.activity-icon {
    font-size: 1.5rem;
}

.activity-info p {
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.course-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.progress-text {
    color: #a0a0a0;
    font-size: 0.875rem;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.no-roles, .no-activity {
    color: #a0a0a0;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .user-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>

<script>
function resetPassword(userId) {
    if (confirm('Are you sure you want to reset this user\'s password?')) {
        // Implementation for password reset
        console.log('Reset password for user:', userId);
    }
}

function verifyEmail(userId) {
    if (confirm('Mark this user\'s email as verified?')) {
        // Implementation for email verification
        console.log('Verify email for user:', userId);
    }
}

function unverifyEmail(userId) {
    if (confirm('Mark this user\'s email as unverified?')) {
        // Implementation for email unverification
        console.log('Unverify email for user:', userId);
    }
}

function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this user? They will not be able to access the platform.')) {
        // Implementation for user suspension
        console.log('Suspend user:', userId);
    }
}

function restoreUser(userId) {
    if (confirm('Restore this user\'s access to the platform?')) {
        // Implementation for user restoration
        console.log('Restore user:', userId);
    }
}

function loginAsUser(userId) {
    if (confirm('Login as this user? You will be logged in as them and can see what they see.')) {
        // Implementation for impersonation
        console.log('Login as user:', userId);
    }
}
</script>
@endsection
