@extends('layouts.app')

@section('title', 'Course Analytics - ' . $course->title)

@section('content')
<div class="container">
    <div class="course-analytics-container">
        <!-- Header -->
        <div class="analytics-header">
            <div class="header-content">
                <div class="course-info">
                    <h1>{{ $course->title }}</h1>
                    <p>Course Analytics & Student Management</p>
                    <div class="course-meta">
                        <span class="status-badge status-{{ $course->status }}">
                            @if($course->status === 'published')
                                ✅ Published
                            @elseif($course->status === 'draft')
                                📝 Draft
                            @else
                                📦 Archived
                            @endif
                        </span>
                        <span class="created-date">Created {{ $course->created_at->format('M j, Y') }}</span>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="{{ route('courses.edit', $course) }}" class="btn btn-secondary">
                        ✏️ Edit Course
                    </a>
                    <a href="{{ route('courses.show', $course) }}" class="btn btn-primary">
                        👁️ View Course
                    </a>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-overview">
            <div class="metric-card revenue">
                <div class="metric-icon">💰</div>
                <div class="metric-content">
                    <h3>Total Revenue</h3>
                    <div class="metric-value">${{ number_format($stats['revenue'], 2) }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📈</span>
                        <span class="change-text">+12.5% this month</span>
                    </div>
                </div>
            </div>

            <div class="metric-card enrollments">
                <div class="metric-icon">👥</div>
                <div class="metric-content">
                    <h3>Total Enrollments</h3>
                    <div class="metric-value">{{ number_format($stats['total_enrollments']) }}</div>
                    <div class="metric-change">
                        <span class="change-icon">📈</span>
                        <span class="change-text">{{ $enrollments->where('created_at', '>=', now()->subDays(30))->count() }} this month</span>
                    </div>
                </div>
            </div>

            <div class="metric-card completion">
                <div class="metric-icon">🏆</div>
                <div class="metric-content">
                    <h3>Completion Rate</h3>
                    <div class="metric-value">{{ number_format(($stats['completed_enrollments'] / max($stats['total_enrollments'], 1)) * 100, 1) }}%</div>
                    <div class="metric-change">
                        <span class="change-icon">📊</span>
                        <span class="change-text">{{ $stats['completed_enrollments'] }} completed</span>
                    </div>
                </div>
            </div>

            <div class="metric-card progress">
                <div class="metric-icon">📊</div>
                <div class="metric-content">
                    <h3>Avg Progress</h3>
                    <div class="metric-value">{{ number_format($stats['average_progress'], 1) }}%</div>
                    <div class="metric-change">
                        <span class="change-icon">⏱️</span>
                        <span class="change-text">Across all students</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3>Enrollment Trends</h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" data-period="7">7 Days</button>
                        <button class="chart-btn" data-period="30">30 Days</button>
                        <button class="chart-btn" data-period="90">90 Days</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="enrollmentChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3>Progress Distribution</h3>
                </div>
                <div class="progress-distribution">
                    <div class="progress-segment">
                        <div class="segment-bar">
                            <div class="segment-fill" style="width: 25%; background: #ef4444;"></div>
                        </div>
                        <div class="segment-info">
                            <span class="segment-label">0-25%</span>
                            <span class="segment-count">{{ $enrollments->where('progress', '<=', 25)->count() }} students</span>
                        </div>
                    </div>
                    
                    <div class="progress-segment">
                        <div class="segment-bar">
                            <div class="segment-fill" style="width: 35%; background: #f59e0b;"></div>
                        </div>
                        <div class="segment-info">
                            <span class="segment-label">26-50%</span>
                            <span class="segment-count">{{ $enrollments->whereBetween('progress', [26, 50])->count() }} students</span>
                        </div>
                    </div>
                    
                    <div class="progress-segment">
                        <div class="segment-bar">
                            <div class="segment-fill" style="width: 25%; background: #3b82f6;"></div>
                        </div>
                        <div class="segment-info">
                            <span class="segment-label">51-75%</span>
                            <span class="segment-count">{{ $enrollments->whereBetween('progress', [51, 75])->count() }} students</span>
                        </div>
                    </div>
                    
                    <div class="progress-segment">
                        <div class="segment-bar">
                            <div class="segment-fill" style="width: 15%; background: #22c55e;"></div>
                        </div>
                        <div class="segment-info">
                            <span class="segment-label">76-100%</span>
                            <span class="segment-count">{{ $enrollments->where('progress', '>=', 76)->count() }} students</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Management -->
        <div class="students-section">
            <div class="section-header">
                <h2>Student Management</h2>
                <div class="section-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Search students..." id="studentSearch">
                        <span class="search-icon">🔍</span>
                    </div>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Students</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    <button type="button" class="btn btn-secondary" onclick="exportStudents()">
                        📊 Export Data
                    </button>
                </div>
            </div>

            <div class="students-table">
                <div class="table-header">
                    <div class="header-cell">Student</div>
                    <div class="header-cell">Enrolled</div>
                    <div class="header-cell">Progress</div>
                    <div class="header-cell">Last Activity</div>
                    <div class="header-cell">Status</div>
                    <div class="header-cell">Actions</div>
                </div>

                @forelse($enrollments as $enrollment)
                    <div class="table-row" data-student-name="{{ strtolower($enrollment->user->name) }}">
                        <div class="table-cell student-info">
                            <div class="student-avatar">
                                @if($enrollment->user->avatar)
                                    <img src="{{ Storage::url($enrollment->user->avatar) }}" alt="{{ $enrollment->user->name }}">
                                @else
                                    <div class="avatar-placeholder">
                                        {{ strtoupper(substr($enrollment->user->name, 0, 2)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="student-details">
                                <h4>{{ $enrollment->user->name }}</h4>
                                <p>{{ $enrollment->user->email }}</p>
                            </div>
                        </div>

                        <div class="table-cell">
                            <span class="enrollment-date">{{ $enrollment->enrolled_at->format('M j, Y') }}</span>
                            <span class="enrollment-type">{{ ucfirst($enrollment->enrollment_type) }}</span>
                        </div>

                        <div class="table-cell">
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ $enrollment->progress ?? 0 }}%"></div>
                                </div>
                                <span class="progress-text">{{ number_format($enrollment->progress ?? 0, 0) }}%</span>
                            </div>
                        </div>

                        <div class="table-cell">
                            @if($enrollment->last_accessed_at)
                                <span class="last-activity">{{ $enrollment->last_accessed_at->diffForHumans() }}</span>
                            @else
                                <span class="no-activity">Never accessed</span>
                            @endif
                        </div>

                        <div class="table-cell">
                            <span class="status-badge status-{{ $enrollment->status }}">
                                @if($enrollment->completed_at)
                                    🏆 Completed
                                @elseif($enrollment->progress > 0)
                                    ⏱️ In Progress
                                @else
                                    📝 Not Started
                                @endif
                            </span>
                        </div>

                        <div class="table-cell">
                            <div class="action-buttons">
                                <button type="button" class="btn-icon" onclick="viewStudent({{ $enrollment->user->id }})" title="View Profile">
                                    👁️
                                </button>
                                <button type="button" class="btn-icon" onclick="messageStudent({{ $enrollment->user->id }})" title="Send Message">
                                    💬
                                </button>
                                @if($enrollment->completed_at && $course->certificate_enabled)
                                    <button type="button" class="btn-icon" onclick="viewCertificate({{ $enrollment->id }})" title="View Certificate">
                                        🏆
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-students">
                        <div class="empty-icon">👥</div>
                        <h3>No Students Yet</h3>
                        <p>Students will appear here once they enroll in your course.</p>
                    </div>
                @endforelse
            </div>

            @if($enrollments->hasPages())
                <div class="pagination-wrapper">
                    {{ $enrollments->links() }}
                </div>
            @endif
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="actions-grid">
                <button type="button" class="action-card" onclick="sendAnnouncement()">
                    <div class="action-icon">📢</div>
                    <h4>Send Announcement</h4>
                    <p>Notify all students about updates</p>
                </button>
                
                <button type="button" class="action-card" onclick="createAssignment()">
                    <div class="action-icon">📝</div>
                    <h4>Create Assignment</h4>
                    <p>Add new assignments or quizzes</p>
                </button>
                
                <button type="button" class="action-card" onclick="scheduleSession()">
                    <div class="action-icon">📅</div>
                    <h4>Schedule Live Session</h4>
                    <p>Plan interactive sessions with students</p>
                </button>
                
                <button type="button" class="action-card" onclick="viewFeedback()">
                    <div class="action-icon">⭐</div>
                    <h4>View Feedback</h4>
                    <p>Check student reviews and ratings</p>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.course-analytics-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.analytics-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.course-info p {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.course-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-published {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-draft {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.created-date {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.metric-icon {
    font-size: 3rem;
}

.metric-content h3 {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.metric-value {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.change-text {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 500;
}

.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #a0a0a0;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.chart-container {
    height: 300px;
}

.progress-distribution {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.progress-segment {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.segment-bar {
    flex: 1;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.segment-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.segment-info {
    display: flex;
    flex-direction: column;
    min-width: 100px;
}

.segment-label {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.segment-count {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.students-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.students-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-weight: 600;
    color: #ffffff;
    font-size: 0.875rem;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    align-items: center;
    transition: all 0.3s ease;
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.student-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
}

.student-details h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.student-details p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.progress-text {
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 35px;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.btn-icon:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.quick-actions {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.quick-actions h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.action-card h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.action-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

@media (max-width: 768px) {
    .course-analytics-container {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .metrics-overview {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .section-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Search functionality
document.getElementById('studentSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('.table-row');
    
    rows.forEach(row => {
        const studentName = row.dataset.studentName;
        if (studentName.includes(searchTerm)) {
            row.style.display = 'grid';
        } else {
            row.style.display = 'none';
        }
    });
});

// Action functions
function viewStudent(userId) {
    console.log('View student:', userId);
    // Implementation for viewing student profile
}

function messageStudent(userId) {
    console.log('Message student:', userId);
    // Implementation for messaging student
}

function viewCertificate(enrollmentId) {
    console.log('View certificate:', enrollmentId);
    // Implementation for viewing certificate
}

function exportStudents() {
    console.log('Export students data');
    // Implementation for exporting student data
}

function sendAnnouncement() {
    console.log('Send announcement');
    // Implementation for sending announcements
}

function createAssignment() {
    console.log('Create assignment');
    // Implementation for creating assignments
}

function scheduleSession() {
    console.log('Schedule session');
    // Implementation for scheduling live sessions
}

function viewFeedback() {
    console.log('View feedback');
    // Implementation for viewing feedback
}

// Chart initialization would go here
console.log('Course analytics loaded');
</script>
@endsection
