// Course Detail JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeCourseDetail();
});

function initializeCourseDetail() {
    // Setup tabs
    setupTabs();
    
    // Setup video preview
    setupVideoPreview();
    
    // Setup enrollment
    setupEnrollment();
    
    // Setup lesson interactions
    setupLessonInteractions();
    
    // Setup reviews
    setupReviews();
    
    console.log('Course detail page initialized');
}

function setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Track tab interaction
            trackEvent('course_tab_viewed', { tab: targetTab });
        });
    });
}

function setupVideoPreview() {
    const playButton = document.querySelector('.play-button');
    
    if (playButton) {
        playButton.addEventListener('click', function() {
            const videoUrl = this.getAttribute('data-video');
            if (videoUrl) {
                openVideoModal(videoUrl);
                trackEvent('course_preview_played');
            }
        });
    }
}

function openVideoModal(videoUrl) {
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'video-modal';
    modal.innerHTML = `
        <div class="video-modal-content">
            <button class="video-modal-close">&times;</button>
            <div class="video-container">
                <iframe src="${videoUrl}" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    `;
    
    // Add styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;
    
    const modalContent = modal.querySelector('.video-modal-content');
    modalContent.style.cssText = `
        position: relative;
        width: 90%;
        max-width: 800px;
        background: #000;
        border-radius: 12px;
        overflow: hidden;
    `;
    
    const closeButton = modal.querySelector('.video-modal-close');
    closeButton.style.cssText = `
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 2rem;
        cursor: pointer;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    `;
    
    const videoContainer = modal.querySelector('.video-container');
    videoContainer.style.cssText = `
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%;
    `;
    
    const iframe = modal.querySelector('iframe');
    iframe.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal functionality
    closeButton.addEventListener('click', closeVideoModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeVideoModal();
        }
    });
    
    function closeVideoModal() {
        document.body.removeChild(modal);
        document.body.style.overflow = '';
    }
}

function setupEnrollment() {
    const enrollButton = document.querySelector('form[action*="enroll"] button');
    
    if (enrollButton) {
        enrollButton.addEventListener('click', function(e) {
            // Add loading state
            this.textContent = 'Processing...';
            this.disabled = true;
            
            // Track enrollment attempt
            trackEvent('course_enrollment_attempted');
        });
    }
}

function setupLessonInteractions() {
    const lessonItems = document.querySelectorAll('.lesson-item');
    
    lessonItems.forEach(lesson => {
        if (lesson.classList.contains('accessible')) {
            lesson.addEventListener('click', function() {
                const lessonTitle = this.querySelector('.lesson-title').textContent;
                trackEvent('lesson_clicked', { lesson: lessonTitle });
            });
        }
    });
    
    // Lesson progress tracking
    const watchButtons = document.querySelectorAll('.lesson-item .btn');
    watchButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const lessonItem = this.closest('.lesson-item');
            const lessonTitle = lessonItem.querySelector('.lesson-title').textContent;
            
            // Mark as watched (visual feedback)
            this.textContent = 'Watched';
            this.classList.remove('btn-primary');
            this.classList.add('btn-success');
            
            // Add checkmark to lesson
            const lessonNumber = lessonItem.querySelector('.lesson-number');
            lessonNumber.innerHTML = '✓';
            lessonNumber.style.background = '#10b981';
            lessonNumber.style.color = 'white';
            
            trackEvent('lesson_completed', { lesson: lessonTitle });
        });
    });
}

function setupReviews() {
    // Star rating interaction
    const stars = document.querySelectorAll('.rating-input .star');
    
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            updateStarRating(rating);
            submitRating(rating);
        });
        
        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });
    });
    
    const ratingContainer = document.querySelector('.rating-input');
    if (ratingContainer) {
        ratingContainer.addEventListener('mouseleave', function() {
            const currentRating = this.getAttribute('data-rating') || 0;
            highlightStars(currentRating);
        });
    }
}

function highlightStars(rating) {
    const stars = document.querySelectorAll('.rating-input .star');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.add('highlighted');
        } else {
            star.classList.remove('highlighted');
        }
    });
}

function updateStarRating(rating) {
    const ratingContainer = document.querySelector('.rating-input');
    if (ratingContainer) {
        ratingContainer.setAttribute('data-rating', rating);
        highlightStars(rating);
    }
}

function submitRating(rating) {
    const courseId = document.querySelector('[data-course-id]')?.getAttribute('data-course-id');
    
    if (!courseId) return;
    
    fetch(`/courses/${courseId}/rate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ rating: rating })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Thank you for rating this course!', 'success');
            updateCourseRating(data.newRating, data.ratingCount);
        }
    })
    .catch(error => {
        console.error('Rating error:', error);
        showNotification('Failed to submit rating. Please try again.', 'error');
    });
}

function updateCourseRating(newRating, ratingCount) {
    const ratingElement = document.querySelector('.average-rating');
    const countElement = document.querySelector('.review-count');
    
    if (ratingElement) {
        ratingElement.textContent = newRating.toFixed(1);
    }
    
    if (countElement) {
        countElement.textContent = `(${ratingCount} reviews)`;
    }
}

// Course progress tracking
function updateProgress(lessonId, completed = true) {
    fetch(`/lessons/${lessonId}/progress`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ completed: completed })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateProgressBar(data.progress);
            
            if (data.courseCompleted) {
                showCourseCompletionModal();
            }
        }
    })
    .catch(error => {
        console.error('Progress update error:', error);
    });
}

function updateProgressBar(progress) {
    const progressFill = document.querySelector('.progress-fill');
    const progressPercentage = document.querySelector('.progress-percentage');
    
    if (progressFill) {
        progressFill.style.width = progress + '%';
    }
    
    if (progressPercentage) {
        progressPercentage.textContent = Math.round(progress) + '%';
    }
}

function showCourseCompletionModal() {
    const modal = document.createElement('div');
    modal.className = 'completion-modal';
    modal.innerHTML = `
        <div class="completion-modal-content">
            <div class="completion-icon">🎉</div>
            <h2>Congratulations!</h2>
            <p>You have successfully completed this course!</p>
            <div class="completion-actions">
                <button class="btn btn-primary" onclick="downloadCertificate()">Download Certificate</button>
                <button class="btn btn-secondary" onclick="closeCompletionModal()">Continue</button>
            </div>
        </div>
    `;
    
    // Add styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;
    
    document.body.appendChild(modal);
    trackEvent('course_completed');
}

function closeCompletionModal() {
    const modal = document.querySelector('.completion-modal');
    if (modal) {
        modal.remove();
    }
}

function downloadCertificate() {
    const courseId = document.querySelector('[data-course-id]')?.getAttribute('data-course-id');
    if (courseId) {
        window.open(`/courses/${courseId}/certificate`, '_blank');
        trackEvent('certificate_downloaded');
    }
}

// Utility functions
function trackEvent(eventName, properties = {}) {
    console.log('Event tracked:', eventName, properties);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Add course detail specific styles
const courseDetailStyles = document.createElement('style');
courseDetailStyles.textContent = `
    .rating-input {
        display: flex;
        gap: 0.25rem;
        margin: 1rem 0;
    }
    
    .rating-input .star {
        font-size: 1.5rem;
        color: #666666;
        cursor: pointer;
        transition: color 0.3s ease;
    }
    
    .rating-input .star.highlighted,
    .rating-input .star:hover {
        color: #fbbf24;
    }
    
    .completion-modal-content {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 3rem;
        text-align: center;
        color: white;
        max-width: 400px;
    }
    
    .completion-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }
    
    .completion-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
`;
document.head.appendChild(courseDetailStyles);
