<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Community - <?php echo e(config('app.name')); ?></title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/community.css')); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="<?php echo e(route('home')); ?>">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                <li><a href="<?php echo e(route('communities.index')); ?>" class="active">Community</a></li>
                <li><a href="<?php echo e(route('chat.index')); ?>">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="community-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Join The Brotherhood</h1>
                    <p>Connect with like-minded individuals on the path to success</p>
                </div>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create communities')): ?>
                    <div class="header-actions">
                        <a href="<?php echo e(route('communities.create')); ?>" class="btn btn-primary">
                            Create Community
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Community Grid -->
            <div class="community-layout">
                <div class="community-content">
                    <!-- Filters -->
                    <div class="filters-section">
                        <form method="GET" action="<?php echo e(route('communities.index')); ?>" class="filters-form">
                            <div class="filter-group">
                                <input type="text" name="search" placeholder="Search communities..." 
                                       value="<?php echo e(request('search')); ?>" class="search-input">
                            </div>
                            
                            <div class="filter-group">
                                <select name="type" class="filter-select">
                                    <option value="">All Types</option>
                                    <option value="public" <?php echo e(request('type') == 'public' ? 'selected' : ''); ?>>
                                        Public
                                    </option>
                                    <option value="course-specific" <?php echo e(request('type') == 'course-specific' ? 'selected' : ''); ?>>
                                        Course Specific
                                    </option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="<?php echo e(route('communities.index')); ?>" class="btn btn-secondary">Clear</a>
                        </form>
                    </div>

                    <!-- Communities Grid -->
                    <div class="communities-grid">
                        <?php $__empty_1 = true; $__currentLoopData = $communities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $community): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="community-card">
                                <div class="community-header">
                                    <?php if($community->banner): ?>
                                        <img src="<?php echo e(Storage::url($community->banner)); ?>" alt="<?php echo e($community->name); ?>" class="community-banner">
                                    <?php else: ?>
                                        <div class="community-banner-placeholder">
                                            <span class="community-icon">👥</span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="community-type">
                                        <span class="type-badge type-<?php echo e($community->type); ?>">
                                            <?php echo e(ucfirst(str_replace('-', ' ', $community->type))); ?>

                                        </span>
                                    </div>
                                </div>
                                
                                <div class="community-content">
                                    <h3 class="community-title">
                                        <a href="<?php echo e(route('communities.show', $community)); ?>"><?php echo e($community->name); ?></a>
                                    </h3>
                                    
                                    <p class="community-description"><?php echo e(Str::limit($community->description, 120)); ?></p>
                                    
                                    <?php if($community->course): ?>
                                        <div class="community-course">
                                            <span class="course-label">Course:</span>
                                            <a href="<?php echo e(route('courses.show', $community->course)); ?>" class="course-link">
                                                <?php echo e($community->course->title); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="community-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value"><?php echo e($community->member_count); ?> members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value"><?php echo e($community->post_count); ?> posts</span>
                                        </div>
                                    </div>
                                    
                                    <div class="community-creator">
                                        <span class="creator-label">Created by</span>
                                        <span class="creator-name"><?php echo e($community->creator->name); ?></span>
                                    </div>
                                </div>
                                
                                <div class="community-actions">
                                    <a href="<?php echo e(route('communities.show', $community)); ?>" class="btn btn-primary btn-block">
                                        View Community
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="empty-state">
                                <div class="empty-icon">👥</div>
                                <h3>No communities found</h3>
                                <p>Be the first to create a community and start building connections</p>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create communities')): ?>
                                    <a href="<?php echo e(route('communities.create')); ?>" class="btn btn-primary">
                                        Create First Community
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if($communities->hasPages()): ?>
                        <div class="pagination-wrapper">
                            <?php echo e($communities->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="community-sidebar">
                    <!-- Popular Communities -->
                    <div class="sidebar-card">
                        <h4>Popular Communities</h4>
                        <div class="popular-communities">
                            <?php $__empty_1 = true; $__currentLoopData = $popularCommunities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $popular): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="popular-item">
                                    <div class="popular-info">
                                        <h5><a href="<?php echo e(route('communities.show', $popular)); ?>"><?php echo e($popular->name); ?></a></h5>
                                        <span class="popular-stats"><?php echo e($popular->member_count); ?> members</span>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p class="no-popular">No popular communities yet</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Community Guidelines -->
                    <div class="sidebar-card">
                        <h4>Community Guidelines</h4>
                        <ul class="guidelines-list">
                            <li>🤝 Respect all members</li>
                            <li>💡 Share valuable insights</li>
                            <li>🚫 No spam or self-promotion</li>
                            <li>🎯 Stay on topic</li>
                            <li>💪 Support each other's growth</li>
                        </ul>
                    </div>

                    <!-- Quick Stats -->
                    <div class="sidebar-card">
                        <h4>Community Stats</h4>
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <span class="stat-number"><?php echo e($communities->total()); ?></span>
                                <span class="stat-label">Total Communities</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-number"><?php echo e($popularCommunities->sum('member_count')); ?></span>
                                <span class="stat-label">Active Members</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-number"><?php echo e($popularCommunities->sum('post_count')); ?></span>
                                <span class="stat-label">Total Posts</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/community.js')); ?>"></script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/community/index.blade.php ENDPATH**/ ?>