<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Courses - <?php echo e(config('app.name')); ?></title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/courses.css')); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="<?php echo e(route('home')); ?>">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li><a href="<?php echo e(route('courses.index')); ?>" class="active">Courses</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="#chat">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="courses-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Master Real-World Skills</h1>
                    <p>Choose from 18 different campuses designed to make you money</p>
                </div>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create courses')): ?>
                    <div class="header-actions">
                        <a href="<?php echo e(route('courses.create')); ?>" class="btn btn-primary">
                            Create Course
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" action="<?php echo e(route('courses.index')); ?>" class="filters-form">
                    <div class="filter-group">
                        <input type="text" name="search" placeholder="Search courses..." 
                               value="<?php echo e(request('search')); ?>" class="search-input">
                    </div>
                    
                    <div class="filter-group">
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" 
                                        <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="difficulty" class="filter-select">
                            <option value="">All Levels</option>
                            <option value="beginner" <?php echo e(request('difficulty') == 'beginner' ? 'selected' : ''); ?>>
                                Beginner
                            </option>
                            <option value="intermediate" <?php echo e(request('difficulty') == 'intermediate' ? 'selected' : ''); ?>>
                                Intermediate
                            </option>
                            <option value="advanced" <?php echo e(request('difficulty') == 'advanced' ? 'selected' : ''); ?>>
                                Advanced
                            </option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="price_filter" class="filter-select">
                            <option value="">All Prices</option>
                            <option value="free" <?php echo e(request('price_filter') == 'free' ? 'selected' : ''); ?>>
                                Free
                            </option>
                            <option value="paid" <?php echo e(request('price_filter') == 'paid' ? 'selected' : ''); ?>>
                                Paid
                            </option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <!-- Courses Grid -->
            <div class="courses-grid">
                <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="course-card">
                        <div class="course-thumbnail">
                            <?php if($course->thumbnail): ?>
                                <img src="<?php echo e(Storage::url($course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">🎯</span>
                                </div>
                            <?php endif; ?>
                            <?php if($course->is_free): ?>
                                <div class="course-badge free">FREE</div>
                            <?php else: ?>
                                <div class="course-badge price">$<?php echo e($course->price); ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-content">
                            <div class="course-meta">
                                <span class="course-category"><?php echo e($course->category->name); ?></span>
                                <span class="course-level"><?php echo e(ucfirst($course->difficulty_level)); ?></span>
                            </div>
                            
                            <h3 class="course-title">
                                <a href="<?php echo e(route('courses.show', $course)); ?>"><?php echo e($course->title); ?></a>
                            </h3>
                            
                            <p class="course-description"><?php echo e($course->short_description); ?></p>
                            
                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-icon">👥</span>
                                    <span class="stat-value"><?php echo e($course->total_students); ?></span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">📚</span>
                                    <span class="stat-value"><?php echo e($course->total_lessons); ?> lessons</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-value"><?php echo e(number_format($course->rating, 1)); ?></span>
                                </div>
                            </div>
                            
                            <div class="course-instructor">
                                <span class="instructor-label">By</span>
                                <span class="instructor-name"><?php echo e($course->instructor->name); ?></span>
                            </div>
                        </div>
                        
                        <div class="course-actions">
                            <a href="<?php echo e(route('courses.show', $course)); ?>" class="btn btn-primary btn-block">
                                View Course
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📚</div>
                        <h3>No courses found</h3>
                        <p>Try adjusting your filters or search terms</p>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create courses')): ?>
                            <a href="<?php echo e(route('courses.create')); ?>" class="btn btn-primary">
                                Create First Course
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if($courses->hasPages()): ?>
                <div class="pagination-wrapper">
                    <?php echo e($courses->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/courses.js')); ?>"></script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/course/index.blade.php ENDPATH**/ ?>