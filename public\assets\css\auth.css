/* Authentication Styles - The Real World Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.guest-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #0f0f0f;
    min-height: 100vh;
    color: #ffffff;
    position: relative;
}

/* Background Elements */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
}

/* Guest Navigation */
.guest-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .brand-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #ffffff;
}

.brand-icon {
    font-size: 1.5rem;
}

.brand-text {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    color: #a0a0a0;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

/* Guest Main Content */
.guest-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100px 20px 50px;
}

.auth-container {
    width: 100%;
    max-width: 480px;
    padding: 20px;
}

.auth-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.register-card {
    max-width: 600px;
    margin: 0 auto;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
}

.auth-header p {
    font-size: 16px;
    color: #a0a0a0;
}

.auth-form {
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 8px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.12);
}

.form-group input::placeholder {
    color: #666666;
}

.form-group select option {
    background: #1a1a1a;
    color: #ffffff;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #a0a0a0;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    accent-color: #3b82f6;
}

.error-message {
    display: block;
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
}

.form-actions {
    margin: 32px 0 24px 0;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.auth-links {
    text-align: center;
    margin: 16px 0;
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #2563eb;
}

.auth-footer {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: #a0a0a0;
    font-size: 14px;
}

.auth-footer a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #2563eb;
}

/* Responsive Design */
@media (max-width: 640px) {
    .auth-card {
        padding: 24px;
        margin: 20px;
    }
    
    .register-card {
        max-width: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
}

/* Loading Animation */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Guest Footer */
.guest-footer {
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem 0 1.5rem;
    margin-top: auto;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.footer-section p {
    color: #a0a0a0;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #a0a0a0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ffffff;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
    text-align: center;
}

.footer-bottom p {
    color: #666666;
    margin: 0;
    font-size: 0.875rem;
}

/* Responsive Design for Guest Layout */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .brand-text {
        display: none;
    }

    .nav-links {
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .guest-main {
        padding: 80px 15px 30px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
}
