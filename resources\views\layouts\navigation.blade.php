<nav class="main-navigation">
    <div class="nav-container">
        <div class="nav-content">
            <div class="nav-left">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="{{ route('home') }}" class="logo-link">
                        <span class="logo-icon">🌍</span>
                        <span class="logo-text">The Real World</span>
                    </a>
                </div>

                <!-- Main Navigation Links -->
                <div class="nav-links">
                    <a href="{{ route('courses.index') }}" class="nav-link {{ request()->routeIs('courses.*') ? 'active' : '' }}">
                        📚 Courses
                    </a>
                    <a href="{{ route('communities.index') }}" class="nav-link {{ request()->routeIs('communities.*') ? 'active' : '' }}">
                        🏘️ Community
                    </a>
                    <a href="{{ route('chat.index') }}" class="nav-link {{ request()->routeIs('chat.*') ? 'active' : '' }}">
                        💬 Chat
                    </a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            🏠 Dashboard
                        </a>
                    @endauth
                </div>
            </div>

            <!-- Right Side Navigation -->
            <div class="nav-right">
                <!-- Search Bar -->
                <div class="nav-search">
                    <form action="{{ route('search.index') }}" method="GET" class="search-form">
                        <input type="text"
                               name="q"
                               placeholder="Search courses, instructors..."
                               class="search-input"
                               value="{{ request('q') }}">
                        <button type="submit" class="search-btn">🔍</button>
                    </form>
                </div>

                @auth
                    <!-- Notifications -->
                    <div class="nav-notifications">
                        <a href="{{ route('notifications.index') }}" class="notification-btn">
                            <span class="notification-icon">🔔</span>
                            @if(Auth::user()->unreadNotifications->count() > 0)
                                <span class="notification-badge">{{ Auth::user()->unreadNotifications->count() }}</span>
                            @endif
                        </a>
                    </div>

                    <!-- User Dropdown -->
                    <div class="nav-user-dropdown">
                        <button type="button" class="user-dropdown-btn" onclick="toggleUserDropdown()">
                            <div class="user-avatar">
                                @if(Auth::user()->avatar)
                                    <img src="{{ Storage::url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}">
                                @else
                                    <div class="avatar-placeholder">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                                    </div>
                                @endif
                            </div>
                            <span class="user-name">{{ Auth::user()->name }}</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>

                        <div class="user-dropdown-menu" id="userDropdownMenu" style="display: none;">
                            <div class="dropdown-header">
                                <div class="user-info">
                                    <span class="user-name">{{ Auth::user()->name }}</span>
                                    <span class="user-email">{{ Auth::user()->email }}</span>
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>

                            <a href="{{ route('dashboard') }}" class="dropdown-item">
                                <span class="item-icon">🏠</span>
                                Dashboard
                            </a>

                            <a href="{{ route('profile.show') }}" class="dropdown-item">
                                <span class="item-icon">👤</span>
                                Profile
                            </a>

                            <a href="{{ route('course.my-courses') }}" class="dropdown-item">
                                <span class="item-icon">📚</span>
                                My Courses
                            </a>

                            @can('create courses')
                                <div class="dropdown-divider"></div>
                                <a href="{{ route('instructor.dashboard') }}" class="dropdown-item">
                                    <span class="item-icon">👨‍🏫</span>
                                    Instructor Panel
                                </a>
                            @endcan

                            @can('access admin panel')
                                <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                    <span class="item-icon">🛡️</span>
                                    Admin Panel
                                </a>
                            @endcan

                            <div class="dropdown-divider"></div>

                            <form method="POST" action="{{ route('logout') }}" class="dropdown-form">
                                @csrf
                                <button type="submit" class="dropdown-item logout-btn">
                                    <span class="item-icon">🚪</span>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <!-- Guest Links -->
                    <div class="nav-auth">
                        <a href="{{ route('login') }}" class="auth-link login-link">Login</a>
                        <a href="{{ route('register') }}" class="auth-link register-link">Register</a>
                    </div>
                @endauth
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-nav-toggle" onclick="toggleMobileMenu()">
                <span id="hamburger-icon">☰</span>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div class="mobile-nav-menu" id="mobileNavMenu">
        <div class="mobile-nav-links">
            <a href="{{ route('courses.index') }}" class="mobile-nav-link {{ request()->routeIs('courses.*') ? 'active' : '' }}">
                📚 Courses
            </a>
            <a href="{{ route('communities.index') }}" class="mobile-nav-link {{ request()->routeIs('communities.*') ? 'active' : '' }}">
                🏘️ Community
            </a>
            <a href="{{ route('chat.index') }}" class="mobile-nav-link {{ request()->routeIs('chat.*') ? 'active' : '' }}">
                💬 Chat
            </a>
            @auth
                <a href="{{ route('dashboard') }}" class="mobile-nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                    🏠 Dashboard
                </a>
                <a href="{{ route('profile.show') }}" class="mobile-nav-link">
                    👤 Profile
                </a>
                <a href="{{ route('course.my-courses') }}" class="mobile-nav-link">
                    📚 My Courses
                </a>
                @can('create courses')
                    <a href="{{ route('instructor.dashboard') }}" class="mobile-nav-link">
                        👨‍🏫 Instructor Panel
                    </a>
                @endcan
                @can('access admin panel')
                    <a href="{{ route('admin.dashboard') }}" class="mobile-nav-link">
                        🛡️ Admin Panel
                    </a>
                @endcan
            @else
                <a href="{{ route('login') }}" class="mobile-nav-link">
                    🔑 Login
                </a>
                <a href="{{ route('register') }}" class="mobile-nav-link">
                    📝 Register
                </a>
            @endauth
        </div>

        <!-- Mobile Search -->
        <div class="mobile-search">
            <form action="{{ route('search.index') }}" method="GET" class="search-form">
                <input type="text"
                       name="q"
                       placeholder="Search courses, instructors..."
                       class="search-input"
                       value="{{ request('q') }}">
                <button type="submit" class="search-btn">🔍</button>
            </form>
        </div>

        @auth
            <!-- Mobile Logout -->
            <div class="mobile-nav-links" style="border-top: 1px solid var(--dark-border); padding-top: 1rem;">
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="mobile-nav-link" style="width: 100%; text-align: left; background: transparent; border: none; color: var(--error-color);">
                        🚪 Logout
                    </button>
                </form>
            </div>
        @endauth
    </div>
</nav>

<script>
    // User dropdown functionality
    function toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdownMenu');
        const isVisible = dropdown.style.display === 'block';
        dropdown.style.display = isVisible ? 'none' : 'block';
    }

    // Mobile menu functionality
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobileNavMenu');
        const hamburgerIcon = document.getElementById('hamburger-icon');
        const isActive = mobileMenu.classList.contains('active');

        if (isActive) {
            mobileMenu.classList.remove('active');
            hamburgerIcon.textContent = '☰';
        } else {
            mobileMenu.classList.add('active');
            hamburgerIcon.textContent = '✕';
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('userDropdownMenu');
        const button = document.querySelector('.user-dropdown-btn');
        const mobileMenu = document.getElementById('mobileNavMenu');
        const mobileToggle = document.querySelector('.mobile-nav-toggle');

        // Close user dropdown
        if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.style.display = 'none';
        }

        // Close mobile menu
        if (mobileMenu && mobileToggle && !mobileToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
            mobileMenu.classList.remove('active');
            document.getElementById('hamburger-icon').textContent = '☰';
        }
    });

    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(searchInput => {
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.closest('form').submit();
                }
            });
        });

        // Close mobile menu when clicking on nav links
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                const mobileMenu = document.getElementById('mobileNavMenu');
                mobileMenu.classList.remove('active');
                document.getElementById('hamburger-icon').textContent = '☰';
            });
        });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            const mobileMenu = document.getElementById('mobileNavMenu');
            mobileMenu.classList.remove('active');
            document.getElementById('hamburger-icon').textContent = '☰';
        }
    });
</script>
