<nav class="main-navigation">
    <div class="nav-container">
        <div class="nav-content">
            <div class="nav-left">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="{{ route('home') }}" class="logo-link">
                        <span class="logo-icon">🌍</span>
                        <span class="logo-text">The Real World</span>
                    </a>
                </div>

                <!-- Main Navigation Links -->
                <div class="nav-links">
                    <a href="{{ route('courses.index') }}" class="nav-link {{ request()->routeIs('courses.*') ? 'active' : '' }}">
                        📚 Courses
                    </a>
                    <a href="{{ route('communities.index') }}" class="nav-link {{ request()->routeIs('communities.*') ? 'active' : '' }}">
                        🏘️ Community
                    </a>
                    <a href="{{ route('chat.index') }}" class="nav-link {{ request()->routeIs('chat.*') ? 'active' : '' }}">
                        💬 Chat
                    </a>
                    @auth
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            🏠 Dashboard
                        </a>
                    @endauth
                </div>
            </div>

            <!-- Right Side Navigation -->
            <div class="nav-right">
                <!-- Search Bar -->
                <div class="nav-search">
                    <form action="{{ route('search.index') }}" method="GET" class="search-form">
                        <input type="text"
                               name="q"
                               placeholder="Search courses, instructors..."
                               class="search-input"
                               value="{{ request('q') }}">
                        <button type="submit" class="search-btn">🔍</button>
                    </form>
                </div>

                @auth
                    <!-- Notifications -->
                    <div class="nav-notifications">
                        <a href="{{ route('notifications.index') }}" class="notification-btn">
                            <span class="notification-icon">🔔</span>
                            @if(Auth::user()->unreadNotifications->count() > 0)
                                <span class="notification-badge">{{ Auth::user()->unreadNotifications->count() }}</span>
                            @endif
                        </a>
                    </div>

                    <!-- User Dropdown -->
                    <div class="nav-user-dropdown">
                        <button type="button" class="user-dropdown-btn" onclick="toggleUserDropdown()">
                            <div class="user-avatar">
                                @if(Auth::user()->avatar)
                                    <img src="{{ Storage::url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}">
                                @else
                                    <div class="avatar-placeholder">
                                        {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                                    </div>
                                @endif
                            </div>
                            <span class="user-name">{{ Auth::user()->name }}</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>

                        <div class="user-dropdown-menu" id="userDropdownMenu" style="display: none;">
                            <div class="dropdown-header">
                                <div class="user-info">
                                    <span class="user-name">{{ Auth::user()->name }}</span>
                                    <span class="user-email">{{ Auth::user()->email }}</span>
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>

                            <a href="{{ route('dashboard') }}" class="dropdown-item">
                                <span class="item-icon">🏠</span>
                                Dashboard
                            </a>

                            <a href="{{ route('profile.show') }}" class="dropdown-item">
                                <span class="item-icon">👤</span>
                                Profile
                            </a>

                            <a href="{{ route('course.my-courses') }}" class="dropdown-item">
                                <span class="item-icon">📚</span>
                                My Courses
                            </a>

                            @can('create courses')
                                <div class="dropdown-divider"></div>
                                <a href="{{ route('instructor.dashboard') }}" class="dropdown-item">
                                    <span class="item-icon">👨‍🏫</span>
                                    Instructor Panel
                                </a>
                            @endcan

                            @can('access admin panel')
                                <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                    <span class="item-icon">🛡️</span>
                                    Admin Panel
                                </a>
                            @endcan

                            <div class="dropdown-divider"></div>

                            <form method="POST" action="{{ route('logout') }}" class="dropdown-form">
                                @csrf
                                <button type="submit" class="dropdown-item logout-btn">
                                    <span class="item-icon">🚪</span>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <!-- Guest Links -->
                    <div class="nav-auth">
                        <a href="{{ route('login') }}" class="auth-link login-link">Login</a>
                        <a href="{{ route('register') }}" class="auth-link register-link">Register</a>
                    </div>
                @endauth
            </div>

            <!-- Hamburger -->
            <div class="-mr-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-900 focus:text-gray-500 dark:focus:text-gray-400 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            @auth
                <x-responsive-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                    {{ __('Dashboard') }}
                </x-responsive-nav-link>
            @endauth
        </div>

        <!-- Responsive Settings Options -->
        @auth
            <div class="pt-4 pb-1 border-t border-gray-200 dark:border-gray-600">
                <div class="px-4">
                    <div class="font-medium text-base text-gray-800 dark:text-gray-200">{{ Auth::user()->name }}</div>
                    <div class="font-medium text-sm text-gray-500">{{ Auth::user()->email }}</div>
                </div>

                <div class="mt-3 space-y-1">
                    <x-responsive-nav-link :href="route('profile.show')">
                        {{ __('Profile') }}
                    </x-responsive-nav-link>

                    <!-- Authentication -->
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf

                        <x-responsive-nav-link :href="route('logout')"
                                onclick="event.preventDefault();
                                            this.closest('form').submit();">
                            {{ __('Log Out') }}
                        </x-responsive-nav-link>
                    </form>
                </div>
            </div>
        @else
            <div class="pt-4 pb-1 border-t border-gray-200 dark:border-gray-600">
                <div class="mt-3 space-y-1">
                    <x-responsive-nav-link :href="route('login')">
                        {{ __('Login') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('register')">
                        {{ __('Register') }}
                    </x-responsive-nav-link>
                </div>
            </div>
        @endauth
    </div>
</nav>

<script>
    // User dropdown functionality
    function toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdownMenu');
        const isVisible = dropdown.style.display === 'block';
        dropdown.style.display = isVisible ? 'none' : 'block';
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('userDropdownMenu');
        const button = document.querySelector('.user-dropdown-btn');

        if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.style.display = 'none';
        }
    });

    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.closest('form').submit();
                }
            });
        }
    });
</script>
