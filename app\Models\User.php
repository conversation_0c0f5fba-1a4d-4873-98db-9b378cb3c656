<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Cashier\Billable;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, Billable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'first_name',
        'last_name',
        'phone',
        'avatar',
        'bio',
        'country',
        'timezone',
        'date_of_birth',
        'is_active',
        'last_login_at',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'last_login_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the user's enrolled courses.
     */
    public function enrolledCourses()
    {
        return $this->belongsToMany(Course::class, 'course_enrollments')
                    ->withPivot(['enrolled_at', 'completed_at', 'progress'])
                    ->withTimestamps();
    }

    /**
     * Get the user's created courses (if instructor).
     */
    public function createdCourses()
    {
        return $this->hasMany(Course::class, 'instructor_id');
    }

    /**
     * Get the user's chat messages.
     */
    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    /**
     * Get the user's community posts.
     */
    public function communityPosts()
    {
        return $this->hasMany(CommunityPost::class);
    }

    /**
     * Get the user's achievements.
     */
    public function achievements()
    {
        return $this->belongsToMany(Achievement::class, 'user_achievements')
                    ->withPivot(['earned_at'])
                    ->withTimestamps();
    }

    /**
     * Get the communities the user is a member of.
     */
    public function communities()
    {
        return $this->belongsToMany(Community::class, 'community_members')
                    ->withPivot(['role', 'joined_at', 'last_active_at'])
                    ->withTimestamps();
    }

    /**
     * Get the chat rooms the user is a member of.
     */
    public function chatRooms()
    {
        return $this->belongsToMany(ChatRoom::class, 'chat_room_members')
                    ->withPivot(['role', 'joined_at', 'last_read_at', 'is_muted'])
                    ->withTimestamps();
    }

    /**
     * Get the communities created by the user.
     */
    public function createdCommunities()
    {
        return $this->hasMany(Community::class, 'created_by');
    }

    /**
     * Get the chat rooms created by the user.
     */
    public function createdChatRooms()
    {
        return $this->hasMany(ChatRoom::class, 'created_by');
    }

    /**
     * Get the user's payments.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }
}
