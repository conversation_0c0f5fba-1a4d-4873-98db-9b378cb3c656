/* Course Detail Page Styles */

.course-hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    padding: 3rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.course-hero-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.course-breadcrumb {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-bottom: 1rem;
}

.course-breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.course-breadcrumb span {
    margin: 0 0.5rem;
}

.course-title {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.course-description {
    font-size: 1.25rem;
    color: #a0a0a0;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.course-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meta-label {
    font-size: 0.875rem;
    color: #a0a0a0;
    font-weight: 500;
}

.meta-value {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
}

.progress-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    color: #ffffff;
    font-weight: 600;
}

.progress-percentage {
    color: #3b82f6;
    font-size: 1.125rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Course Sidebar */
.course-sidebar {
    position: sticky;
    top: 2rem;
}

.course-preview {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.course-preview img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.course-placeholder {
    width: 100%;
    height: 250px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-preview:hover .preview-overlay {
    opacity: 1;
}

.play-button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: #1a1a1a;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.05);
}

.course-pricing {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.price.free {
    color: #10b981;
}

.price:not(.free) {
    color: #3b82f6;
}

.course-includes {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.course-includes h4 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.course-includes ul {
    list-style: none;
    padding: 0;
}

.course-includes li {
    color: #a0a0a0;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

/* Course Content */
.course-content {
    padding: 3rem 0;
}

.content-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 2rem;
    color: #a0a0a0;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #ffffff;
    border-bottom-color: #3b82f6;
}

.tab-button:hover {
    color: #ffffff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview Tab */
.overview-section {
    margin-bottom: 3rem;
}

.overview-section h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.course-full-description {
    color: #a0a0a0;
    line-height: 1.6;
    font-size: 1rem;
}

.learning-objectives,
.requirements-list {
    list-style: none;
    padding: 0;
}

.learning-objectives li,
.requirements-list li {
    color: #a0a0a0;
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

/* Curriculum Tab */
.curriculum-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.curriculum-header h3 {
    color: #ffffff;
    font-size: 1.5rem;
}

.lesson-count {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.lessons-list {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.lesson-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-item.accessible:hover {
    background: rgba(255, 255, 255, 0.05);
}

.lesson-item.locked {
    opacity: 0.6;
}

.lesson-number {
    width: 40px;
    height: 40px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    font-weight: 600;
    flex-shrink: 0;
}

.lesson-content {
    flex: 1;
}

.lesson-title {
    color: #ffffff;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.lesson-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.lesson-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #666666;
}

.lesson-actions {
    flex-shrink: 0;
}

.locked-icon {
    font-size: 1.25rem;
    color: #666666;
}

/* Reviews Tab */
.reviews-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.reviews-header h3 {
    color: #ffffff;
    font-size: 1.5rem;
}

.rating-summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.average-rating {
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
}

.stars {
    display: flex;
    gap: 0.125rem;
}

.star {
    font-size: 1rem;
    color: #666666;
}

.star.filled {
    color: #fbbf24;
}

.review-count {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.review-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reviewer-name {
    color: #ffffff;
    font-weight: 600;
}

.review-rating {
    display: flex;
    gap: 0.125rem;
    margin-top: 0.25rem;
}

.review-date {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.review-text {
    color: #a0a0a0;
    line-height: 1.6;
}

/* Related Courses */
.related-courses {
    padding: 3rem 0;
    background: rgba(255, 255, 255, 0.02);
}

.related-courses h3 {
    color: #ffffff;
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .course-hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .course-sidebar {
        position: static;
    }
    
    .course-meta {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .course-title {
        font-size: 2rem;
    }
    
    .content-tabs {
        flex-wrap: wrap;
    }
    
    .tab-button {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .lesson-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .lesson-actions {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .course-hero {
        padding: 2rem 0;
    }
    
    .course-title {
        font-size: 1.75rem;
    }
    
    .course-description {
        font-size: 1rem;
    }
    
    .price {
        font-size: 2rem;
    }
}
