@extends('layouts.admin')

@section('title', 'Payment Details')

@section('content')
<div class="payment-details">
    <!-- Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>💳 Payment Details</h1>
                <p>Transaction ID: {{ $payment->transaction_id }}</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('admin.payments.index') }}" class="btn btn-outline">
                    ← Back to Payments
                </a>
                @if($payment->status === 'completed' && $payment->canRefund())
                    <button type="button" class="btn btn-danger" onclick="showRefundModal()">
                        💸 Process Refund
                    </button>
                @endif
            </div>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="payment-info">
        <div class="info-grid">
            <!-- Transaction Details -->
            <div class="info-card">
                <h3>💳 Transaction Details</h3>
                <div class="detail-row">
                    <span class="label">Transaction ID:</span>
                    <span class="value">{{ $payment->transaction_id }}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Amount:</span>
                    <span class="value amount">${{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Payment Method:</span>
                    <span class="value">{{ ucfirst($payment->payment_method) }}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value">
                        <span class="status-badge {{ $payment->status }}">
                            @switch($payment->status)
                                @case('completed')
                                    ✅ Completed
                                    @break
                                @case('pending')
                                    ⏳ Pending
                                    @break
                                @case('failed')
                                    ❌ Failed
                                    @break
                                @case('refunded')
                                    💸 Refunded
                                    @break
                                @default
                                    📋 {{ ucfirst($payment->status) }}
                            @endswitch
                        </span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="label">Created:</span>
                    <span class="value">{{ $payment->created_at->format('M j, Y \a\t g:i A') }}</span>
                </div>
                @if($payment->completed_at)
                    <div class="detail-row">
                        <span class="label">Completed:</span>
                        <span class="value">{{ $payment->completed_at->format('M j, Y \a\t g:i A') }}</span>
                    </div>
                @endif
            </div>

            <!-- Customer Information -->
            <div class="info-card">
                <h3>👤 Customer Information</h3>
                <div class="customer-info">
                    <div class="customer-avatar">
                        @if($payment->user->avatar)
                            <img src="{{ Storage::url($payment->user->avatar) }}" alt="{{ $payment->user->name }}">
                        @else
                            <div class="avatar-placeholder">
                                {{ strtoupper(substr($payment->user->name, 0, 2)) }}
                            </div>
                        @endif
                    </div>
                    <div class="customer-details">
                        <h4>{{ $payment->user->name }}</h4>
                        <p>{{ $payment->user->email }}</p>
                        @if($payment->user->phone)
                            <p>📞 {{ $payment->user->phone }}</p>
                        @endif
                        @if($payment->user->location)
                            <p>📍 {{ $payment->user->location }}</p>
                        @endif
                    </div>
                </div>
                <div class="customer-actions">
                    <a href="{{ route('admin.users.show', $payment->user) }}" class="btn btn-sm btn-outline">
                        👁️ View Profile
                    </a>
                </div>
            </div>

            <!-- Course Information -->
            <div class="info-card">
                <h3>📚 Course Information</h3>
                <div class="course-info">
                    <div class="course-thumbnail">
                        @if($payment->course->thumbnail)
                            <img src="{{ Storage::url($payment->course->thumbnail) }}" alt="{{ $payment->course->title }}">
                        @else
                            <div class="thumbnail-placeholder">📚</div>
                        @endif
                    </div>
                    <div class="course-details">
                        <h4>{{ $payment->course->title }}</h4>
                        <p>by {{ $payment->course->instructor->name }}</p>
                        <p>{{ $payment->course->category->name }}</p>
                        <div class="course-meta">
                            <span class="meta-item">⭐ {{ number_format($payment->course->rating ?? 0, 1) }}</span>
                            <span class="meta-item">👥 {{ $payment->course->enrollments_count ?? 0 }} students</span>
                            <span class="meta-item">💰 ${{ number_format($payment->course->price, 2) }}</span>
                        </div>
                    </div>
                </div>
                <div class="course-actions">
                    <a href="{{ route('courses.show', $payment->course) }}" class="btn btn-sm btn-outline">
                        👁️ View Course
                    </a>
                </div>
            </div>

            <!-- Payment Gateway Details -->
            @if($payment->gateway_response)
                <div class="info-card">
                    <h3>🔗 Gateway Response</h3>
                    <div class="gateway-details">
                        <pre>{{ json_encode($payment->gateway_response, JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </div>
            @endif

            <!-- Refund Information -->
            @if($payment->status === 'refunded')
                <div class="info-card refund-info">
                    <h3>💸 Refund Information</h3>
                    <div class="detail-row">
                        <span class="label">Refund Amount:</span>
                        <span class="value amount">${{ number_format($payment->refund_amount, 2) }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Refund Reason:</span>
                        <span class="value">{{ $payment->refund_reason }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Refunded At:</span>
                        <span class="value">{{ $payment->refunded_at->format('M j, Y \a\t g:i A') }}</span>
                    </div>
                    @if($payment->refundedBy)
                        <div class="detail-row">
                            <span class="label">Refunded By:</span>
                            <span class="value">{{ $payment->refundedBy->name }}</span>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>

    <!-- Payment Timeline -->
    <div class="payment-timeline">
        <h3>📅 Payment Timeline</h3>
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-marker created"></div>
                <div class="timeline-content">
                    <h4>Payment Created</h4>
                    <p>{{ $payment->created_at->format('M j, Y \a\t g:i A') }}</p>
                    <small>Payment initiated by {{ $payment->user->name }}</small>
                </div>
            </div>

            @if($payment->completed_at)
                <div class="timeline-item">
                    <div class="timeline-marker completed"></div>
                    <div class="timeline-content">
                        <h4>Payment Completed</h4>
                        <p>{{ $payment->completed_at->format('M j, Y \a\t g:i A') }}</p>
                        <small>Payment successfully processed</small>
                    </div>
                </div>
            @endif

            @if($payment->refunded_at)
                <div class="timeline-item">
                    <div class="timeline-marker refunded"></div>
                    <div class="timeline-content">
                        <h4>Payment Refunded</h4>
                        <p>{{ $payment->refunded_at->format('M j, Y \a\t g:i A') }}</p>
                        <small>Refund of ${{ number_format($payment->refund_amount, 2) }} processed</small>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Refund Modal -->
@if($payment->status === 'completed')
    <div class="modal" id="refundModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>💸 Process Refund</h3>
                <button type="button" class="close-btn" onclick="closeRefundModal()">✕</button>
            </div>
            <form method="POST" action="{{ route('admin.payments.refund', $payment) }}">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="refund_amount">Refund Amount</label>
                        <input type="number" 
                               id="refund_amount" 
                               name="refund_amount" 
                               step="0.01" 
                               min="0.01" 
                               max="{{ $payment->amount }}" 
                               value="{{ $payment->amount }}"
                               class="form-control" 
                               required>
                        <small class="form-text">Maximum refund amount: ${{ number_format($payment->amount, 2) }}</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="refund_reason">Refund Reason</label>
                        <textarea id="refund_reason" 
                                  name="refund_reason" 
                                  class="form-control" 
                                  rows="3" 
                                  placeholder="Please provide a reason for the refund..."
                                  required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeRefundModal()">Cancel</button>
                    <button type="submit" class="btn btn-danger">Process Refund</button>
                </div>
            </form>
        </div>
    </div>
@endif

<style>
.payment-details {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.payment-info {
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
}

.info-card h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.label {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
}

.value {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
}

.value.amount {
    color: #22c55e;
    font-size: 1.125rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-badge.failed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-badge.refunded {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}

.customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.customer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
}

.customer-details h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.customer-details p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.course-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.course-thumbnail {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 80px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    border-radius: 8px;
}

.course-details h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.course-details p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.meta-item {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.gateway-details pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.75rem;
    overflow-x: auto;
}

.refund-info {
    border-left: 4px solid #ef4444;
}

.payment-timeline {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.payment-timeline h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: rgba(255, 255, 255, 0.2);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.timeline-marker.created {
    background: #3b82f6;
    border-color: #3b82f6;
}

.timeline-marker.completed {
    background: #22c55e;
    border-color: #22c55e;
}

.timeline-marker.refunded {
    background: #ef4444;
    border-color: #ef4444;
}

.timeline-content h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.timeline-content p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.timeline-content small {
    color: #6b7280;
    font-size: 0.75rem;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.modal-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-text {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .payment-details {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .customer-info,
    .course-info {
        flex-direction: column;
        text-align: center;
    }
    
    .course-meta {
        justify-content: center;
    }
}
</style>

<script>
function showRefundModal() {
    document.getElementById('refundModal').style.display = 'flex';
}

function closeRefundModal() {
    document.getElementById('refundModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('refundModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        closeRefundModal();
    }
});
</script>
@endsection
