@extends('layouts.app')

@section('title', 'Create New Lesson - ' . $course->title)

@section('content')
<div class="container">
    <div class="create-lesson-container">
        <!-- Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="course-info">
                    <h1>Create New Lesson</h1>
                    <p>Add engaging content to <strong>{{ $course->title }}</strong></p>
                </div>
                <div class="header-actions">
                    <a href="{{ route('courses.lessons.index', $course) }}" class="btn btn-secondary">
                        ← Back to Lessons
                    </a>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('courses.lessons.store', $course) }}" enctype="multipart/form-data" class="create-lesson-form">
            @csrf

            <!-- Basic Information -->
            <div class="form-section">
                <h2>📝 Basic Information</h2>
                
                <div class="form-group">
                    <label for="title" class="form-label">Lesson Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="form-control @error('title') is-invalid @enderror" 
                           value="{{ old('title') }}" 
                           placeholder="Enter a clear, descriptive lesson title"
                           required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Lesson Description</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control @error('description') is-invalid @enderror" 
                              rows="4" 
                              placeholder="Describe what students will learn in this lesson">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="lesson_type" class="form-label">Lesson Type *</label>
                        <select id="lesson_type" name="lesson_type" class="form-control @error('lesson_type') is-invalid @enderror" required onchange="toggleContentSections()">
                            <option value="">Select lesson type</option>
                            <option value="video" {{ old('lesson_type') === 'video' ? 'selected' : '' }}>🎥 Video Lesson</option>
                            <option value="text" {{ old('lesson_type') === 'text' ? 'selected' : '' }}>📝 Text Content</option>
                            <option value="quiz" {{ old('lesson_type') === 'quiz' ? 'selected' : '' }}>❓ Quiz</option>
                            <option value="assignment" {{ old('lesson_type') === 'assignment' ? 'selected' : '' }}>📋 Assignment</option>
                            <option value="live" {{ old('lesson_type') === 'live' ? 'selected' : '' }}>📡 Live Session</option>
                        </select>
                        @error('lesson_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                        <input type="number" 
                               id="duration_minutes" 
                               name="duration_minutes" 
                               class="form-control @error('duration_minutes') is-invalid @enderror" 
                               value="{{ old('duration_minutes') }}" 
                               min="1" 
                               placeholder="15">
                        @error('duration_minutes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Video Content Section -->
            <div class="form-section content-section" id="video-section" style="display: none;">
                <h2>🎥 Video Content</h2>
                
                <div class="form-group">
                    <label for="video_url" class="form-label">Video URL</label>
                    <input type="url" 
                           id="video_url" 
                           name="video_url" 
                           class="form-control @error('video_url') is-invalid @enderror" 
                           value="{{ old('video_url') }}" 
                           placeholder="https://youtube.com/watch?v=... or upload file below">
                    @error('video_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="video_file" class="form-label">Or Upload Video File</label>
                    <div class="file-upload-area" onclick="document.getElementById('video_file').click()">
                        <input type="file" id="video_file" name="video_file" accept="video/*" class="file-input" onchange="previewVideo(this)">
                        <div class="upload-placeholder" id="video-placeholder">
                            <span class="upload-icon">🎥</span>
                            <span class="upload-text">Click to upload video file</span>
                            <span class="upload-hint">MP4, MOV, AVI (max 500MB)</span>
                        </div>
                        <div class="video-preview" id="video-preview" style="display: none;">
                            <video controls width="300"></video>
                            <button type="button" class="remove-file" onclick="removeVideo()">✕</button>
                        </div>
                    </div>
                    @error('video_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Text Content Section -->
            <div class="form-section content-section" id="text-section" style="display: none;">
                <h2>📝 Text Content</h2>
                
                <div class="form-group">
                    <label for="content" class="form-label">Lesson Content *</label>
                    <div class="editor-toolbar">
                        <button type="button" class="editor-btn" onclick="formatText('bold')"><strong>B</strong></button>
                        <button type="button" class="editor-btn" onclick="formatText('italic')"><em>I</em></button>
                        <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                        <button type="button" class="editor-btn" onclick="insertList()">📝</button>
                        <button type="button" class="editor-btn" onclick="insertLink()">🔗</button>
                        <button type="button" class="editor-btn" onclick="insertImage()">🖼️</button>
                    </div>
                    <textarea id="content" 
                              name="content" 
                              class="form-control content-editor @error('content') is-invalid @enderror" 
                              rows="15" 
                              placeholder="Write your lesson content here. You can use markdown formatting.">{{ old('content') }}</textarea>
                    @error('content')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Quiz Section -->
            <div class="form-section content-section" id="quiz-section" style="display: none;">
                <h2>❓ Quiz Content</h2>
                
                <div class="quiz-builder">
                    <div class="quiz-header">
                        <h3>Quiz Questions</h3>
                        <button type="button" class="btn btn-secondary" onclick="addQuestion()">
                            ➕ Add Question
                        </button>
                    </div>
                    
                    <div class="questions-container" id="questions-container">
                        <!-- Questions will be added dynamically -->
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="passing_score" class="form-label">Passing Score (%)</label>
                        <input type="number" 
                               id="passing_score" 
                               name="passing_score" 
                               class="form-control" 
                               value="{{ old('passing_score', 70) }}" 
                               min="0" 
                               max="100">
                    </div>
                    <div class="form-group">
                        <label for="max_attempts" class="form-label">Max Attempts</label>
                        <input type="number" 
                               id="max_attempts" 
                               name="max_attempts" 
                               class="form-control" 
                               value="{{ old('max_attempts', 3) }}" 
                               min="1">
                    </div>
                </div>
            </div>

            <!-- Assignment Section -->
            <div class="form-section content-section" id="assignment-section" style="display: none;">
                <h2>📋 Assignment Details</h2>
                
                <div class="form-group">
                    <label for="assignment_instructions" class="form-label">Assignment Instructions *</label>
                    <textarea id="assignment_instructions" 
                              name="assignment_instructions" 
                              class="form-control @error('assignment_instructions') is-invalid @enderror" 
                              rows="8" 
                              placeholder="Provide clear instructions for the assignment">{{ old('assignment_instructions') }}</textarea>
                    @error('assignment_instructions')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="datetime-local" 
                               id="due_date" 
                               name="due_date" 
                               class="form-control" 
                               value="{{ old('due_date') }}">
                    </div>
                    <div class="form-group">
                        <label for="max_points" class="form-label">Max Points</label>
                        <input type="number" 
                               id="max_points" 
                               name="max_points" 
                               class="form-control" 
                               value="{{ old('max_points', 100) }}" 
                               min="1">
                    </div>
                </div>
            </div>

            <!-- Learning Objectives -->
            <div class="form-section">
                <h2>🎯 Learning Objectives</h2>
                
                <div class="objectives-builder">
                    <div class="objectives-header">
                        <p>Define what students will learn from this lesson</p>
                        <button type="button" class="btn btn-secondary" onclick="addObjective()">
                            ➕ Add Objective
                        </button>
                    </div>
                    
                    <div class="objectives-container" id="objectives-container">
                        <div class="objective-item">
                            <input type="text" 
                                   name="learning_objectives[]" 
                                   class="form-control" 
                                   placeholder="Students will be able to..."
                                   value="{{ old('learning_objectives.0') }}">
                            <button type="button" class="btn-remove" onclick="removeObjective(this)">✕</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attachments -->
            <div class="form-section">
                <h2>📎 Attachments & Resources</h2>
                
                <div class="form-group">
                    <label for="attachments" class="form-label">Upload Files</label>
                    <div class="file-upload-area" onclick="document.getElementById('attachments').click()">
                        <input type="file" id="attachments" name="attachments[]" multiple class="file-input" onchange="previewAttachments(this)">
                        <div class="upload-placeholder">
                            <span class="upload-icon">📎</span>
                            <span class="upload-text">Click to upload files</span>
                            <span class="upload-hint">PDFs, documents, images (max 10MB each)</span>
                        </div>
                    </div>
                    <div class="attachments-preview" id="attachments-preview"></div>
                </div>
            </div>

            <!-- Settings -->
            <div class="form-section">
                <h2>⚙️ Lesson Settings</h2>
                
                <div class="settings-grid">
                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="is_published" value="1" {{ old('is_published') ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">📢 Publish Lesson</span>
                                <span class="toggle-desc">Make this lesson visible to students</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="is_free" value="1" {{ old('is_free') ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">🆓 Free Preview</span>
                                <span class="toggle-desc">Allow non-enrolled students to view this lesson</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="allow_comments" value="1" {{ old('allow_comments', true) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">💬 Allow Comments</span>
                                <span class="toggle-desc">Students can leave comments and questions</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="downloadable" value="1" {{ old('downloadable') ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">📥 Downloadable</span>
                                <span class="toggle-desc">Allow students to download lesson materials</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="sort_order" class="form-label">Lesson Order</label>
                        <input type="number" 
                               id="sort_order" 
                               name="sort_order" 
                               class="form-control" 
                               value="{{ old('sort_order', $nextOrder ?? 1) }}" 
                               min="1">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="{{ route('courses.lessons.index', $course) }}" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" name="action" value="draft" class="btn btn-outline">
                    📝 Save as Draft
                </button>
                <button type="submit" name="action" value="publish" class="btn btn-primary">
                    🚀 Save & Publish
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.create-lesson-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.course-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.create-lesson-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.file-upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 2.5rem;
}

.upload-text {
    color: #ffffff;
    font-weight: 500;
}

.upload-hint {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.editor-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.editor-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.content-editor {
    min-height: 300px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.quiz-builder, .objectives-builder {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
}

.quiz-header, .objectives-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.quiz-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
}

.objectives-header p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.objective-item {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    align-items: center;
}

.objective-item:last-child {
    margin-bottom: 0;
}

.btn-remove {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.btn-remove:hover {
    background: rgba(239, 68, 68, 0.3);
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.setting-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-content {
    display: flex;
    flex-direction: column;
}

.toggle-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toggle-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .create-lesson-container {
        padding: 1rem;
    }
    
    .create-lesson-form {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .editor-toolbar {
        flex-wrap: wrap;
    }
}
</style>

<script>
function toggleContentSections() {
    const lessonType = document.getElementById('lesson_type').value;
    const sections = document.querySelectorAll('.content-section');
    
    // Hide all content sections
    sections.forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active');
    });
    
    // Show relevant section
    if (lessonType) {
        const targetSection = document.getElementById(lessonType + '-section');
        if (targetSection) {
            targetSection.style.display = 'block';
            targetSection.classList.add('active');
        }
    }
}

function previewVideo(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const preview = document.getElementById('video-preview');
        const placeholder = document.getElementById('video-placeholder');
        const video = preview.querySelector('video');
        
        video.src = URL.createObjectURL(file);
        preview.style.display = 'block';
        placeholder.style.display = 'none';
    }
}

function removeVideo() {
    const input = document.getElementById('video_file');
    const preview = document.getElementById('video-preview');
    const placeholder = document.getElementById('video-placeholder');
    
    input.value = '';
    preview.style.display = 'none';
    placeholder.style.display = 'flex';
}

function previewAttachments(input) {
    const preview = document.getElementById('attachments-preview');
    preview.innerHTML = '';
    
    if (input.files) {
        Array.from(input.files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'attachment-item';
            fileItem.innerHTML = `
                <span class="file-icon">📎</span>
                <span class="file-name">${file.name}</span>
                <span class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
                <button type="button" class="btn-remove" onclick="removeAttachment(${index})">✕</button>
            `;
            preview.appendChild(fileItem);
        });
    }
}

function removeAttachment(index) {
    // Implementation for removing specific attachment
    console.log('Remove attachment at index:', index);
}

function addObjective() {
    const container = document.getElementById('objectives-container');
    const objectiveItem = document.createElement('div');
    objectiveItem.className = 'objective-item';
    objectiveItem.innerHTML = `
        <input type="text" 
               name="learning_objectives[]" 
               class="form-control" 
               placeholder="Students will be able to...">
        <button type="button" class="btn-remove" onclick="removeObjective(this)">✕</button>
    `;
    container.appendChild(objectiveItem);
}

function removeObjective(button) {
    button.parentElement.remove();
}

function addQuestion() {
    // Implementation for adding quiz questions
    console.log('Add quiz question');
}

// Text editor functions
function formatText(command) {
    document.execCommand(command, false, null);
}

function insertList() {
    document.execCommand('insertUnorderedList', false, null);
}

function insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
        document.execCommand('createLink', false, url);
    }
}

function insertImage() {
    const url = prompt('Enter image URL:');
    if (url) {
        document.execCommand('insertImage', false, url);
    }
}

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    toggleContentSections();
});
</script>
@endsection
