// Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Setup dashboard interactions
    setupQuickActions();
    setupProgressTracking();
    setupNotifications();
    
    // Load dashboard data
    loadDashboardData();
    
    console.log('Dashboard initialized');
}

function setupQuickActions() {
    const quickActions = document.querySelectorAll('.quick-action');
    
    quickActions.forEach(action => {
        action.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Track action
            const actionType = this.querySelector('h4').textContent;
            trackDashboardAction('quick_action_clicked', { action: actionType });
        });
    });
}

function setupProgressTracking() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-fill');
    
    progressBars.forEach(bar => {
        const targetWidth = bar.getAttribute('data-progress') || '0';
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.width = targetWidth + '%';
        }, 500);
    });
}

function setupNotifications() {
    // Check for new notifications
    checkNotifications();
    
    // Setup notification polling
    setInterval(checkNotifications, 30000); // Check every 30 seconds
}

function loadDashboardData() {
    // Load user stats
    loadUserStats();
    
    // Load recent activity
    loadRecentActivity();
    
    // Load course progress
    loadCourseProgress();
}

function loadUserStats() {
    // This would typically fetch from an API
    const stats = {
        coursesEnrolled: 0,
        progress: 0,
        achievements: 0
    };
    
    updateStatCard('courses-enrolled', stats.coursesEnrolled);
    updateStatCard('progress', stats.progress + '%');
    updateStatCard('achievements', stats.achievements);
}

function updateStatCard(statId, value) {
    const statElement = document.querySelector(`[data-stat="${statId}"] .stat-number`);
    if (statElement) {
        animateNumber(statElement, value);
    }
}

function animateNumber(element, targetValue) {
    const isPercentage = typeof targetValue === 'string' && targetValue.includes('%');
    const numericValue = parseInt(targetValue.toString().replace(/[^\d]/g, ''));
    
    let current = 0;
    const increment = numericValue / 30;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= numericValue) {
            current = numericValue;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(current) + (isPercentage ? '%' : '');
    }, 50);
}

function loadRecentActivity() {
    // This would fetch recent user activity
    const activities = [];
    
    const activityContainer = document.querySelector('.recent-activity');
    if (activityContainer && activities.length === 0) {
        showEmptyState(activityContainer, 'No recent activity', 'Your learning activity will appear here');
    }
}

function loadCourseProgress() {
    // This would fetch user's course progress
    const courses = [];
    
    const coursesContainer = document.querySelector('.continue-learning');
    if (coursesContainer && courses.length === 0) {
        showEmptyState(coursesContainer, 'No courses in progress', 'Start your first course to begin your journey');
    }
}

function showEmptyState(container, title, message) {
    const emptyState = container.querySelector('.empty-state');
    if (emptyState) {
        const titleElement = emptyState.querySelector('h4');
        const messageElement = emptyState.querySelector('p');
        
        if (titleElement) titleElement.textContent = title;
        if (messageElement) messageElement.textContent = message;
    }
}

function checkNotifications() {
    // This would check for new notifications from the server
    // For now, we'll simulate it
    const hasNewNotifications = false;
    
    if (hasNewNotifications) {
        showNotificationBadge();
    }
}

function showNotificationBadge() {
    const notificationIcon = document.querySelector('.notification-icon');
    if (notificationIcon) {
        notificationIcon.classList.add('has-notifications');
    }
}

function trackDashboardAction(action, data = {}) {
    console.log('Dashboard action:', action, data);
    
    // This would send analytics data to your tracking service
    if (typeof gtag !== 'undefined') {
        gtag('event', action, data);
    }
}

// Course card interactions
function setupCourseCards() {
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        card.addEventListener('click', function() {
            const courseId = this.getAttribute('data-course-id');
            if (courseId) {
                window.location.href = `/courses/${courseId}`;
            }
        });
        
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Achievement notifications
function showAchievementNotification(achievement) {
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    notification.innerHTML = `
        <div class="achievement-icon">🏆</div>
        <div class="achievement-content">
            <h4>Achievement Unlocked!</h4>
            <p>${achievement.name}</p>
        </div>
    `;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Search functionality
function setupSearch() {
    const searchInput = document.querySelector('#dashboard-search');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const query = this.value.toLowerCase();
            performSearch(query);
        }, 300));
    }
}

function performSearch(query) {
    if (query.length < 2) {
        clearSearchResults();
        return;
    }
    
    // This would search through courses, lessons, community posts, etc.
    console.log('Searching for:', query);
}

function clearSearchResults() {
    const resultsContainer = document.querySelector('#search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize additional dashboard features
document.addEventListener('DOMContentLoaded', function() {
    setupCourseCards();
    setupSearch();
});

// Add dashboard-specific styles
const dashboardStyles = document.createElement('style');
dashboardStyles.textContent = `
    .course-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
    }
    
    .course-card:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    }
    
    .quick-action {
        transition: transform 0.15s ease;
    }
    
    .achievement-notification {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .achievement-icon {
        font-size: 2rem;
    }
    
    .notification-icon.has-notifications::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 8px;
        height: 8px;
        background: #ef4444;
        border-radius: 50%;
    }
`;
document.head.appendChild(dashboardStyles);
