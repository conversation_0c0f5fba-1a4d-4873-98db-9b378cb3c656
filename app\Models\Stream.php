<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Stream extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'instructor_id',
        'course_id',
        'scheduled_at',
        'started_at',
        'ended_at',
        'duration_minutes',
        'max_participants',
        'is_public',
        'allow_chat',
        'allow_recording',
        'stream_key',
        'stream_url',
        'recording_url',
        'status',
        'peak_concurrent_viewers',
        'total_views',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'is_public' => 'boolean',
        'allow_chat' => 'boolean',
        'allow_recording' => 'boolean',
        'peak_concurrent_viewers' => 'integer',
        'total_views' => 'integer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'stream_key',
    ];

    /**
     * Get the instructor that owns the stream.
     */
    public function instructor()
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the course associated with the stream.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the participants of the stream.
     */
    public function participants()
    {
        return $this->belongsToMany(User::class, 'stream_participants')
            ->withPivot(['joined_at', 'left_at'])
            ->withTimestamps();
    }

    /**
     * Get the chat messages for the stream.
     */
    public function chatMessages()
    {
        return $this->hasMany(StreamChatMessage::class);
    }

    /**
     * Scope for live streams.
     */
    public function scopeLive($query)
    {
        return $query->where('status', 'live');
    }

    /**
     * Scope for scheduled streams.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for public streams.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for upcoming streams.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', now())
            ->where('status', 'scheduled');
    }

    /**
     * Check if the stream is live.
     */
    public function isLive()
    {
        return $this->status === 'live';
    }

    /**
     * Check if the stream is scheduled.
     */
    public function isScheduled()
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if the stream has ended.
     */
    public function hasEnded()
    {
        return $this->status === 'ended';
    }

    /**
     * Check if the stream is starting soon (within 15 minutes).
     */
    public function isStartingSoon()
    {
        if (!$this->isScheduled()) {
            return false;
        }

        return $this->scheduled_at->diffInMinutes(now()) <= 15;
    }

    /**
     * Get the duration of the stream in minutes.
     */
    public function getActualDurationAttribute()
    {
        if ($this->started_at && $this->ended_at) {
            return $this->ended_at->diffInMinutes($this->started_at);
        }

        if ($this->started_at && $this->isLive()) {
            return now()->diffInMinutes($this->started_at);
        }

        return 0;
    }

    /**
     * Get the current participant count.
     */
    public function getCurrentParticipantCount()
    {
        return $this->participants()
            ->wherePivot('left_at', null)
            ->count();
    }

    /**
     * Get the stream status with color.
     */
    public function getStatusWithColorAttribute()
    {
        $colors = [
            'scheduled' => 'blue',
            'live' => 'green',
            'ended' => 'gray',
            'cancelled' => 'red',
        ];

        return [
            'status' => $this->status,
            'color' => $colors[$this->status] ?? 'gray',
            'label' => ucfirst($this->status),
        ];
    }

    /**
     * Get the stream thumbnail URL.
     */
    public function getThumbnailUrlAttribute()
    {
        // Return course thumbnail if stream is linked to a course
        if ($this->course && $this->course->thumbnail) {
            return $this->course->thumbnail;
        }

        // Return instructor avatar
        if ($this->instructor && $this->instructor->avatar) {
            return $this->instructor->avatar;
        }

        // Return default thumbnail
        return asset('images/default-stream-thumbnail.jpg');
    }

    /**
     * Get formatted scheduled time.
     */
    public function getFormattedScheduledTimeAttribute()
    {
        return $this->scheduled_at ? $this->scheduled_at->format('M j, Y \a\t g:i A') : 'Not scheduled';
    }

    /**
     * Get time until stream starts.
     */
    public function getTimeUntilStartAttribute()
    {
        if (!$this->isScheduled()) {
            return null;
        }

        return $this->scheduled_at->diffForHumans();
    }

    /**
     * Get estimated end time.
     */
    public function getEstimatedEndTimeAttribute()
    {
        if ($this->started_at) {
            return $this->started_at->addMinutes($this->duration_minutes);
        }

        return $this->scheduled_at ? $this->scheduled_at->addMinutes($this->duration_minutes) : null;
    }

    /**
     * Check if user can join the stream.
     */
    public function canUserJoin(User $user)
    {
        // Stream must be live or starting soon
        if (!$this->isLive() && !$this->isStartingSoon()) {
            return false;
        }

        // Check max participants
        if ($this->max_participants && $this->getCurrentParticipantCount() >= $this->max_participants) {
            return false;
        }

        // Check if user has access
        return $this->userHasAccess($user);
    }

    /**
     * Check if user has access to the stream.
     */
    public function userHasAccess(User $user)
    {
        // Instructor always has access
        if ($this->instructor_id === $user->id) {
            return true;
        }

        // Admins have access to all streams
        if ($user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // Public streams are accessible to everyone
        if ($this->is_public) {
            return true;
        }

        // If linked to a course, check enrollment
        if ($this->course_id) {
            return $this->course->enrollments()
                ->where('user_id', $user->id)
                ->exists();
        }

        return false;
    }

    /**
     * Add a participant to the stream.
     */
    public function addParticipant(User $user)
    {
        if (!$this->participants()->where('user_id', $user->id)->exists()) {
            $this->participants()->attach($user->id, [
                'joined_at' => now(),
            ]);
        }
    }

    /**
     * Remove a participant from the stream.
     */
    public function removeParticipant(User $user)
    {
        $this->participants()->updateExistingPivot($user->id, [
            'left_at' => now(),
        ]);
    }

    /**
     * Update peak concurrent viewers.
     */
    public function updatePeakViewers()
    {
        $currentCount = $this->getCurrentParticipantCount();
        
        if ($currentCount > ($this->peak_concurrent_viewers ?? 0)) {
            $this->update(['peak_concurrent_viewers' => $currentCount]);
        }
    }

    /**
     * Increment total views.
     */
    public function incrementViews()
    {
        $this->increment('total_views');
    }
}
