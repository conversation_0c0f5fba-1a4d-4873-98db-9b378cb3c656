<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Instructor Panel') - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/instructor.css') }}" rel="stylesheet">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    @stack('styles')
</head>
<body class="instructor-body">
    <!-- Instructor Sidebar -->
    <div class="instructor-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <h2>👨‍🏫 Instructor</h2>
                <p>{{ Auth::user()->name }}</p>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="{{ route('instructor.dashboard') }}" class="nav-link {{ request()->routeIs('instructor.dashboard') ? 'active' : '' }}">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('instructor.courses.index') }}" class="nav-link {{ request()->routeIs('instructor.courses.*') ? 'active' : '' }}">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">My Courses</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('courses.create') }}" class="nav-link {{ request()->routeIs('courses.create') ? 'active' : '' }}">
                        <span class="nav-icon">➕</span>
                        <span class="nav-text">Create Course</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('instructor.students') }}" class="nav-link {{ request()->routeIs('instructor.students*') ? 'active' : '' }}">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">Students</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('instructor.earnings') }}" class="nav-link {{ request()->routeIs('instructor.earnings*') ? 'active' : '' }}">
                        <span class="nav-icon">💰</span>
                        <span class="nav-text">Earnings</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('instructor.analytics') }}" class="nav-link {{ request()->routeIs('instructor.analytics*') ? 'active' : '' }}">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('instructor.streams.index') }}" class="nav-link {{ request()->routeIs('instructor.streams.*') ? 'active' : '' }}">
                        <span class="nav-icon">🎥</span>
                        <span class="nav-text">Live Streams</span>
                    </a>
                </li>
                
                <li class="nav-divider"></li>
                
                <li class="nav-item">
                    <a href="{{ route('dashboard') }}" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">Back to Site</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="{{ route('profile.edit') }}" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <form method="POST" action="{{ route('logout') }}" class="nav-form">
                        @csrf
                        <button type="submit" class="nav-link nav-logout">
                            <span class="nav-icon">🚪</span>
                            <span class="nav-text">Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>
    
    <!-- Main Content Area -->
    <div class="instructor-main">
        <!-- Top Bar -->
        <div class="instructor-topbar">
            <div class="topbar-left">
                <button type="button" class="sidebar-toggle" onclick="toggleSidebar()">
                    <span class="hamburger"></span>
                </button>
                <div class="breadcrumb">
                    <span class="breadcrumb-item">Instructor</span>
                    @if(isset($breadcrumb))
                        @foreach($breadcrumb as $item)
                            <span class="breadcrumb-separator">›</span>
                            <span class="breadcrumb-item">{{ $item }}</span>
                        @endforeach
                    @endif
                </div>
            </div>
            
            <div class="topbar-right">
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Students</span>
                        <span class="stat-value">{{ Auth::user()->totalStudents ?? 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Monthly Earnings</span>
                        <span class="stat-value">${{ number_format(Auth::user()->monthlyEarnings ?? 0, 2) }}</span>
                    </div>
                </div>
                
                <div class="instructor-user">
                    <div class="user-avatar">
                        @if(Auth::user()->avatar)
                            <img src="{{ Storage::url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}">
                        @else
                            <div class="avatar-placeholder">
                                {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                            </div>
                        @endif
                    </div>
                    <div class="user-info">
                        <span class="user-name">{{ Auth::user()->name }}</span>
                        <span class="user-role">Instructor</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page Content -->
        <div class="instructor-content">
            <!-- Flash Messages -->
            @if (session('success'))
                <div class="alert alert-success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">
                        <strong>Success!</strong>
                        <p>{{ session('success') }}</p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">
                        <strong>Error!</strong>
                        <p>{{ session('error') }}</p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            @endif

            @if (session('warning'))
                <div class="alert alert-warning">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <strong>Warning!</strong>
                        <p>{{ session('warning') }}</p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            @endif

            @if (session('info'))
                <div class="alert alert-info">
                    <div class="alert-icon">ℹ️</div>
                    <div class="alert-content">
                        <strong>Info!</strong>
                        <p>{{ session('info') }}</p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            @endif
            
            <!-- Main Content -->
            @yield('content')
        </div>
    </div>
    
    <!-- Custom JS -->
    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/instructor.js') }}"></script>
    
    <script>
        // Sidebar toggle functionality
        function toggleSidebar() {
            document.body.classList.toggle('sidebar-collapsed');
            localStorage.setItem('instructorSidebarCollapsed', document.body.classList.contains('sidebar-collapsed'));
        }
        
        // Restore sidebar state
        if (localStorage.getItem('instructorSidebarCollapsed') === 'true') {
            document.body.classList.add('sidebar-collapsed');
        }
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
