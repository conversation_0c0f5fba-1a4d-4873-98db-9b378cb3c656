<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseEnrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'enrolled_at',
        'completed_at',
        'progress',
        'last_accessed_at',
        'certificate_issued_at',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'certificate_issued_at' => 'datetime',
        'progress' => 'integer',
    ];

    /**
     * Get the user that owns the enrollment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that this enrollment is for.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Check if the enrollment is completed.
     */
    public function isCompleted(): bool
    {
        return !is_null($this->completed_at);
    }

    /**
     * Get progress status for filtering.
     */
    public function getProgressStatus()
    {
        if ($this->completed_at) {
            return 'completed';
        }

        if ($this->progress > 0) {
            return 'in-progress';
        }

        return 'not-started';
    }

    /**
     * Get progress percentage.
     */
    public function getProgressPercentage()
    {
        return min(100, max(0, $this->progress));
    }

    /**
     * Mark enrollment as completed.
     */
    public function markAsCompleted()
    {
        $this->update([
            'completed_at' => now(),
            'progress' => 100,
        ]);
    }

    /**
     * Update progress.
     */
    public function updateProgress($progress)
    {
        $this->update([
            'progress' => min(100, max(0, $progress)),
            'last_accessed_at' => now(),
        ]);

        // Auto-complete if progress reaches 100%
        if ($progress >= 100 && !$this->completed_at) {
            $this->markAsCompleted();
        }
    }

    /**
     * Get formatted enrollment date.
     */
    public function getFormattedEnrollmentDateAttribute()
    {
        return $this->enrolled_at ? $this->enrolled_at->format('M j, Y') : $this->created_at->format('M j, Y');
    }

    /**
     * Get time since enrollment.
     */
    public function getTimeSinceEnrollmentAttribute()
    {
        $enrollmentDate = $this->enrolled_at ?: $this->created_at;
        return $enrollmentDate->diffForHumans();
    }

    /**
     * Check if certificate can be issued.
     */
    public function canIssueCertificate()
    {
        return $this->isCompleted() && !$this->certificate_issued_at;
    }

    /**
     * Issue certificate.
     */
    public function issueCertificate()
    {
        if ($this->canIssueCertificate()) {
            $this->update(['certificate_issued_at' => now()]);
            return true;
        }

        return false;
    }

    /**
     * Get the completed lessons for this enrollment.
     */
    public function completedLessons()
    {
        return $this->belongsToMany(Lesson::class, 'lesson_completions', 'enrollment_id', 'lesson_id')
            ->withPivot('completed_at')
            ->withTimestamps();
    }
}
