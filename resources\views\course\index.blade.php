<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Courses - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/courses.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}" class="active">Courses</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="#chat">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="courses-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Master Real-World Skills</h1>
                    <p>Choose from 18 different campuses designed to make you money</p>
                </div>
                @can('create courses')
                    <div class="header-actions">
                        <a href="{{ route('courses.create') }}" class="btn btn-primary">
                            Create Course
                        </a>
                    </div>
                @endcan
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" action="{{ route('courses.index') }}" class="filters-form">
                    <div class="filter-group">
                        <input type="text" name="search" placeholder="Search courses..." 
                               value="{{ request('search') }}" class="search-input">
                    </div>
                    
                    <div class="filter-group">
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" 
                                        {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="difficulty" class="filter-select">
                            <option value="">All Levels</option>
                            <option value="beginner" {{ request('difficulty') == 'beginner' ? 'selected' : '' }}>
                                Beginner
                            </option>
                            <option value="intermediate" {{ request('difficulty') == 'intermediate' ? 'selected' : '' }}>
                                Intermediate
                            </option>
                            <option value="advanced" {{ request('difficulty') == 'advanced' ? 'selected' : '' }}>
                                Advanced
                            </option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="price_filter" class="filter-select">
                            <option value="">All Prices</option>
                            <option value="free" {{ request('price_filter') == 'free' ? 'selected' : '' }}>
                                Free
                            </option>
                            <option value="paid" {{ request('price_filter') == 'paid' ? 'selected' : '' }}>
                                Paid
                            </option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="{{ route('courses.index') }}" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <!-- Courses Grid -->
            <div class="courses-grid">
                @forelse($courses as $course)
                    <div class="course-card">
                        <div class="course-thumbnail">
                            @if($course->thumbnail)
                                <img src="{{ Storage::url($course->thumbnail) }}" alt="{{ $course->title }}">
                            @else
                                <div class="course-placeholder">
                                    <span class="course-icon">🎯</span>
                                </div>
                            @endif
                            @if($course->is_free)
                                <div class="course-badge free">FREE</div>
                            @else
                                <div class="course-badge price">${{ $course->price }}</div>
                            @endif
                        </div>
                        
                        <div class="course-content">
                            <div class="course-meta">
                                <span class="course-category">{{ $course->category->name }}</span>
                                <span class="course-level">{{ ucfirst($course->difficulty_level) }}</span>
                            </div>
                            
                            <h3 class="course-title">
                                <a href="{{ route('courses.show', $course) }}">{{ $course->title }}</a>
                            </h3>
                            
                            <p class="course-description">{{ $course->short_description }}</p>
                            
                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-icon">👥</span>
                                    <span class="stat-value">{{ $course->total_students }}</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">📚</span>
                                    <span class="stat-value">{{ $course->total_lessons }} lessons</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-value">{{ number_format($course->rating, 1) }}</span>
                                </div>
                            </div>
                            
                            <div class="course-instructor">
                                <span class="instructor-label">By</span>
                                <span class="instructor-name">{{ $course->instructor->name }}</span>
                            </div>
                        </div>
                        
                        <div class="course-actions">
                            <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-block">
                                View Course
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="empty-state">
                        <div class="empty-icon">📚</div>
                        <h3>No courses found</h3>
                        <p>Try adjusting your filters or search terms</p>
                        @can('create courses')
                            <a href="{{ route('courses.create') }}" class="btn btn-primary">
                                Create First Course
                            </a>
                        @endcan
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($courses->hasPages())
                <div class="pagination-wrapper">
                    {{ $courses->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </main>

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/courses.js') }}"></script>
</body>
</html>
