<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\Community;
use App\Models\ChatRoom;
use App\Models\Notification;
use App\Models\Payment;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

class CompleteDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting complete data seeding...');

        // Create roles and permissions
        $this->createRolesAndPermissions();

        // Create admin user
        $this->createAdminUser();

        // Create sample users
        $this->createSampleUsers();

        // Create course categories
        $this->createCourseCategories();

        // Create sample courses
        $this->createSampleCourses();

        // Create communities
        $this->createCommunities();

        // Create chat rooms
        $this->createChatRooms();

        // Create sample notifications
        $this->createSampleNotifications();

        // Create sample payments
        $this->createSamplePayments();

        $this->command->info('✅ Complete data seeding finished!');
    }

    private function createRolesAndPermissions()
    {
        $this->command->info('Creating roles and permissions...');

        // Create roles
        $roles = ['super-admin', 'admin', 'instructor', 'student'];
        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }

        // Create permissions
        $permissions = [
            'manage users',
            'manage courses',
            'manage payments',
            'manage communities',
            'manage chat',
            'view analytics',
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $superAdmin = Role::findByName('super-admin');
        $superAdmin->givePermissionTo(Permission::all());

        $admin = Role::findByName('admin');
        $admin->givePermissionTo([
            'manage users',
            'manage courses',
            'manage payments',
            'manage communities',
            'view analytics',
        ]);

        $instructor = Role::findByName('instructor');
        $instructor->givePermissionTo([
            'manage courses',
            'view analytics',
        ]);
    }

    private function createAdminUser()
    {
        $this->command->info('Creating admin user...');

        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        $admin->assignRole('super-admin');
    }

    private function createSampleUsers()
    {
        $this->command->info('Creating sample users...');

        // Create instructor
        $instructor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Andrew Tate',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );
        $instructor->assignRole('instructor');

        // Create sample students
        $students = [
            ['name' => 'John Student', 'email' => '<EMAIL>'],
            ['name' => 'Jane Learner', 'email' => '<EMAIL>'],
            ['name' => 'Mike Scholar', 'email' => '<EMAIL>'],
            ['name' => 'Sarah Pupil', 'email' => '<EMAIL>'],
            ['name' => 'David Trainee', 'email' => '<EMAIL>'],
        ];

        foreach ($students as $studentData) {
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                [
                    'name' => $studentData['name'],
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                    'is_active' => true,
                ]
            );
            $student->assignRole('student');
        }
    }

    private function createCourseCategories()
    {
        $this->command->info('Creating course categories...');

        $categories = [
            ['name' => 'Business & Entrepreneurship', 'slug' => 'business-entrepreneurship', 'description' => 'Learn to build and scale businesses'],
            ['name' => 'Cryptocurrency & Trading', 'slug' => 'cryptocurrency-trading', 'description' => 'Master crypto trading and blockchain'],
            ['name' => 'Fitness & Health', 'slug' => 'fitness-health', 'description' => 'Physical and mental optimization'],
            ['name' => 'Mindset & Psychology', 'slug' => 'mindset-psychology', 'description' => 'Develop winning mindset'],
            ['name' => 'Marketing & Sales', 'slug' => 'marketing-sales', 'description' => 'Master the art of selling'],
            ['name' => 'Real Estate', 'slug' => 'real-estate', 'description' => 'Property investment strategies'],
        ];

        foreach ($categories as $category) {
            CourseCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }

    private function createSampleCourses()
    {
        $this->command->info('Creating sample courses...');

        $instructor = User::where('email', '<EMAIL>')->first();
        $categories = CourseCategory::all();

        $courses = [
            [
                'title' => 'Escape The Matrix: Complete Business Course',
                'slug' => 'escape-the-matrix-business-course',
                'description' => 'Learn how to build a million-dollar business from scratch and escape the 9-5 matrix.',
                'short_description' => 'Build a million-dollar business from scratch',
                'price' => 497.00,
                'difficulty_level' => 'intermediate',
                'duration_hours' => 20,
                'total_lessons' => 50,
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Cryptocurrency Mastery',
                'slug' => 'cryptocurrency-mastery',
                'description' => 'Master cryptocurrency trading, DeFi, and blockchain technology.',
                'short_description' => 'Master crypto trading and blockchain',
                'price' => 297.00,
                'difficulty_level' => 'beginner',
                'duration_hours' => 15,
                'total_lessons' => 35,
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Iron Body: Complete Fitness System',
                'slug' => 'iron-body-fitness-system',
                'description' => 'Transform your body and mind with the complete fitness system.',
                'short_description' => 'Transform your body and mind',
                'price' => 197.00,
                'difficulty_level' => 'beginner',
                'duration_hours' => 10,
                'total_lessons' => 25,
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
            ],
            [
                'title' => 'Millionaire Mindset Mastery',
                'slug' => 'millionaire-mindset-mastery',
                'description' => 'Develop the mindset of the ultra-wealthy and successful.',
                'short_description' => 'Develop millionaire mindset',
                'price' => 397.00,
                'difficulty_level' => 'advanced',
                'duration_hours' => 18,
                'total_lessons' => 40,
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
            ],
        ];

        foreach ($courses as $index => $courseData) {
            $course = Course::firstOrCreate(
                ['title' => $courseData['title']],
                array_merge($courseData, [
                    'instructor_id' => $instructor->id,
                    'category_id' => $categories[$index % $categories->count()]->id,
                    'total_students' => rand(50, 500),
                    'rating' => rand(40, 50) / 10, // 4.0 to 5.0
                    'rating_count' => rand(10, 100),
                ])
            );
        }
    }

    private function createCommunities()
    {
        $this->command->info('Creating communities...');

        $creator = User::where('email', '<EMAIL>')->first();

        $communities = [
            [
                'name' => 'The War Room',
                'description' => 'Elite community for high-achieving entrepreneurs and business owners.',
                'type' => 'private',
                'is_active' => true,
                'member_count' => 150,
                'post_count' => 500,
            ],
            [
                'name' => 'Crypto Legends',
                'description' => 'Discuss cryptocurrency trading strategies and market analysis.',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 300,
                'post_count' => 800,
            ],
            [
                'name' => 'Iron Warriors',
                'description' => 'Fitness and health community for physical and mental optimization.',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 200,
                'post_count' => 350,
            ],
            [
                'name' => 'Mindset Masters',
                'description' => 'Develop winning mindset and mental strength.',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 180,
                'post_count' => 420,
            ],
        ];

        foreach ($communities as $communityData) {
            Community::firstOrCreate(
                ['name' => $communityData['name']],
                array_merge($communityData, [
                    'created_by' => $creator->id,
                    'slug' => \Str::slug($communityData['name']),
                ])
            );
        }
    }

    private function createChatRooms()
    {
        $this->command->info('Creating chat rooms...');

        $creator = User::where('email', '<EMAIL>')->first();

        $chatRooms = [
            [
                'name' => 'General Discussion',
                'description' => 'General chat for all members',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 500,
                'message_count' => 2000,
            ],
            [
                'name' => 'Business Talk',
                'description' => 'Discuss business strategies and opportunities',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 250,
                'message_count' => 1200,
            ],
            [
                'name' => 'Crypto Signals',
                'description' => 'Real-time crypto trading signals and analysis',
                'type' => 'private',
                'is_active' => true,
                'member_count' => 100,
                'message_count' => 800,
            ],
            [
                'name' => 'Fitness Motivation',
                'description' => 'Daily motivation and workout tips',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 180,
                'message_count' => 600,
            ],
            [
                'name' => 'Success Stories',
                'description' => 'Share your wins and celebrate success',
                'type' => 'public',
                'is_active' => true,
                'member_count' => 300,
                'message_count' => 900,
            ],
        ];

        foreach ($chatRooms as $roomData) {
            ChatRoom::firstOrCreate(
                ['name' => $roomData['name']],
                array_merge($roomData, [
                    'created_by' => $creator->id,
                    'slug' => \Str::slug($roomData['name']),
                ])
            );
        }
    }

    private function createSampleNotifications()
    {
        $this->command->info('Creating sample notifications...');

        $users = User::where('email', '!=', '<EMAIL>')->take(5)->get();
        $courses = Course::take(3)->get();

        foreach ($users as $user) {
            // Create database notifications using Laravel's notification system
            $user->notifications()->create([
                'id' => \Str::uuid(),
                'type' => 'App\Notifications\WelcomeNotification',
                'data' => [
                    'title' => 'Welcome to The Real World!',
                    'message' => 'Welcome to The Real World community. Start your journey to escape the matrix.',
                    'welcome_bonus' => true,
                    'first_login' => true,
                    'action_url' => '/dashboard',
                ],
                'read_at' => null,
            ]);

            // Course enrollment notification (if user has courses)
            if ($courses->isNotEmpty()) {
                $course = $courses->random();
                $user->notifications()->create([
                    'id' => \Str::uuid(),
                    'type' => 'App\Notifications\CourseEnrollmentNotification',
                    'data' => [
                        'title' => 'Course Enrollment Successful',
                        'message' => "You have successfully enrolled in '{$course->title}'",
                        'course_id' => $course->id,
                        'course_title' => $course->title,
                        'action_url' => "/courses/{$course->id}",
                    ],
                    'read_at' => rand(0, 1) == 1 ? now() : null,
                ]);
            }
        }
    }

    private function createSamplePayments()
    {
        $this->command->info('Creating sample payments...');

        $users = User::where('email', '!=', '<EMAIL>')->take(5)->get();
        $courses = Course::all();

        foreach ($users as $user) {
            if ($courses->isNotEmpty()) {
                $course = $courses->random();

                Payment::create([
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                    'payment_method' => rand(0, 1) ? 'stripe' : 'crypto',
                    'amount' => $course->price,
                    'currency' => 'USD',
                    'status' => 'completed',
                    'payment_intent_id' => 'pi_' . uniqid(),
                    'gateway_response' => [
                        'processed_at' => now()->subDays(rand(1, 30))->toISOString(),
                        'sample_data' => true,
                    ],
                    'completed_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
}
