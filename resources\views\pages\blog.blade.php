@extends('layouts.minimal')

@section('title', 'Blog')

@section('content')
<div class="blog-page">
    <div class="page-header">
        <h1>📝 The Real World Blog</h1>
        <p class="page-subtitle">Insights, tips, and success stories from our community</p>
    </div>

    <div class="blog-content">
        <div class="coming-soon">
            <div class="coming-soon-icon">🚧</div>
            <h2>Blog Coming Soon</h2>
            <p>We're working on bringing you valuable content, success stories, and insights from The Real World community.</p>
            
            <div class="preview-topics">
                <h3>What to Expect:</h3>
                <ul>
                    <li>🎯 Business Strategy Articles</li>
                    <li>💰 Success Stories from Students</li>
                    <li>📈 Market Analysis and Trends</li>
                    <li>🛠️ Practical Tips and Tutorials</li>
                    <li>🎤 Interviews with Successful Entrepreneurs</li>
                </ul>
            </div>
            
            <div class="newsletter-signup">
                <h3>Stay Updated</h3>
                <p>Be the first to know when we launch our blog!</p>
                <form class="signup-form">
                    <input type="email" placeholder="Enter your email" class="email-input">
                    <button type="submit" class="btn btn-primary">Notify Me</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.blog-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin: 0;
}

.coming-soon {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
}

.coming-soon-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.coming-soon h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.coming-soon p {
    color: #64748b;
    font-size: 1.125rem;
    margin-bottom: 2rem;
}

.preview-topics {
    margin: 2rem 0;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.preview-topics h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    text-align: center;
}

.preview-topics ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.preview-topics li {
    padding: 0.5rem 0;
    color: #475569;
    font-size: 1rem;
}

.newsletter-signup {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.newsletter-signup h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.newsletter-signup p {
    color: #64748b;
    margin-bottom: 1.5rem;
}

.signup-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.email-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
}

.email-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (max-width: 768px) {
    .blog-page {
        padding: 1rem;
    }
    
    .coming-soon {
        padding: 2rem;
    }
    
    .signup-form {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.signup-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.querySelector('.email-input').value;
        if (email) {
            alert('Thank you! We\'ll notify you when the blog launches.');
            document.querySelector('.email-input').value = '';
        }
    });
});
</script>
@endsection
