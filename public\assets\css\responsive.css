/* Responsive Design System */

/* Mobile First Approach */
:root {
    --mobile-padding: 1rem;
    --tablet-padding: 2rem;
    --desktop-padding: 3rem;
    
    --mobile-font-size: 0.875rem;
    --tablet-font-size: 1rem;
    --desktop-font-size: 1.125rem;
    
    --mobile-heading: 1.5rem;
    --tablet-heading: 2rem;
    --desktop-heading: 2.5rem;
}

/* Base Mobile Styles (320px+) */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--mobile-padding);
}

/* Typography Responsive */
h1 {
    font-size: var(--mobile-heading);
    line-height: 1.2;
}

h2 {
    font-size: calc(var(--mobile-heading) * 0.85);
    line-height: 1.3;
}

h3 {
    font-size: calc(var(--mobile-heading) * 0.7);
    line-height: 1.4;
}

body {
    font-size: var(--mobile-font-size);
    line-height: 1.6;
}

/* Navigation Responsive */
.navbar {
    padding: 0.75rem 0;
}

.navbar-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.navbar-nav.active {
    display: flex;
}

.navbar-nav li {
    margin: 1rem 0;
}

.navbar-nav a {
    font-size: 1.25rem;
    padding: 1rem 2rem;
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Grid Systems */
.grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

.grid-2 {
    grid-template-columns: 1fr;
}

.grid-3 {
    grid-template-columns: 1fr;
}

.grid-4 {
    grid-template-columns: 1fr;
}

/* Card Responsive */
.card {
    padding: 1rem;
    margin-bottom: 1rem;
}

.course-card,
.community-card,
.chat-room-card {
    margin-bottom: 1rem;
}

/* Form Responsive */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    font-size: var(--mobile-font-size);
}

.btn {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: var(--mobile-font-size);
    margin-bottom: 0.5rem;
}

.btn-group {
    flex-direction: column;
    gap: 0.5rem;
}

/* Dashboard Responsive */
.dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.quick-actions {
    grid-template-columns: 1fr;
}

.stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

/* Course Layout Responsive */
.course-layout {
    flex-direction: column;
}

.course-sidebar {
    order: -1;
    margin-bottom: 2rem;
}

.course-content {
    order: 1;
}

/* Chat Layout Responsive */
.chat-container {
    flex-direction: column;
    height: calc(100vh - 160px);
}

.chat-sidebar {
    order: -1;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.chat-area {
    order: 1;
    flex: 1;
}

/* Admin Panel Responsive */
.admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.admin-sidebar.active {
    transform: translateX(0);
}

.admin-main {
    margin-left: 0;
}

.admin-topbar {
    padding: 1rem;
}

.metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.charts-section {
    grid-template-columns: 1fr;
}

.content-grid {
    grid-template-columns: 1fr;
}

/* Table Responsive */
.table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.data-table {
    min-width: 600px;
}

.data-table th,
.data-table td {
    padding: 0.5rem;
    font-size: 0.75rem;
}

/* Payment Responsive */
.course-summary {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
}

.method-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
}

.method-badges {
    justify-content: center;
}

.security-badges {
    flex-direction: column;
    gap: 1rem;
}

/* Community Responsive */
.community-content-layout {
    flex-direction: column;
}

.community-sidebar {
    order: -1;
    margin-bottom: 2rem;
}

.community-posts {
    order: 1;
}

/* Tablet Styles (768px+) */
@media (min-width: 768px) {
    :root {
        --mobile-padding: var(--tablet-padding);
        --mobile-font-size: var(--tablet-font-size);
        --mobile-heading: var(--tablet-heading);
    }
    
    .navbar-nav {
        position: static;
        display: flex;
        flex-direction: row;
        height: auto;
        background: none;
        backdrop-filter: none;
        width: auto;
    }
    
    .navbar-nav li {
        margin: 0 1rem;
    }
    
    .navbar-nav a {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .btn {
        width: auto;
        margin-bottom: 0;
    }
    
    .btn-group {
        flex-direction: row;
    }
    
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .course-layout {
        flex-direction: row;
    }
    
    .course-sidebar {
        order: 1;
        margin-bottom: 0;
        margin-left: 2rem;
        min-width: 300px;
    }
    
    .course-content {
        order: 0;
        flex: 1;
    }
    
    .chat-container {
        flex-direction: row;
        height: calc(100vh - 120px);
    }
    
    .chat-sidebar {
        order: 1;
        max-height: none;
        margin-bottom: 0;
        margin-left: 1rem;
        min-width: 250px;
    }
    
    .chat-area {
        order: 0;
        flex: 1;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .course-summary {
        flex-direction: row;
        text-align: left;
    }
    
    .method-header {
        flex-direction: row;
        text-align: left;
    }
    
    .security-badges {
        flex-direction: row;
    }
    
    .community-content-layout {
        flex-direction: row;
    }
    
    .community-sidebar {
        order: 1;
        margin-bottom: 0;
        margin-left: 2rem;
        min-width: 300px;
    }
    
    .community-posts {
        order: 0;
        flex: 1;
    }
}

/* Desktop Styles (1024px+) */
@media (min-width: 1024px) {
    :root {
        --mobile-padding: var(--desktop-padding);
        --mobile-font-size: var(--desktop-font-size);
        --mobile-heading: var(--desktop-heading);
    }
    
    .grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .admin-sidebar {
        transform: translateX(0);
        position: fixed;
    }
    
    .admin-main {
        margin-left: 280px;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .charts-section {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .content-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    .grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: 3fr 1fr;
    }
}

/* Ultra Wide (1920px+) */
@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .admin-sidebar,
    .chat-input,
    .btn,
    .action-buttons {
        display: none !important;
    }
    
    .admin-main {
        margin-left: 0 !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card,
    .content-card {
        border: 1px solid #ccc !important;
        background: white !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #ffffff;
        --background-color: #000000;
        --text-color: #ffffff;
        --border-color: #ffffff;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 2px solid var(--border-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Dark Mode Preference */
@media (prefers-color-scheme: dark) {
    /* Already implemented in main CSS */
}

/* Light Mode Override */
@media (prefers-color-scheme: light) {
    body.light-mode {
        background: #ffffff;
        color: #000000;
    }
    
    .light-mode .card {
        background: rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.1);
    }
    
    .light-mode .navbar {
        background: rgba(255, 255, 255, 0.95);
    }
    
    .light-mode .btn-primary {
        background: #007bff;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .navbar-nav a {
        padding: 1rem;
    }
    
    .action-buttons .btn {
        min-width: 40px;
        min-height: 40px;
    }
}

/* Landscape Phone */
@media (max-height: 500px) and (orientation: landscape) {
    .chat-container {
        height: calc(100vh - 80px);
    }
    
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-main {
        margin-left: 0;
    }
}
