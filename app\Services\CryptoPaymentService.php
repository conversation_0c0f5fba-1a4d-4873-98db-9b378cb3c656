<?php

namespace App\Services;

use App\Models\Course;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CryptoPaymentService
{
    protected $supportedCurrencies = ['BTC', 'ETH', 'USDT', 'USDC'];

    /**
     * Create crypto payment intent.
     */
    public function createPaymentIntent(User $user, Course $course, string $currency = 'BTC'): array
    {
        try {
            if (!in_array($currency, $this->supportedCurrencies)) {
                throw new \InvalidArgumentException('Unsupported cryptocurrency');
            }

            // Get current crypto prices
            $prices = $this->getPrices();
            $cryptoAmount = $course->price / $prices[$currency];

            // Generate unique payment address (in real implementation, use proper wallet service)
            $paymentAddress = $this->generatePaymentAddress($currency);
            $paymentId = Str::uuid();

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'payment_method' => 'crypto',
                'amount' => $course->price,
                'currency' => 'USD',
                'status' => 'pending',
                'transaction_id' => $paymentId,
                'payment_data' => [
                    'crypto_currency' => $currency,
                    'crypto_amount' => $cryptoAmount,
                    'payment_address' => $paymentAddress,
                    'exchange_rate' => $prices[$currency],
                    'expires_at' => now()->addMinutes(30)->toISOString(),
                ],
            ]);

            Log::info('Crypto payment intent created', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'course_id' => $course->id,
                'currency' => $currency,
                'amount' => $cryptoAmount,
            ]);

            return [
                'success' => true,
                'payment_id' => $paymentId,
                'payment_address' => $paymentAddress,
                'crypto_currency' => $currency,
                'crypto_amount' => $cryptoAmount,
                'usd_amount' => $course->price,
                'exchange_rate' => $prices[$currency],
                'expires_at' => now()->addMinutes(30)->toISOString(),
                'qr_code' => $this->generateQRCode($paymentAddress, $cryptoAmount, $currency),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create crypto payment intent', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'currency' => $currency,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get cryptocurrency prices.
     */
    public function getPrices(): array
    {
        try {
            // In a real implementation, fetch from CoinGecko, CoinMarketCap, etc.
            $response = Http::timeout(10)->get('https://api.coingecko.com/api/v3/simple/price', [
                'ids' => 'bitcoin,ethereum,tether,usd-coin',
                'vs_currencies' => 'usd',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'BTC' => $data['bitcoin']['usd'] ?? 45000,
                    'ETH' => $data['ethereum']['usd'] ?? 3200,
                    'USDT' => $data['tether']['usd'] ?? 1.00,
                    'USDC' => $data['usd-coin']['usd'] ?? 1.00,
                ];
            }

            throw new \Exception('Failed to fetch crypto prices');

        } catch (\Exception $e) {
            Log::warning('Failed to fetch live crypto prices, using fallback', [
                'error' => $e->getMessage(),
            ]);

            // Return fallback prices
            return [
                'BTC' => 45000.00,
                'ETH' => 3200.00,
                'USDT' => 1.00,
                'USDC' => 1.00,
            ];
        }
    }

    /**
     * Generate payment address for cryptocurrency.
     */
    protected function generatePaymentAddress(string $currency): string
    {
        // In a real implementation, generate actual wallet addresses
        // This is just a mock implementation
        switch ($currency) {
            case 'BTC':
                return '1' . Str::random(33); // Bitcoin address format
            case 'ETH':
            case 'USDT':
            case 'USDC':
                return '0x' . Str::random(40); // Ethereum address format
            default:
                throw new \InvalidArgumentException('Unsupported currency');
        }
    }

    /**
     * Generate QR code for payment.
     */
    protected function generateQRCode(string $address, float $amount, string $currency): string
    {
        // Generate payment URI
        $uri = match($currency) {
            'BTC' => "bitcoin:{$address}?amount={$amount}",
            'ETH' => "ethereum:{$address}?value=" . ($amount * 1e18), // Convert to wei
            'USDT', 'USDC' => "ethereum:{$address}?value={$amount}",
            default => "{$currency}:{$address}?amount={$amount}",
        };

        // In a real implementation, generate actual QR code image
        // For now, return the URI that can be used to generate QR code on frontend
        return $uri;
    }

    /**
     * Verify crypto payment.
     */
    public function verifyPayment(string $paymentId, string $transactionHash): bool
    {
        try {
            $payment = Payment::where('transaction_id', $paymentId)->first();

            if (!$payment) {
                throw new \Exception('Payment not found');
            }

            // In a real implementation, verify transaction on blockchain
            // This is a mock verification
            $isValid = $this->verifyTransactionOnBlockchain(
                $transactionHash,
                $payment->payment_data['payment_address'],
                $payment->payment_data['crypto_amount'],
                $payment->payment_data['crypto_currency']
            );

            if ($isValid) {
                $payment->update([
                    'status' => 'completed',
                    'payment_data' => array_merge($payment->payment_data, [
                        'transaction_hash' => $transactionHash,
                        'confirmed_at' => now()->toISOString(),
                    ]),
                ]);

                Log::info('Crypto payment verified', [
                    'payment_id' => $payment->id,
                    'transaction_hash' => $transactionHash,
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to verify crypto payment', [
                'payment_id' => $paymentId,
                'transaction_hash' => $transactionHash,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Verify transaction on blockchain.
     */
    protected function verifyTransactionOnBlockchain(string $txHash, string $address, float $amount, string $currency): bool
    {
        // In a real implementation, use blockchain APIs like:
        // - Blockchair API
        // - Etherscan API
        // - Infura/Alchemy for Ethereum
        // - BlockCypher API

        // Mock verification - in production, implement actual blockchain verification
        return strlen($txHash) >= 32; // Basic validation
    }

    /**
     * Process crypto refund.
     */
    public function processRefund(Payment $payment, float $amount = null): bool
    {
        try {
            // Crypto refunds are typically manual processes
            // Mark payment as refund requested
            $payment->update([
                'status' => 'refund_requested',
                'payment_data' => array_merge($payment->payment_data ?? [], [
                    'refund_requested_at' => now()->toISOString(),
                    'refund_amount' => $amount ?? $payment->amount,
                ]),
            ]);

            Log::info('Crypto refund requested', [
                'payment_id' => $payment->id,
                'amount' => $amount ?? $payment->amount,
            ]);

            // In a real implementation, notify admin to process manual refund
            // or integrate with automated refund service

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to process crypto refund', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Handle crypto webhook (from payment processor).
     */
    public function handleWebhook(array $payload): bool
    {
        try {
            $eventType = $payload['event_type'] ?? null;
            $paymentData = $payload['data'] ?? [];

            Log::info('Crypto webhook received', [
                'event_type' => $eventType,
                'payment_id' => $paymentData['payment_id'] ?? null,
            ]);

            switch ($eventType) {
                case 'payment_confirmed':
                    return $this->handlePaymentConfirmed($paymentData);

                case 'payment_failed':
                    return $this->handlePaymentFailed($paymentData);

                default:
                    Log::info('Unhandled crypto webhook event', [
                        'event_type' => $eventType,
                    ]);
                    return true;
            }

        } catch (\Exception $e) {
            Log::error('Failed to handle crypto webhook', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Handle confirmed crypto payment.
     */
    protected function handlePaymentConfirmed(array $data): bool
    {
        $paymentId = $data['payment_id'] ?? null;
        $transactionHash = $data['transaction_hash'] ?? null;

        if ($paymentId && $transactionHash) {
            return $this->verifyPayment($paymentId, $transactionHash);
        }

        return false;
    }

    /**
     * Handle failed crypto payment.
     */
    protected function handlePaymentFailed(array $data): bool
    {
        $paymentId = $data['payment_id'] ?? null;

        if ($paymentId) {
            $payment = Payment::where('transaction_id', $paymentId)->first();

            if ($payment) {
                $payment->update([
                    'status' => 'failed',
                    'payment_data' => array_merge($payment->payment_data ?? [], [
                        'failed_at' => now()->toISOString(),
                        'failure_reason' => $data['reason'] ?? 'Unknown',
                    ]),
                ]);

                return true;
            }
        }

        return false;
    }
}
