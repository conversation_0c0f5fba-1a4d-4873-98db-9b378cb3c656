@extends('layouts.app')

@section('title', 'Payment Options')

@push('styles')
    <link href="{{ asset('assets/css/payment.css') }}" rel="stylesheet">
    <script src="https://js.stripe.com/v3/"></script>
@endpush

@section('content')
    <!-- Main Content -->
    <div class="payment-main">
        <div class="container">
            <!-- Course Info -->
            <div class="course-summary">
                <div class="course-info">
                    <h1>{{ $course->title }}</h1>
                    <p>{{ $course->description }}</p>
                    <div class="course-details">
                        <span class="course-instructor">by {{ $course->instructor->name }}</span>
                        <span class="course-duration">{{ $course->duration }} minutes</span>
                        <span class="course-level">{{ ucfirst($course->difficulty) }}</span>
                    </div>
                </div>
                <div class="course-price">
                    <div class="price-display">
                        <span class="currency">$</span>
                        <span class="amount">{{ number_format($course->price, 2) }}</span>
                    </div>
                    <div class="price-note">One-time payment</div>
                </div>
            </div>

            <!-- Payment Options -->
            <div class="payment-options">
                <h2>Choose Payment Method</h2>
                
                <!-- Stripe Payment -->
                <div class="payment-method stripe-payment">
                    <div class="method-header">
                        <div class="method-icon">💳</div>
                        <div class="method-info">
                            <h3>Credit Card</h3>
                            <p>Pay securely with your credit or debit card</p>
                        </div>
                        <div class="method-badges">
                            <span class="badge">Visa</span>
                            <span class="badge">Mastercard</span>
                            <span class="badge">Amex</span>
                        </div>
                    </div>
                    
                    <div class="payment-form" id="stripe-form" style="display: none;">
                        <form id="stripe-payment-form">
                            @csrf
                            <div class="form-group">
                                <label for="card-element">Card Information</label>
                                <div id="card-element" class="stripe-element">
                                    <!-- Stripe Elements will create form elements here -->
                                </div>
                                <div id="card-errors" role="alert" class="error-message"></div>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="save-card" name="save_card">
                                    Save this card for future purchases
                                </label>
                            </div>
                            
                            <button type="submit" id="stripe-submit" class="btn btn-primary btn-block">
                                Pay ${{ number_format($course->price, 2) }}
                            </button>
                        </form>
                    </div>
                    
                    <button class="method-select-btn" onclick="selectPaymentMethod('stripe')">
                        Select Credit Card
                    </button>
                </div>

                <!-- Cryptocurrency Payment -->
                <div class="payment-method crypto-payment">
                    <div class="method-header">
                        <div class="method-icon">₿</div>
                        <div class="method-info">
                            <h3>Cryptocurrency</h3>
                            <p>Pay with Bitcoin, Ethereum, or stablecoins</p>
                        </div>
                        <div class="method-badges">
                            <span class="badge">BTC</span>
                            <span class="badge">ETH</span>
                            <span class="badge">USDT</span>
                            <span class="badge">USDC</span>
                        </div>
                    </div>
                    
                    <div class="payment-form" id="crypto-form" style="display: none;">
                        <form id="crypto-payment-form">
                            @csrf
                            <div class="form-group">
                                <label for="crypto-currency">Select Cryptocurrency</label>
                                <select id="crypto-currency" name="crypto_currency" class="form-control">
                                    <option value="">Choose cryptocurrency...</option>
                                    <option value="BTC">Bitcoin (BTC)</option>
                                    <option value="ETH">Ethereum (ETH)</option>
                                    <option value="USDT">Tether USD (USDT)</option>
                                    <option value="USDC">USD Coin (USDC)</option>
                                </select>
                            </div>
                            
                            <div id="crypto-amount-display" class="crypto-amount" style="display: none;">
                                <div class="amount-info">
                                    <span class="crypto-label">Amount to pay:</span>
                                    <span class="crypto-value" id="crypto-amount">0.00</span>
                                    <span class="crypto-symbol" id="crypto-symbol">BTC</span>
                                </div>
                                <div class="usd-equivalent">
                                    ≈ ${{ number_format($course->price, 2) }} USD
                                </div>
                            </div>
                            
                            <button type="submit" id="crypto-submit" class="btn btn-primary btn-block">
                                Pay with Cryptocurrency
                            </button>
                        </form>
                    </div>
                    
                    <button class="method-select-btn" onclick="selectPaymentMethod('crypto')">
                        Select Cryptocurrency
                    </button>
                </div>

                <!-- Saved Payment Methods -->
                @if($paymentMethods->count() > 0)
                    <div class="saved-methods">
                        <h3>Saved Payment Methods</h3>
                        @foreach($paymentMethods as $method)
                            <div class="saved-method">
                                <div class="method-info">
                                    <span class="method-type">{{ ucfirst($method->type) }}</span>
                                    @if($method->type === 'card')
                                        <span class="method-details">
                                            **** **** **** {{ $method->last_four }}
                                            {{ $method->brand }} expires {{ $method->exp_month }}/{{ $method->exp_year }}
                                        </span>
                                    @endif
                                </div>
                                <button class="btn btn-secondary" onclick="usePaymentMethod('{{ $method->id }}')">
                                    Use This Method
                                </button>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Security Info -->
            <div class="security-info">
                <div class="security-badges">
                    <div class="security-badge">
                        <span class="badge-icon">🔒</span>
                        <span class="badge-text">SSL Encrypted</span>
                    </div>
                    <div class="security-badge">
                        <span class="badge-icon">🛡️</span>
                        <span class="badge-text">PCI Compliant</span>
                    </div>
                    <div class="security-badge">
                        <span class="badge-icon">💯</span>
                        <span class="badge-text">30-Day Refund</span>
                    </div>
                </div>
                <p class="security-note">
                    Your payment information is secure and encrypted. We never store your card details.
                </p>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
        // Initialize Stripe
        const stripe = Stripe('{{ config("services.stripe.key") }}');
        const elements = stripe.elements();
        
        // Create card element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#ffffff',
                    '::placeholder': {
                        color: '#a0a0a0',
                    },
                },
            },
        });
        
        let selectedMethod = null;
        
        function selectPaymentMethod(method) {
            // Hide all forms
            document.querySelectorAll('.payment-form').forEach(form => {
                form.style.display = 'none';
            });
            
            // Remove active class from all methods
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected form and add active class
            const selectedElement = document.querySelector(`.${method}-payment`);
            selectedElement.classList.add('active');
            document.getElementById(`${method}-form`).style.display = 'block';
            
            selectedMethod = method;
            
            if (method === 'stripe') {
                // Mount Stripe card element
                cardElement.mount('#card-element');
            } else if (method === 'crypto') {
                loadCryptoPrices();
            }
        }
        
        function loadCryptoPrices() {
            fetch('/api/crypto/prices')
                .then(response => response.json())
                .then(data => {
                    window.cryptoPrices = data.prices;
                    
                    // Update amount when currency changes
                    document.getElementById('crypto-currency').addEventListener('change', function() {
                        updateCryptoAmount(this.value);
                    });
                })
                .catch(error => console.error('Error loading crypto prices:', error));
        }
        
        function updateCryptoAmount(currency) {
            if (!currency || !window.cryptoPrices) return;
            
            const usdAmount = {{ $course->price }};
            const cryptoAmount = usdAmount / window.cryptoPrices[currency];
            
            document.getElementById('crypto-amount').textContent = cryptoAmount.toFixed(8);
            document.getElementById('crypto-symbol').textContent = currency;
            document.getElementById('crypto-amount-display').style.display = 'block';
        }
        
        // Handle Stripe form submission
        document.getElementById('stripe-payment-form').addEventListener('submit', async (event) => {
            event.preventDefault();
            
            const submitButton = document.getElementById('stripe-submit');
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            
            try {
                const response = await fetch('{{ route("payment.process", $course) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        payment_method: 'stripe',
                        save_card: document.getElementById('save-card').checked
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = result.redirect_url;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Payment error:', error);
                showError('Payment failed: ' + error.message);
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = 'Pay ${{ number_format($course->price, 2) }}';
            }
        });
        
        // Handle crypto form submission
        document.getElementById('crypto-payment-form').addEventListener('submit', async (event) => {
            event.preventDefault();
            
            const currency = document.getElementById('crypto-currency').value;
            if (!currency) {
                showError('Please select a cryptocurrency');
                return;
            }
            
            const submitButton = document.getElementById('crypto-submit');
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            
            try {
                const response = await fetch('{{ route("payment.process", $course) }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        payment_method: 'crypto',
                        crypto_currency: currency
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = result.payment_url;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Payment error:', error);
                showError('Payment failed: ' + error.message);
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = 'Pay with Cryptocurrency';
            }
        });
        
        function showError(message) {
            // Create or update error display
            let errorDiv = document.getElementById('payment-error');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'payment-error';
                errorDiv.className = 'error-message';
                document.querySelector('.payment-options').prepend(errorDiv);
            }
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        function usePaymentMethod(methodId) {
            // Implementation for using saved payment method
            console.log('Using payment method:', methodId);
        }
</script>
@endpush
