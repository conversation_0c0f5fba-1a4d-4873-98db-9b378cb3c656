/* Courses Page Styles */

.courses-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.header-content p {
    font-size: 1.125rem;
    color: #a0a0a0;
}

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filters-form {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto auto;
    gap: 1rem;
    align-items: end;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.search-input,
.filter-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.search-input::placeholder {
    color: #666666;
}

.filter-select option {
    background: #1a1a1a;
    color: #ffffff;
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.course-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.course-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-icon {
    font-size: 3rem;
    color: white;
}

.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.course-badge.free {
    background: #10b981;
    color: white;
}

.course-badge.price {
    background: #3b82f6;
    color: white;
}

.course-content {
    padding: 1.5rem;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.course-category,
.course-level {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: 500;
}

.course-category {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
}

.course-level {
    background: rgba(16, 185, 129, 0.2);
    color: #34d399;
}

.course-title {
    margin-bottom: 0.75rem;
}

.course-title a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.course-title a:hover {
    color: #3b82f6;
}

.course-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #a0a0a0;
}

.stat-icon {
    font-size: 0.875rem;
}

.course-instructor {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-bottom: 1rem;
}

.instructor-name {
    color: #ffffff;
    font-weight: 500;
}

.course-actions {
    padding: 0 1.5rem 1.5rem;
}

.btn-block {
    width: 100%;
    text-align: center;
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .filters-form {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .courses-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .course-stats {
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .courses-main {
        padding: 1rem 0;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .filters-form {
        padding: 1rem;
    }
    
    .course-content {
        padding: 1rem;
    }
    
    .course-actions {
        padding: 0 1rem 1rem;
    }
}
