/* Admin Dashboard Styles */

.admin-body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.sidebar-header p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #a0a0a0;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    color: #ffffff;
    background: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
}

.nav-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: 280px;
    padding: 0;
    overflow-x: hidden;
}

/* Top Bar */
.admin-topbar {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left h1 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
}

.topbar-left p {
    color: #a0a0a0;
    margin: 0;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666666;
}

.status-indicator.healthy {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.status-indicator.warning {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
}

.status-indicator.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #a0a0a0;
}

.user-menu .btn-link {
    color: #a0a0a0;
    text-decoration: none;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.user-menu .btn-link:hover {
    color: #ffffff;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.metric-content h3 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
}

.metric-content p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
}

.metric-change.up {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.metric-change.down {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.metric-change.stable {
    color: #a0a0a0;
    background: rgba(160, 160, 160, 0.1);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 0 2rem 2rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
}

.chart-header {
    margin-bottom: 1rem;
}

.chart-header h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 0 2rem 2rem;
}

.content-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.view-all {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-all:hover {
    color: #2563eb;
}

/* Activities List */
.activities-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.activity-icon.success {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.activity-icon.primary {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.activity-icon.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.activity-content h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.activity-content p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0 0 0.25rem 0;
}

.activity-time {
    color: #666666;
    font-size: 0.75rem;
}

/* Courses List */
.courses-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.course-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.course-info h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.course-info p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.course-stats {
    display: flex;
    gap: 1rem;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
}

.stat-label {
    color: #a0a0a0;
    font-size: 0.75rem;
}

/* Quick Actions */
.quick-actions {
    padding: 0 2rem 2rem;
}

.quick-actions h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 1.5rem;
}

.action-text {
    font-weight: 500;
}

/* Empty States */
.no-activities,
.no-courses {
    color: #a0a0a0;
    text-align: center;
    padding: 2rem;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .charts-section,
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .admin-topbar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .topbar-right {
        gap: 1rem;
    }
    
    .metrics-grid,
    .charts-section,
    .content-grid {
        padding: 1rem;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
