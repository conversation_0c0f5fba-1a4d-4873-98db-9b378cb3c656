@extends('layouts.app')

@section('title', 'Payment Management - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}" class="active">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>Payment Management</h1>
                    <p>Monitor and manage all platform transactions</p>
                </div>
                <div class="header-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportPayments()">
                        📊 Export Report
                    </button>
                    <button type="button" class="btn btn-primary" onclick="refreshPayments()">
                        🔄 Refresh
                    </button>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <!-- Revenue Stats -->
            <div class="revenue-stats">
                <div class="stat-card primary">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                        <h3>${{ number_format($totalRevenue, 2) }}</h3>
                        <p>Total Revenue</p>
                        <span class="stat-change positive">+{{ number_format($revenueGrowth, 1) }}% this month</span>
                    </div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-icon">✅</div>
                    <div class="stat-info">
                        <h3>{{ $successfulPayments }}</h3>
                        <p>Successful Payments</p>
                        <span class="stat-change positive">{{ number_format($successRate, 1) }}% success rate</span>
                    </div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-info">
                        <h3>{{ $pendingPayments }}</h3>
                        <p>Pending Payments</p>
                        <span class="stat-change neutral">Awaiting confirmation</span>
                    </div>
                </div>
                
                <div class="stat-card danger">
                    <div class="stat-icon">❌</div>
                    <div class="stat-info">
                        <h3>{{ $failedPayments }}</h3>
                        <p>Failed Payments</p>
                        <span class="stat-change negative">{{ number_format($failureRate, 1) }}% failure rate</span>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Breakdown -->
            <div class="payment-methods-section">
                <h3>Payment Methods Breakdown</h3>
                <div class="methods-grid">
                    <div class="method-card">
                        <div class="method-icon">💳</div>
                        <div class="method-info">
                            <h4>Stripe</h4>
                            <p>${{ number_format($stripeRevenue, 2) }}</p>
                            <span class="method-percentage">{{ number_format($stripePercentage, 1) }}%</span>
                        </div>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">₿</div>
                        <div class="method-info">
                            <h4>Bitcoin</h4>
                            <p>${{ number_format($bitcoinRevenue, 2) }}</p>
                            <span class="method-percentage">{{ number_format($bitcoinPercentage, 1) }}%</span>
                        </div>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">Ξ</div>
                        <div class="method-info">
                            <h4>Ethereum</h4>
                            <p>${{ number_format($ethereumRevenue, 2) }}</p>
                            <span class="method-percentage">{{ number_format($ethereumPercentage, 1) }}%</span>
                        </div>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">💎</div>
                        <div class="method-info">
                            <h4>Other Crypto</h4>
                            <p>${{ number_format($otherCryptoRevenue, 2) }}</p>
                            <span class="method-percentage">{{ number_format($otherCryptoPercentage, 1) }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" action="{{ route('admin.payments.index') }}" class="filters-form">
                    <div class="filter-group">
                        <input type="text" 
                               name="search" 
                               placeholder="Search by user, course, or transaction ID..." 
                               value="{{ request('search') }}"
                               class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <select name="status" class="filter-select">
                            <option value="">All Status</option>
                            <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                            <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>Refunded</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="payment_method" class="filter-select">
                            <option value="">All Methods</option>
                            <option value="stripe" {{ request('payment_method') === 'stripe' ? 'selected' : '' }}>Stripe</option>
                            <option value="crypto" {{ request('payment_method') === 'crypto' ? 'selected' : '' }}>Cryptocurrency</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <input type="date" 
                               name="date_from" 
                               value="{{ request('date_from') }}"
                               class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <input type="date" 
                               name="date_to" 
                               value="{{ request('date_to') }}"
                               class="filter-input">
                    </div>
                    
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="{{ route('admin.payments.index') }}" class="btn btn-outline">Clear</a>
                </form>
            </div>

            <!-- Payments Table -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Recent Payments ({{ $payments->total() }})</h3>
                    <div class="table-actions">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="exportSelected()">
                            📊 Export Selected
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="bulkRefund()">
                            💸 Bulk Refund
                        </button>
                    </div>
                </div>
                
                <div class="table-wrapper">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>Transaction</th>
                                <th>User</th>
                                <th>Course</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($payments as $payment)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="payment_ids[]" value="{{ $payment->id }}" class="payment-checkbox">
                                    </td>
                                    <td>
                                        <div class="transaction-info">
                                            <span class="transaction-id">#{{ $payment->id }}</span>
                                            @if($payment->payment_intent_id)
                                                <span class="payment-intent">{{ Str::limit($payment->payment_intent_id, 20) }}</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <span class="user-name">{{ $payment->user->name }}</span>
                                            <span class="user-email">{{ $payment->user->email }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="course-info">
                                            <span class="course-title">{{ $payment->course->title }}</span>
                                            <span class="course-instructor">by {{ $payment->course->instructor->name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="amount-info">
                                            <span class="amount">${{ number_format($payment->amount, 2) }}</span>
                                            @if($payment->currency !== 'USD')
                                                <span class="currency">{{ $payment->currency }}</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="method-badge method-{{ $payment->payment_method }}">
                                            @if($payment->payment_method === 'stripe')
                                                💳 Stripe
                                            @elseif($payment->payment_method === 'crypto')
                                                @if($payment->crypto_currency === 'BTC')
                                                    ₿ Bitcoin
                                                @elseif($payment->crypto_currency === 'ETH')
                                                    Ξ Ethereum
                                                @else
                                                    💎 {{ $payment->crypto_currency }}
                                                @endif
                                            @else
                                                💰 {{ ucfirst($payment->payment_method) }}
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ $payment->status }}">
                                            @if($payment->status === 'completed')
                                                ✅ Completed
                                            @elseif($payment->status === 'pending')
                                                ⏳ Pending
                                            @elseif($payment->status === 'failed')
                                                ❌ Failed
                                            @elseif($payment->status === 'refunded')
                                                💸 Refunded
                                            @else
                                                ❓ {{ ucfirst($payment->status) }}
                                            @endif
                                        </span>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <span class="date">{{ $payment->created_at ? $payment->created_at->format('M j, Y') : 'Unknown' }}</span>
                                            <span class="time">{{ $payment->created_at ? $payment->created_at->format('g:i A') : '--:--' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.payments.show', $payment) }}" class="btn btn-sm btn-info" title="View Details">
                                                👁️
                                            </a>
                                            @if($payment->status === 'completed')
                                                <button type="button" class="btn btn-sm btn-warning" onclick="refundPayment({{ $payment->id }})" title="Refund">
                                                    💸
                                                </button>
                                            @endif
                                            @if($payment->status === 'pending')
                                                <button type="button" class="btn btn-sm btn-success" onclick="confirmPayment({{ $payment->id }})" title="Confirm">
                                                    ✅
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="cancelPayment({{ $payment->id }})" title="Cancel">
                                                    ❌
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="no-data">
                                        <div class="no-data-message">
                                            <div class="no-data-icon">💰</div>
                                            <h3>No payments found</h3>
                                            <p>No payments match your current filters.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($payments->hasPages())
                    <div class="pagination-wrapper">
                        {{ $payments->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </main>
</div>

<style>
.revenue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.stat-card.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.stat-card.warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-info h3 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.stat-change.positive {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.stat-change.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.stat-change.neutral {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.payment-methods-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.payment-methods-section h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.method-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.method-icon {
    font-size: 2rem;
}

.method-info h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.method-info p {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.method-percentage {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.transaction-info {
    display: flex;
    flex-direction: column;
}

.transaction-id {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
}

.payment-intent {
    color: #a0a0a0;
    font-size: 0.75rem;
    font-family: monospace;
}

.user-info, .course-info {
    display: flex;
    flex-direction: column;
}

.user-name, .course-title {
    color: #ffffff;
    font-weight: 500;
    font-size: 0.875rem;
}

.user-email, .course-instructor {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.amount-info {
    display: flex;
    flex-direction: column;
}

.amount {
    color: #ffffff;
    font-weight: 700;
    font-size: 1rem;
}

.currency {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.method-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.method-stripe {
    background: rgba(99, 102, 241, 0.2);
    color: #6366f1;
}

.method-crypto {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-failed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-refunded {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date {
    color: #ffffff;
    font-size: 0.875rem;
}

.time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .revenue-stats {
        grid-template-columns: 1fr;
    }
    
    .methods-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.payment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function refundPayment(paymentId) {
    if (confirm('Are you sure you want to refund this payment? This action cannot be undone.')) {
        // Implementation for payment refund
        console.log('Refund payment:', paymentId);
    }
}

function confirmPayment(paymentId) {
    if (confirm('Confirm this payment as completed?')) {
        // Implementation for payment confirmation
        console.log('Confirm payment:', paymentId);
    }
}

function cancelPayment(paymentId) {
    if (confirm('Cancel this pending payment?')) {
        // Implementation for payment cancellation
        console.log('Cancel payment:', paymentId);
    }
}

function exportPayments() {
    // Implementation for payment export
    console.log('Export payments');
}

function refreshPayments() {
    location.reload();
}

function exportSelected() {
    const selectedPayments = document.querySelectorAll('.payment-checkbox:checked');
    if (selectedPayments.length === 0) {
        alert('Please select at least one payment.');
        return;
    }
    
    // Implementation for selected export
    console.log('Export selected payments:', selectedPayments.length);
}

function bulkRefund() {
    const selectedPayments = document.querySelectorAll('.payment-checkbox:checked');
    if (selectedPayments.length === 0) {
        alert('Please select at least one payment.');
        return;
    }
    
    if (confirm(`Are you sure you want to refund ${selectedPayments.length} payments? This action cannot be undone.`)) {
        // Implementation for bulk refund
        console.log('Bulk refund payments:', selectedPayments.length);
    }
}
</script>
@endsection
