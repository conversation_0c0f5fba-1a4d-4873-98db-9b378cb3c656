<?php

return [
    /*
    |--------------------------------------------------------------------------
    | The Real World LMS Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options specific to The Real World
    | Learning Management System.
    |
    */

    'name' => env('LMS_NAME', 'The Real World'),
    'tagline' => env('LMS_TAGLINE', 'Escape The Matrix'),
    'description' => env('LMS_DESCRIPTION', 'Join The Real World and learn the skills to escape the matrix. Build wealth, develop mindset, and achieve financial freedom.'),

    /*
    |--------------------------------------------------------------------------
    | Course Settings
    |--------------------------------------------------------------------------
    */
    'courses' => [
        'max_students_default' => 1000,
        'max_lessons_per_course' => 100,
        'max_video_size_mb' => 500,
        'max_thumbnail_size_mb' => 5,
        'allowed_video_formats' => ['mp4', 'mov', 'avi', 'wmv'],
        'allowed_image_formats' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'default_difficulty' => 'beginner',
        'auto_publish' => false,
        'require_approval' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Community Settings
    |--------------------------------------------------------------------------
    */
    'communities' => [
        'max_members_default' => 10000,
        'max_posts_per_day' => 10,
        'max_post_length' => 5000,
        'allow_images' => true,
        'allow_videos' => true,
        'allow_links' => true,
        'moderation_enabled' => true,
        'auto_approve_posts' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Chat Settings
    |--------------------------------------------------------------------------
    */
    'chat' => [
        'max_rooms_per_user' => 50,
        'max_message_length' => 2000,
        'max_file_size_mb' => 10,
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'message_history_days' => 365,
        'typing_indicator_timeout' => 3, // seconds
        'online_timeout' => 5, // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    */
    'payments' => [
        'currency' => 'USD',
        'tax_rate' => 0.08, // 8%
        'refund_period_days' => 30,
        'min_course_price' => 1.00,
        'max_course_price' => 9999.99,
        'stripe_fee_percentage' => 0.029, // 2.9%
        'stripe_fee_fixed' => 0.30, // $0.30
        'crypto_confirmation_blocks' => 3,
        'crypto_timeout_minutes' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | User Settings
    |--------------------------------------------------------------------------
    */
    'users' => [
        'max_avatar_size_mb' => 2,
        'allowed_avatar_formats' => ['jpg', 'jpeg', 'png'],
        'default_role' => 'student',
        'email_verification_required' => true,
        'password_min_length' => 8,
        'session_timeout_minutes' => 120,
        'max_login_attempts' => 5,
        'lockout_duration_minutes' => 15,
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'max_per_user' => 1000,
        'cleanup_after_days' => 90,
        'email_enabled' => true,
        'push_enabled' => true,
        'sms_enabled' => false,
        'digest_frequency' => 'daily', // daily, weekly, never
        'types' => [
            'course_enrollment' => true,
            'payment_success' => true,
            'payment_failed' => true,
            'course_completed' => true,
            'new_message' => true,
            'community_post' => true,
            'achievement_earned' => true,
            'system_update' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | File Storage Settings
    |--------------------------------------------------------------------------
    */
    'storage' => [
        'disk' => env('LMS_STORAGE_DISK', 'public'),
        'course_videos_path' => 'courses/videos',
        'course_thumbnails_path' => 'courses/thumbnails',
        'user_avatars_path' => 'users/avatars',
        'community_images_path' => 'communities/images',
        'chat_attachments_path' => 'chat/attachments',
        'temp_uploads_path' => 'temp',
        'cleanup_temp_after_hours' => 24,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'csrf_protection' => true,
        'xss_protection' => true,
        'content_security_policy' => true,
        'rate_limiting' => true,
        'ip_whitelist' => [],
        'ip_blacklist' => [],
        'admin_ip_whitelist' => [],
        'two_factor_auth' => false,
        'password_history' => 5,
        'force_https' => env('FORCE_HTTPS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    */
    'analytics' => [
        'enabled' => env('ANALYTICS_ENABLED', true),
        'google_analytics_id' => env('GOOGLE_ANALYTICS_ID'),
        'facebook_pixel_id' => env('FACEBOOK_PIXEL_ID'),
        'track_user_activity' => true,
        'track_course_progress' => true,
        'track_payment_events' => true,
        'retention_days' => 365,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    */
    'api' => [
        'rate_limit_per_minute' => 60,
        'rate_limit_per_hour' => 1000,
        'pagination_default' => 20,
        'pagination_max' => 100,
        'cache_ttl_seconds' => 300,
        'version' => 'v1',
        'documentation_enabled' => env('API_DOCS_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Integration
    |--------------------------------------------------------------------------
    */
    'social' => [
        'facebook_app_id' => env('FACEBOOK_APP_ID'),
        'twitter_handle' => env('TWITTER_HANDLE', '@therealworld'),
        'instagram_handle' => env('INSTAGRAM_HANDLE', '@therealworld'),
        'youtube_channel' => env('YOUTUBE_CHANNEL'),
        'discord_server' => env('DISCORD_SERVER'),
        'telegram_channel' => env('TELEGRAM_CHANNEL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Settings
    |--------------------------------------------------------------------------
    */
    'email' => [
        'from_name' => env('MAIL_FROM_NAME', 'The Real World'),
        'from_address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'support_email' => env('SUPPORT_EMAIL', '<EMAIL>'),
        'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),
        'templates' => [
            'welcome' => 'emails.welcome',
            'course_enrollment' => 'emails.course-enrollment',
            'payment_success' => 'emails.payment-success',
            'password_reset' => 'emails.password-reset',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'real_time_chat' => env('FEATURE_REAL_TIME_CHAT', true),
        'live_notifications' => env('FEATURE_LIVE_NOTIFICATIONS', true),
        'advanced_search' => env('FEATURE_ADVANCED_SEARCH', true),
        'analytics_tracking' => env('FEATURE_ANALYTICS', true),
        'video_streaming' => env('FEATURE_VIDEO_STREAMING', true),
        'crypto_payments' => env('FEATURE_CRYPTO_PAYMENTS', true),
        'community_forums' => env('FEATURE_COMMUNITY_FORUMS', true),
        'achievements' => env('FEATURE_ACHIEVEMENTS', true),
        'leaderboards' => env('FEATURE_LEADERBOARDS', true),
        'certificates' => env('FEATURE_CERTIFICATES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Settings
    |--------------------------------------------------------------------------
    */
    'maintenance' => [
        'enabled' => env('MAINTENANCE_MODE', false),
        'message' => env('MAINTENANCE_MESSAGE', 'The Real World is currently undergoing maintenance. We\'ll be back soon!'),
        'allowed_ips' => explode(',', env('MAINTENANCE_ALLOWED_IPS', '')),
        'retry_after' => env('MAINTENANCE_RETRY_AFTER', 3600), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Settings
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => env('BACKUP_ENABLED', true),
        'frequency' => env('BACKUP_FREQUENCY', 'daily'), // daily, weekly, monthly
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30),
        'include_uploads' => env('BACKUP_INCLUDE_UPLOADS', true),
        'notification_email' => env('BACKUP_NOTIFICATION_EMAIL'),
    ],
];
