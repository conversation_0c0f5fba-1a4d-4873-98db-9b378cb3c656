<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

use Symfony\Polyfill\Intl\Icu\NumberFormatter as NumberFormatterPolyfill;

/**
 * Stub implementation for the NumberFormatter class of the intl extension.
 *
 * <AUTHOR> <bschuss<PERSON>@gmail.com>
 *
 * @see IntlNumberFormatter
 */
class NumberFormatter extends NumberFormatterPolyfill
{
}
