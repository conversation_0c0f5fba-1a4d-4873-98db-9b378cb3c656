<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'Admin Panel'); ?> - <?php echo e(config('app.name')); ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/admin.css')); ?>" rel="stylesheet">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="admin-body">
    <!-- Admin Sidebar -->
    <div class="admin-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <h2>🛡️ Admin Panel</h2>
                <p>The Real World</p>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.users.*') ? 'active' : ''); ?>">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">Users</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.courses.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.courses.*') ? 'active' : ''); ?>">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">Courses</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.payments.*') ? 'active' : ''); ?>">
                        <span class="nav-icon">💳</span>
                        <span class="nav-text">Payments</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.analytics')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.analytics*') ? 'active' : ''); ?>">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.roles.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.roles.*') ? 'active' : ''); ?>">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">Roles & Permissions</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.settings')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>
                
                <li class="nav-divider"></li>
                
                <li class="nav-item">
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">Back to Site</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="nav-form">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="nav-link nav-logout">
                            <span class="nav-icon">🚪</span>
                            <span class="nav-text">Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>
    
    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Top Bar -->
        <div class="admin-topbar">
            <div class="topbar-left">
                <button type="button" class="sidebar-toggle" onclick="toggleSidebar()">
                    <span class="hamburger"></span>
                </button>
                <div class="breadcrumb">
                    <span class="breadcrumb-item">Admin</span>
                    <?php if(isset($breadcrumb)): ?>
                        <?php $__currentLoopData = $breadcrumb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="breadcrumb-separator">›</span>
                            <span class="breadcrumb-item"><?php echo e($item); ?></span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="topbar-right">
                <div class="admin-user">
                    <div class="user-avatar">
                        <?php if(Auth::user()->avatar): ?>
                            <img src="<?php echo e(Storage::url(Auth::user()->avatar)); ?>" alt="<?php echo e(Auth::user()->name); ?>">
                        <?php else: ?>
                            <div class="avatar-placeholder">
                                <?php echo e(strtoupper(substr(Auth::user()->name, 0, 2))); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?php echo e(Auth::user()->name); ?></span>
                        <span class="user-role">Administrator</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page Content -->
        <div class="admin-content">
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="alert alert-success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">
                        <strong>Success!</strong>
                        <p><?php echo e(session('success')); ?></p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">
                        <strong>Error!</strong>
                        <p><?php echo e(session('error')); ?></p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('warning')): ?>
                <div class="alert alert-warning">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <strong>Warning!</strong>
                        <p><?php echo e(session('warning')); ?></p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="alert alert-info">
                    <div class="alert-icon">ℹ️</div>
                    <div class="alert-content">
                        <strong>Info!</strong>
                        <p><?php echo e(session('info')); ?></p>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>
            
            <!-- Main Content -->
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>
    
    <!-- Custom JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/admin.js')); ?>"></script>
    
    <script>
        // Sidebar toggle functionality
        function toggleSidebar() {
            document.body.classList.toggle('sidebar-collapsed');
            localStorage.setItem('sidebarCollapsed', document.body.classList.contains('sidebar-collapsed'));
        }
        
        // Restore sidebar state
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            document.body.classList.add('sidebar-collapsed');
        }
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/admin.blade.php ENDPATH**/ ?>