<?php $__env->startSection('title', 'Help Center'); ?>

<?php $__env->startSection('content'); ?>
<div class="help-page">
    <div class="page-header">
        <h1>❓ Help Center</h1>
        <p class="page-subtitle">Find answers to common questions and get support</p>
    </div>

    <div class="help-search">
        <div class="search-box">
            <input type="text" placeholder="Search for help articles..." class="search-input">
            <button type="button" class="search-btn">🔍</button>
        </div>
    </div>

    <div class="help-categories">
        <div class="category-card">
            <div class="category-icon">🚀</div>
            <h3>Getting Started</h3>
            <p>Learn the basics of using The Real World platform</p>
            <ul class="help-links">
                <li><a href="#getting-started">Creating Your Account</a></li>
                <li><a href="#getting-started">Navigating the Dashboard</a></li>
                <li><a href="#getting-started">Enrolling in Courses</a></li>
                <li><a href="#getting-started">Joining Communities</a></li>
            </ul>
        </div>

        <div class="category-card">
            <div class="category-icon">📚</div>
            <h3>Courses & Learning</h3>
            <p>Everything about courses, lessons, and progress tracking</p>
            <ul class="help-links">
                <li><a href="#courses">How to Access Course Materials</a></li>
                <li><a href="#courses">Tracking Your Progress</a></li>
                <li><a href="#courses">Downloading Resources</a></li>
                <li><a href="#courses">Course Completion Certificates</a></li>
            </ul>
        </div>

        <div class="category-card">
            <div class="category-icon">💳</div>
            <h3>Billing & Payments</h3>
            <p>Payment methods, billing, and subscription management</p>
            <ul class="help-links">
                <li><a href="#billing">Payment Methods</a></li>
                <li><a href="#billing">Managing Subscriptions</a></li>
                <li><a href="#billing">Refund Policy</a></li>
                <li><a href="#billing">Billing History</a></li>
            </ul>
        </div>

        <div class="category-card">
            <div class="category-icon">🏘️</div>
            <h3>Community</h3>
            <p>Using chat, forums, and connecting with other members</p>
            <ul class="help-links">
                <li><a href="#community">Joining Chat Rooms</a></li>
                <li><a href="#community">Community Guidelines</a></li>
                <li><a href="#community">Reporting Issues</a></li>
                <li><a href="#community">Private Messaging</a></li>
            </ul>
        </div>

        <div class="category-card">
            <div class="category-icon">⚙️</div>
            <h3>Account Settings</h3>
            <p>Managing your profile, notifications, and preferences</p>
            <ul class="help-links">
                <li><a href="#account">Updating Profile Information</a></li>
                <li><a href="#account">Changing Password</a></li>
                <li><a href="#account">Notification Settings</a></li>
                <li><a href="#account">Privacy Settings</a></li>
            </ul>
        </div>

        <div class="category-card">
            <div class="category-icon">🛠️</div>
            <h3>Technical Support</h3>
            <p>Troubleshooting and technical assistance</p>
            <ul class="help-links">
                <li><a href="#technical">Browser Compatibility</a></li>
                <li><a href="#technical">Video Playback Issues</a></li>
                <li><a href="#technical">Mobile App Support</a></li>
                <li><a href="#technical">Connectivity Problems</a></li>
            </ul>
        </div>
    </div>

    <div class="quick-help">
        <h2>Quick Help</h2>
        <div class="quick-help-grid">
            <div class="quick-help-item">
                <h3>🔐 Can't log in?</h3>
                <p>Try resetting your password or check if your account is verified.</p>
                <a href="<?php echo e(route('password.request')); ?>" class="help-link">Reset Password</a>
            </div>

            <div class="quick-help-item">
                <h3>📱 Mobile App</h3>
                <p>Download our mobile app for learning on the go.</p>
                <a href="#" class="help-link">Download App</a>
            </div>

            <div class="quick-help-item">
                <h3>💬 Live Support</h3>
                <p>Chat with our support team for immediate assistance.</p>
                <a href="<?php echo e(route('contact')); ?>" class="help-link">Contact Support</a>
            </div>

            <div class="quick-help-item">
                <h3>📧 Email Updates</h3>
                <p>Stay updated with course announcements and platform news.</p>
                <a href="#" class="help-link">Manage Notifications</a>
            </div>
        </div>
    </div>

    <div class="help-footer">
        <div class="help-footer-content">
            <h3>Still need help?</h3>
            <p>Our support team is here to assist you with any questions or issues.</p>
            <div class="help-footer-actions">
                <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">Contact Support</a>
                <a href="mailto:<EMAIL>" class="btn btn-secondary">Email Us</a>
            </div>
        </div>
    </div>
</div>

<style>
.help-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin: 0;
}

.help-search {
    max-width: 600px;
    margin: 0 auto 3rem;
}

.search-box {
    display: flex;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    font-size: 1rem;
    outline: none;
}

.search-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.category-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.category-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.category-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.help-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-links li {
    margin-bottom: 0.5rem;
}

.help-links a {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.help-links a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.quick-help {
    margin-bottom: 4rem;
}

.quick-help h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #1e293b;
    text-align: center;
}

.quick-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.quick-help-item {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.quick-help-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1e293b;
}

.quick-help-item p {
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.help-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.help-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.help-footer {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
}

.help-footer-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.help-footer-content p {
    color: #64748b;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.help-footer-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

@media (max-width: 768px) {
    .help-page {
        padding: 1rem;
    }
    
    .help-categories {
        grid-template-columns: 1fr;
    }
    
    .quick-help-grid {
        grid-template-columns: 1fr;
    }
    
    .help-footer-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<script>
// Simple search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    
    function performSearch() {
        const query = searchInput.value.toLowerCase().trim();
        if (query) {
            // In a real implementation, this would search through help articles
            alert(`Searching for: "${query}"\n\nThis would normally search through our help database and show relevant articles.`);
        }
    }
    
    searchBtn.addEventListener('click', performSearch);
    
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/pages/help.blade.php ENDPATH**/ ?>