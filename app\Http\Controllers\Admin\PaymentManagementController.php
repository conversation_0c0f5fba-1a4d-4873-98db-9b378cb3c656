<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Course;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view payments');
    }

    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        $query = Payment::with(['user', 'course', 'course.instructor']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('course', function($courseQuery) use ($search) {
                    $courseQuery->where('title', 'like', "%{$search}%");
                })
                ->orWhere('transaction_id', 'like', "%{$search}%");
            });
        }

        $payments = $query->latest()->paginate(20);

        // Get summary statistics
        $stats = [
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->sum('amount'),
            'refunded_amount' => Payment::where('status', 'refunded')->sum('amount'),
            'total_transactions' => Payment::count(),
            'this_month_revenue' => Payment::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
        ];

        return view('admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->load(['user', 'course', 'course.instructor']);

        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Process a refund for the specified payment.
     */
    public function processRefund(Request $request, Payment $payment)
    {
        $this->authorize('refund', $payment);

        $request->validate([
            'refund_amount' => 'required|numeric|min:0.01|max:' . $payment->amount,
            'refund_reason' => 'required|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Update payment status
            $payment->update([
                'status' => 'refunded',
                'refund_amount' => $request->refund_amount,
                'refund_reason' => $request->refund_reason,
                'refunded_at' => now(),
                'refunded_by' => auth()->id(),
            ]);

            // Here you would integrate with your payment processor
            // to actually process the refund (Stripe, PayPal, etc.)

            // For now, we'll just log the refund
            \Log::info('Refund processed', [
                'payment_id' => $payment->id,
                'amount' => $request->refund_amount,
                'reason' => $request->refund_reason,
                'processed_by' => auth()->user()->name,
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Refund processed successfully.');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()->with('error', 'Failed to process refund: ' . $e->getMessage());
        }
    }

    /**
     * Export payments data.
     */
    public function export(Request $request)
    {
        $this->authorize('export reports');

        $query = Payment::with(['user', 'course', 'course.instructor']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->get();

        $data = $payments->map(function($payment) {
            return [
                'transaction_id' => $payment->transaction_id,
                'user_name' => $payment->user->name,
                'user_email' => $payment->user->email,
                'course_title' => $payment->course->title,
                'instructor_name' => $payment->course->instructor->name,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'payment_method' => $payment->payment_method,
                'status' => $payment->status,
                'created_at' => $payment->created_at->format('Y-m-d H:i:s'),
                'refund_amount' => $payment->refund_amount,
                'refund_reason' => $payment->refund_reason,
            ];
        });

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="payments_export_' . now()->format('Y_m_d_H_i_s') . '.csv"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'Transaction ID', 'User Name', 'User Email', 'Course Title', 'Instructor Name',
                'Amount', 'Currency', 'Payment Method', 'Status', 'Created At',
                'Refund Amount', 'Refund Reason'
            ]);

            foreach ($data as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get payment analytics data.
     */
    public function analytics()
    {
        $this->authorize('view financial analytics');

        $analytics = [
            'revenue_overview' => $this->getRevenueOverview(),
            'payment_methods' => $this->getPaymentMethodBreakdown(),
            'monthly_trends' => $this->getMonthlyTrends(),
            'top_courses' => $this->getTopCoursesByRevenue(),
            'instructor_earnings' => $this->getInstructorEarnings(),
            'refund_analysis' => $this->getRefundAnalysis(),
        ];

        return view('admin.payments.analytics', compact('analytics'));
    }

    /**
     * Get revenue overview.
     */
    private function getRevenueOverview()
    {
        return [
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'this_month' => Payment::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'last_month' => Payment::where('status', 'completed')
                ->whereMonth('created_at', now()->subMonth()->month)
                ->whereYear('created_at', now()->subMonth()->year)
                ->sum('amount'),
            'pending_amount' => Payment::where('status', 'pending')->sum('amount'),
            'refunded_amount' => Payment::where('status', 'refunded')->sum('amount'),
            'average_transaction' => Payment::where('status', 'completed')->avg('amount'),
        ];
    }

    /**
     * Get payment method breakdown.
     */
    private function getPaymentMethodBreakdown()
    {
        return Payment::select('payment_method')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount) as total_amount')
            ->selectRaw('AVG(amount) as avg_amount')
            ->where('status', 'completed')
            ->groupBy('payment_method')
            ->orderByDesc('total_amount')
            ->get();
    }

    /**
     * Get monthly trends.
     */
    private function getMonthlyTrends()
    {
        $last12Months = collect();

        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = Payment::where('status', 'completed')
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('amount');

            $transactions = Payment::where('status', 'completed')
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();

            $last12Months->push([
                'month' => $month->format('M Y'),
                'revenue' => $revenue,
                'transactions' => $transactions,
                'avg_transaction' => $transactions > 0 ? $revenue / $transactions : 0,
            ]);
        }

        return $last12Months;
    }

    /**
     * Get top courses by revenue.
     */
    private function getTopCoursesByRevenue()
    {
        return Course::select('courses.*')
            ->selectRaw('SUM(payments.amount) as total_revenue')
            ->selectRaw('COUNT(payments.id) as total_sales')
            ->selectRaw('AVG(payments.amount) as avg_sale_amount')
            ->join('payments', 'courses.id', '=', 'payments.course_id')
            ->where('payments.status', 'completed')
            ->groupBy('courses.id')
            ->orderByDesc('total_revenue')
            ->take(10)
            ->get();
    }

    /**
     * Get instructor earnings.
     */
    private function getInstructorEarnings()
    {
        return User::select('users.*')
            ->selectRaw('SUM(payments.amount) as total_earnings')
            ->selectRaw('COUNT(payments.id) as total_sales')
            ->selectRaw('COUNT(DISTINCT courses.id) as course_count')
            ->join('courses', 'users.id', '=', 'courses.instructor_id')
            ->join('payments', 'courses.id', '=', 'payments.course_id')
            ->where('payments.status', 'completed')
            ->groupBy('users.id')
            ->orderByDesc('total_earnings')
            ->take(10)
            ->get();
    }

    /**
     * Get refund analysis.
     */
    private function getRefundAnalysis()
    {
        $totalRefunds = Payment::where('status', 'refunded')->count();
        $totalPayments = Payment::where('status', 'completed')->count();
        $refundRate = $totalPayments > 0 ? ($totalRefunds / $totalPayments) * 100 : 0;

        return [
            'total_refunds' => $totalRefunds,
            'total_refund_amount' => Payment::where('status', 'refunded')->sum('refund_amount'),
            'refund_rate' => $refundRate,
            'avg_refund_amount' => Payment::where('status', 'refunded')->avg('refund_amount'),
            'refund_reasons' => Payment::where('status', 'refunded')
                ->whereNotNull('refund_reason')
                ->select('refund_reason')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('refund_reason')
                ->orderByDesc('count')
                ->get(),
        ];
    }

    /**
     * Approve a pending payment.
     */
    public function approve(Payment $payment)
    {
        $this->authorize('process payments');

        if ($payment->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending payments can be approved.');
        }

        $payment->update([
            'status' => 'completed',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Payment approved successfully.');
    }

    /**
     * Reject a pending payment.
     */
    public function reject(Request $request, Payment $payment)
    {
        $this->authorize('process payments');

        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        if ($payment->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending payments can be rejected.');
        }

        $payment->update([
            'status' => 'failed',
            'rejection_reason' => $request->rejection_reason,
            'rejected_at' => now(),
            'rejected_by' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'Payment rejected successfully.');
    }
}
