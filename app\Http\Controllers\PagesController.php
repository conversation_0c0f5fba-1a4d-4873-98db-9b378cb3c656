<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PagesController extends Controller
{
    /**
     * Show the about page.
     */
    public function about()
    {
        return view('pages.about');
    }

    /**
     * Show the contact page.
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * Show the help center.
     */
    public function help()
    {
        return view('pages.help');
    }

    /**
     * Show the privacy policy.
     */
    public function privacy()
    {
        return view('pages.privacy');
    }

    /**
     * Show the terms of service.
     */
    public function terms()
    {
        return view('pages.terms');
    }

    /**
     * Show the cookie policy.
     */
    public function cookies()
    {
        return view('pages.cookies');
    }

    /**
     * Show the refund policy.
     */
    public function refund()
    {
        return view('pages.refund');
    }

    /**
     * Show the blog.
     */
    public function blog()
    {
        return view('pages.blog');
    }

    /**
     * Show success stories.
     */
    public function successStories()
    {
        return view('pages.success-stories');
    }

    /**
     * Show affiliate program.
     */
    public function affiliate()
    {
        return view('pages.affiliate');
    }

    /**
     * Show careers page.
     */
    public function careers()
    {
        return view('pages.careers');
    }

    /**
     * Show press page.
     */
    public function press()
    {
        return view('pages.press');
    }

    /**
     * Show instructors listing.
     */
    public function instructors()
    {
        return view('pages.instructors');
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just return a success message
        
        return redirect()->route('contact')->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }
}
