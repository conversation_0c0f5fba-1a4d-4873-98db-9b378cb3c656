<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CheckPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:check {email : The email of the user to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check permissions for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }
        
        $this->info("Checking permissions for: {$user->name} ({$user->email})");
        $this->line('');
        
        // Show user's roles
        $roles = $user->roles;
        $this->info("User Roles:");
        foreach ($roles as $role) {
            $this->line("  - {$role->name}");
        }
        $this->line('');
        
        // Show permissions through roles
        $this->info("Permissions through roles:");
        foreach ($roles as $role) {
            $this->line("  Role: {$role->name}");
            foreach ($role->permissions as $permission) {
                $this->line("    - {$permission->name}");
            }
        }
        $this->line('');
        
        // Check specific admin permissions
        $adminPermissions = [
            'view admin dashboard',
            'manage users',
            'manage courses',
            'manage payments',
            'view analytics'
        ];
        
        $this->info("Admin Permission Check:");
        foreach ($adminPermissions as $permission) {
            $hasPermission = $user->can($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->line("  {$status} {$permission}");
        }
        
        return 0;
    }
}
