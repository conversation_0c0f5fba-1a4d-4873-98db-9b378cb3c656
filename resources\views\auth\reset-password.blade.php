@extends('layouts.app')

@section('title', 'Reset Password')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <h1>The Real World</h1>
            </div>
            <h2>Create New Password</h2>
            <p>Enter your new password below. Make sure it's strong and secure.</p>
        </div>

        <form method="POST" action="{{ route('password.store') }}" class="auth-form">
            @csrf

            <!-- Password Reset Token -->
            <input type="hidden" name="token" value="{{ $request->route('token') }}">

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       class="form-control @error('email') is-invalid @enderror" 
                       value="{{ old('email', $request->email) }}" 
                       required 
                       autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password" class="form-label">New Password</label>
                <div class="password-input-container">
                    <input type="password" 
                           id="password" 
                           name="password" 
                           class="form-control @error('password') is-invalid @enderror" 
                           required
                           placeholder="Enter your new password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        👁️
                    </button>
                </div>
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                <div class="password-input-container">
                    <input type="password" 
                           id="password_confirmation" 
                           name="password_confirmation" 
                           class="form-control" 
                           required
                           placeholder="Confirm your new password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                        👁️
                    </button>
                </div>
            </div>

            <div class="password-requirements">
                <h4>Password Requirements:</h4>
                <ul>
                    <li id="length-req">At least 8 characters long</li>
                    <li id="uppercase-req">Contains at least one uppercase letter</li>
                    <li id="lowercase-req">Contains at least one lowercase letter</li>
                    <li id="number-req">Contains at least one number</li>
                    <li id="special-req">Contains at least one special character</li>
                </ul>
            </div>

            <button type="submit" class="btn btn-primary btn-full">
                🔒 Reset Password
            </button>
        </form>

        <div class="auth-footer">
            <p>Remember your password? <a href="{{ route('login') }}">Sign in</a></p>
        </div>
    </div>

    <div class="auth-info">
        <div class="info-card">
            <div class="info-icon">🔐</div>
            <h3>Strong Password</h3>
            <p>Choose a password that's unique and hard to guess. Avoid using personal information or common words.</p>
        </div>
        
        <div class="info-card">
            <div class="info-icon">🛡️</div>
            <h3>Account Security</h3>
            <p>Your new password will be encrypted and stored securely. We never store passwords in plain text.</p>
        </div>
        
        <div class="info-card">
            <div class="info-icon">💡</div>
            <h3>Password Tips</h3>
            <p>Use a mix of letters, numbers, and symbols. Consider using a password manager to generate and store secure passwords.</p>
        </div>
    </div>
</div>

<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 4rem;
}

.auth-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 450px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.password-input-container {
    position: relative;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.password-input-container .form-control {
    padding-right: 3rem;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.form-control.is-invalid {
    border-color: #ef4444;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0a0a0;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #ffffff;
}

.invalid-feedback {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.password-requirements {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.password-requirements h4 {
    color: #3b82f6;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    transition: color 0.3s ease;
}

.password-requirements li::before {
    content: "•";
    color: #a0a0a0;
    position: absolute;
    left: 0;
    font-weight: bold;
    transition: color 0.3s ease;
}

.password-requirements li.valid {
    color: #22c55e;
}

.password-requirements li.valid::before {
    content: "✓";
    color: #22c55e;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-full {
    width: 100%;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.auth-footer a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.auth-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 300px;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
}

.info-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.info-card h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.info-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-info {
        max-width: 100%;
    }
}
</style>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    
    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}

// Password strength validation
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    
    // Check length
    const lengthReq = document.getElementById('length-req');
    if (password.length >= 8) {
        lengthReq.classList.add('valid');
    } else {
        lengthReq.classList.remove('valid');
    }
    
    // Check uppercase
    const uppercaseReq = document.getElementById('uppercase-req');
    if (/[A-Z]/.test(password)) {
        uppercaseReq.classList.add('valid');
    } else {
        uppercaseReq.classList.remove('valid');
    }
    
    // Check lowercase
    const lowercaseReq = document.getElementById('lowercase-req');
    if (/[a-z]/.test(password)) {
        lowercaseReq.classList.add('valid');
    } else {
        lowercaseReq.classList.remove('valid');
    }
    
    // Check number
    const numberReq = document.getElementById('number-req');
    if (/[0-9]/.test(password)) {
        numberReq.classList.add('valid');
    } else {
        numberReq.classList.remove('valid');
    }
    
    // Check special character
    const specialReq = document.getElementById('special-req');
    if (/[^A-Za-z0-9]/.test(password)) {
        specialReq.classList.add('valid');
    } else {
        specialReq.classList.remove('valid');
    }
});
</script>
@endsection
