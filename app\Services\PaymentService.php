<?php

namespace App\Services;

use App\Models\Course;
use App\Models\User;
use App\Models\Payment;
use App\Jobs\ProcessPayment;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected $stripeService;
    protected $cryptoService;

    public function __construct(
        StripePaymentService $stripeService,
        CryptoPaymentService $cryptoService
    ) {
        $this->stripeService = $stripeService;
        $this->cryptoService = $cryptoService;
    }

    /**
     * Create payment intent for course enrollment.
     */
    public function createPaymentIntent(User $user, Course $course, string $paymentMethod = 'stripe'): array
    {
        try {
            if ($paymentMethod === 'stripe') {
                return $this->stripeService->createPaymentIntent($user, $course);
            } elseif ($paymentMethod === 'crypto') {
                return $this->cryptoService->createPaymentIntent($user, $course);
            }

            throw new \InvalidArgumentException('Invalid payment method');

        } catch (\Exception $e) {
            Log::error('Failed to create payment intent', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'payment_method' => $paymentMethod,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Process payment confirmation.
     */
    public function confirmPayment(User $user, Course $course, string $paymentIntentId, string $paymentMethod = 'stripe'): bool
    {
        try {
            // Dispatch payment processing job
            ProcessPayment::dispatch($user, $course, $paymentIntentId, $paymentMethod);

            Log::info('Payment processing job dispatched', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'payment_intent_id' => $paymentIntentId,
                'payment_method' => $paymentMethod,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to dispatch payment processing job', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Process refund for a payment.
     */
    public function processRefund(Payment $payment, float $amount = null): bool
    {
        try {
            if ($payment->payment_method === 'stripe') {
                return $this->stripeService->processRefund($payment, $amount);
            } elseif ($payment->payment_method === 'crypto') {
                return $this->cryptoService->processRefund($payment, $amount);
            }

            throw new \InvalidArgumentException('Invalid payment method');

        } catch (\Exception $e) {
            Log::error('Failed to process refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get payment methods for a user.
     */
    public function getPaymentMethods(User $user): array
    {
        try {
            return $this->stripeService->getPaymentMethods($user);
        } catch (\Exception $e) {
            Log::error('Failed to get payment methods', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Save payment method for a user.
     */
    public function savePaymentMethod(User $user, string $paymentMethodId): bool
    {
        try {
            return $this->stripeService->savePaymentMethod($user, $paymentMethodId);
        } catch (\Exception $e) {
            Log::error('Failed to save payment method', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get cryptocurrency prices.
     */
    public function getCryptoPrices(): array
    {
        try {
            return $this->cryptoService->getPrices();
        } catch (\Exception $e) {
            Log::error('Failed to get crypto prices', [
                'error' => $e->getMessage(),
            ]);

            // Return fallback prices
            return [
                'BTC' => 45000.00,
                'ETH' => 3200.00,
                'USDT' => 1.00,
                'USDC' => 1.00,
            ];
        }
    }

    /**
     * Validate payment data.
     */
    public function validatePayment(User $user, Course $course): array
    {
        $errors = [];

        // Check if user can enroll
        if ($course->instructor_id === $user->id) {
            $errors[] = 'You cannot enroll in your own course';
        }

        // Check if course is published
        if ($course->status !== 'published') {
            $errors[] = 'Course is not available for enrollment';
        }

        // Check if course has available spots
        if ($course->max_students && $course->students_count >= $course->max_students) {
            $errors[] = 'Course is full';
        }

        // Check if user is already enrolled
        if ($user->enrolledCourses()->where('course_id', $course->id)->exists()) {
            $errors[] = 'You are already enrolled in this course';
        }

        // Check if course is free
        if ($course->price <= 0) {
            $errors[] = 'This course is free and does not require payment';
        }

        return $errors;
    }

    /**
     * Calculate total amount including taxes and fees.
     */
    public function calculateTotal(Course $course, string $currency = 'USD'): array
    {
        $subtotal = $course->price;
        $tax = $subtotal * 0.08; // 8% tax
        $processingFee = $subtotal * 0.029 + 0.30; // Stripe fees
        $total = $subtotal + $tax + $processingFee;

        return [
            'subtotal' => round($subtotal, 2),
            'tax' => round($tax, 2),
            'processing_fee' => round($processingFee, 2),
            'total' => round($total, 2),
            'currency' => $currency,
        ];
    }

    /**
     * Get payment analytics.
     */
    public function getPaymentAnalytics(User $user = null, \DateTime $startDate = null, \DateTime $endDate = null): array
    {
        $query = Payment::where('status', 'completed');

        if ($user) {
            $query->where('user_id', $user->id);
        }

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        $payments = $query->get();

        return [
            'total_payments' => $payments->count(),
            'total_revenue' => $payments->sum('amount'),
            'average_payment' => $payments->avg('amount'),
            'payment_methods' => $payments->groupBy('payment_method')->map->count(),
            'monthly_revenue' => $payments->groupBy(function ($payment) {
                return $payment->created_at->format('Y-m');
            })->map->sum('amount'),
        ];
    }
}
