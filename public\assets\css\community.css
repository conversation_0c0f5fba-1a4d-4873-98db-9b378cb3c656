/* Community Styles */

.community-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.header-content p {
    font-size: 1.125rem;
    color: #a0a0a0;
}

/* Community Layout */
.community-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.community-content {
    min-height: 600px;
}

/* Filters */
.filters-section {
    margin-bottom: 2rem;
}

.filters-form {
    display: grid;
    grid-template-columns: 2fr 1fr auto auto;
    gap: 1rem;
    align-items: end;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.search-input,
.filter-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.search-input::placeholder {
    color: #666666;
}

.filter-select option {
    background: #1a1a1a;
    color: #ffffff;
}

/* Communities Grid */
.communities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.community-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.community-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.community-header {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.community-banner {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.community-banner-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.community-icon {
    font-size: 3rem;
    color: white;
}

.community-type {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.type-badge.type-public {
    background: #10b981;
    color: white;
}

.type-badge.type-private {
    background: #ef4444;
    color: white;
}

.type-badge.type-course-specific {
    background: #3b82f6;
    color: white;
}

.community-content {
    padding: 1.5rem;
}

.community-title {
    margin-bottom: 0.75rem;
}

.community-title a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.community-title a:hover {
    color: #3b82f6;
}

.community-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.community-course {
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.course-label {
    color: #a0a0a0;
}

.course-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.course-link:hover {
    color: #2563eb;
}

.community-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #a0a0a0;
}

.stat-icon {
    font-size: 0.875rem;
}

.community-creator {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-bottom: 1rem;
}

.creator-name {
    color: #ffffff;
    font-weight: 500;
}

.community-actions {
    padding: 0 1.5rem 1.5rem;
}

.btn-block {
    width: 100%;
    text-align: center;
}

/* Sidebar */
.community-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
}

.sidebar-card h4 {
    color: #ffffff;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.popular-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-info h5 {
    margin-bottom: 0.25rem;
}

.popular-info h5 a {
    color: #ffffff;
    text-decoration: none;
    font-size: 0.875rem;
}

.popular-info h5 a:hover {
    color: #3b82f6;
}

.popular-stats {
    font-size: 0.75rem;
    color: #a0a0a0;
}

.guidelines-list {
    list-style: none;
    padding: 0;
}

.guidelines-list li {
    color: #a0a0a0;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
}

.stat-label {
    font-size: 0.75rem;
    color: #a0a0a0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .community-layout {
        grid-template-columns: 1fr;
    }
    
    .filters-form {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .communities-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .communities-grid {
        grid-template-columns: 1fr;
    }
    
    .community-stats {
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .community-main {
        padding: 1rem 0;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .filters-form {
        padding: 1rem;
    }
    
    .community-content {
        padding: 1rem;
    }
    
    .community-actions {
        padding: 0 1rem 1rem;
    }
}
