@extends('layouts.app')

@section('title', 'Payment Cancelled')

@push('styles')
    <link href="{{ asset('assets/css/payment.css') }}" rel="stylesheet">
@endpush

@section('content')
    <!-- Main Content -->
    <div class="payment-main">
        <div class="container">
            <div class="payment-cancel-container">
                <div class="cancel-card">
                    <div class="cancel-icon">
                        <div class="x-mark">✕</div>
                    </div>
                    
                    <h1>Payment Cancelled</h1>
                    <p class="cancel-message">
                        Your payment was cancelled and no charges were made to your account.
                    </p>

                    <div class="cancel-info">
                        <h3>What happened?</h3>
                        <ul>
                            <li>You chose to cancel the payment process</li>
                            <li>No money was charged to your payment method</li>
                            <li>You have not been enrolled in the course</li>
                            <li>You can try again at any time</li>
                        </ul>
                    </div>

                    <div class="next-steps">
                        <h3>What would you like to do?</h3>
                        <div class="action-buttons">
                            <a href="{{ url()->previous() }}" class="btn btn-primary">
                                🔄 Try Payment Again
                            </a>
                            <a href="{{ route('courses.index') }}" class="btn btn-secondary">
                                📚 Browse Other Courses
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                📊 Go to Dashboard
                            </a>
                        </div>
                    </div>

                    <div class="help-section">
                        <h3>Need Help?</h3>
                        <p>
                            If you experienced any issues during the payment process, 
                            our support team is here to help.
                        </p>
                        <div class="help-options">
                            <a href="mailto:<EMAIL>" class="help-link">
                                📧 Email Support
                            </a>
                            <a href="#" class="help-link">
                                💬 Live Chat
                            </a>
                            <a href="#" class="help-link">
                                📞 Call Support
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Motivation Section -->
                <div class="motivation-section">
                    <h2>Don't Let This Stop Your Journey! 💪</h2>
                    <p>
                        Every successful person has faced obstacles. The difference is they didn't give up.
                        Your path to escaping the matrix is still waiting for you.
                    </p>
                    
                    <div class="motivation-quotes">
                        <div class="quote">
                            <blockquote>
                                "The only impossible journey is the one you never begin."
                            </blockquote>
                            <cite>- Tony Robbins</cite>
                        </div>
                        <div class="quote">
                            <blockquote>
                                "Success is not final, failure is not fatal: it is the courage to continue that counts."
                            </blockquote>
                            <cite>- Winston Churchill</cite>
                        </div>
                    </div>

                    <div class="course-benefits">
                        <h3>Remember What You're Working Towards:</h3>
                        <div class="benefits-grid">
                            <div class="benefit">
                                <div class="benefit-icon">💰</div>
                                <h4>Financial Freedom</h4>
                                <p>Build multiple income streams and escape the 9-5</p>
                            </div>
                            <div class="benefit">
                                <div class="benefit-icon">🧠</div>
                                <h4>Mindset Mastery</h4>
                                <p>Develop the mental strength of successful people</p>
                            </div>
                            <div class="benefit">
                                <div class="benefit-icon">🚀</div>
                                <h4>Business Skills</h4>
                                <p>Learn proven strategies to build and scale businesses</p>
                            </div>
                            <div class="benefit">
                                <div class="benefit-icon">🌟</div>
                                <h4>Elite Network</h4>
                                <p>Connect with like-minded successful individuals</p>
                            </div>
                        </div>
                    </div>

                    <div class="cta-section">
                        <h3>Ready to Take Action?</h3>
                        <p>Don't let a technical issue stop you from changing your life.</p>
                        <a href="{{ url()->previous() }}" class="btn btn-primary btn-large">
                            🎯 Complete Your Enrollment Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
        .payment-cancel-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .cancel-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .cancel-icon {
            margin-bottom: 2rem;
        }

        .x-mark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            animation: shake 0.6s ease-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .cancel-card h1 {
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cancel-message {
            color: #a0a0a0;
            font-size: 1.25rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .cancel-info, .next-steps, .help-section {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
        }

        .cancel-info h3, .next-steps h3, .help-section h3 {
            color: #ffffff;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .cancel-info ul {
            list-style: none;
            padding: 0;
        }

        .cancel-info li {
            color: #a0a0a0;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .cancel-info li::before {
            content: "•";
            color: #ef4444;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 1.5rem 0;
            flex-wrap: wrap;
        }

        .help-options {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .help-link {
            padding: 0.75rem 1.5rem;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .help-link:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.5);
        }

        .motivation-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
        }

        .motivation-section h2 {
            color: #ffffff;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .motivation-quotes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .quote {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
        }

        .quote blockquote {
            color: #ffffff;
            font-style: italic;
            font-size: 1.125rem;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .quote cite {
            color: #a0a0a0;
            font-size: 0.875rem;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .benefit {
            text-align: center;
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .benefit h4 {
            color: #ffffff;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .benefit p {
            color: #a0a0a0;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .cta-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cta-section h3 {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        @media (max-width: 768px) {
            .payment-cancel-container {
                padding: 1rem;
            }
            
            .cancel-card, .motivation-section {
                padding: 2rem;
            }
            
            .action-buttons, .help-options {
                flex-direction: column;
            }
            
            .motivation-quotes, .benefits-grid {
                grid-template-columns: 1fr;
            }
        }
</style>
@endpush
