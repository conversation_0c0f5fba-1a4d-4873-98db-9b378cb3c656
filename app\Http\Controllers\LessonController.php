<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class LessonController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of lessons for a course.
     */
    public function index(Course $course)
    {
        $this->authorize('view', $course);

        $lessons = $course->lessons()
            ->orderBy('order')
            ->paginate(20);

        return view('lessons.index', compact('course', 'lessons'));
    }

    /**
     * Show the form for creating a new lesson.
     */
    public function create(Course $course)
    {
        $this->authorize('update', $course);

        return view('lessons.create', compact('course'));
    }

    /**
     * Store a newly created lesson.
     */
    public function store(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'video_url' => 'nullable|url',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi,wmv|max:512000', // 500MB max
            'duration_minutes' => 'nullable|integer|min:1',
            'is_preview' => 'boolean',
            'order' => 'nullable|integer|min:1',
        ]);

        // Get the next order number if not provided
        if (!isset($validated['order'])) {
            $validated['order'] = $course->lessons()->max('order') + 1;
        }

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            $videoPath = $request->file('video_file')->store('lessons/videos', 'public');
            $validated['video_url'] = Storage::url($videoPath);
        }

        $lesson = $course->lessons()->create($validated);

        return redirect()->route('instructor.lessons.index', $course)
            ->with('success', 'Lesson created successfully!');
    }

    /**
     * Display the specified lesson.
     */
    public function show(Course $course, Lesson $lesson)
    {
        // Check if user has access to this lesson
        if (!$this->canAccessLesson($lesson)) {
            abort(403, 'You do not have access to this lesson.');
        }

        $lesson->load(['course']);
        
        // Get next and previous lessons
        $nextLesson = $course->lessons()
            ->where('order', '>', $lesson->order)
            ->orderBy('order')
            ->first();

        $previousLesson = $course->lessons()
            ->where('order', '<', $lesson->order)
            ->orderByDesc('order')
            ->first();

        // Track lesson view if user is enrolled
        if (Auth::check() && $course->isUserEnrolled(Auth::user())) {
            $this->trackLessonView($lesson);
        }

        return view('lessons.show', compact('lesson', 'course', 'nextLesson', 'previousLesson'));
    }

    /**
     * Show the form for editing the lesson.
     */
    public function edit(Course $course, Lesson $lesson)
    {
        $this->authorize('update', $course);

        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        return view('lessons.edit', compact('course', 'lesson'));
    }

    /**
     * Update the specified lesson.
     */
    public function update(Request $request, Course $course, Lesson $lesson)
    {
        $this->authorize('update', $course);

        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'video_url' => 'nullable|url',
            'video_file' => 'nullable|file|mimes:mp4,mov,avi,wmv|max:512000',
            'duration_minutes' => 'nullable|integer|min:1',
            'is_preview' => 'boolean',
            'order' => 'nullable|integer|min:1',
        ]);

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            // Delete old video file if exists
            if ($lesson->video_url && Storage::disk('public')->exists(str_replace('/storage/', '', $lesson->video_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $lesson->video_url));
            }

            $videoPath = $request->file('video_file')->store('lessons/videos', 'public');
            $validated['video_url'] = Storage::url($videoPath);
        }

        $lesson->update($validated);

        return redirect()->route('instructor.lessons.index', $course)
            ->with('success', 'Lesson updated successfully!');
    }

    /**
     * Remove the specified lesson.
     */
    public function destroy(Course $course, Lesson $lesson)
    {
        $this->authorize('update', $course);

        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Delete video file if exists
        if ($lesson->video_url && Storage::disk('public')->exists(str_replace('/storage/', '', $lesson->video_url))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $lesson->video_url));
        }

        $lesson->delete();

        return redirect()->route('instructor.lessons.index', $course)
            ->with('success', 'Lesson deleted successfully!');
    }

    /**
     * Reorder lessons.
     */
    public function reorder(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $validated = $request->validate([
            'lessons' => 'required|array',
            'lessons.*.id' => 'required|exists:lessons,id',
            'lessons.*.order' => 'required|integer|min:1',
        ]);

        foreach ($validated['lessons'] as $lessonData) {
            $lesson = Lesson::find($lessonData['id']);
            if ($lesson && $lesson->course_id === $course->id) {
                $lesson->update(['order' => $lessonData['order']]);
            }
        }

        return response()->json(['success' => true]);
    }

    /**
     * Mark lesson as completed for the current user.
     */
    public function complete(Course $course, Lesson $lesson)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        if (!$course->isUserEnrolled(Auth::user())) {
            return response()->json(['error' => 'Not enrolled in course'], 403);
        }

        if ($lesson->course_id !== $course->id) {
            return response()->json(['error' => 'Lesson not found'], 404);
        }

        // Mark lesson as completed
        $enrollment = $course->enrollments()->where('user_id', Auth::id())->first();
        
        if ($enrollment) {
            // Track lesson completion
            $enrollment->completedLessons()->syncWithoutDetaching([$lesson->id => [
                'completed_at' => now(),
            ]]);

            // Update overall course progress
            $totalLessons = $course->lessons()->count();
            $completedLessons = $enrollment->completedLessons()->count();
            $progress = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;

            $enrollment->updateProgress($progress);

            return response()->json([
                'success' => true,
                'progress' => $progress,
                'completed_lessons' => $completedLessons,
                'total_lessons' => $totalLessons,
            ]);
        }

        return response()->json(['error' => 'Enrollment not found'], 404);
    }

    /**
     * Check if user can access the lesson.
     */
    private function canAccessLesson(Lesson $lesson)
    {
        $user = Auth::user();
        $course = $lesson->course;

        // Instructors can access their own lessons
        if ($user && $course->instructor_id === $user->id) {
            return true;
        }

        // Admins can access all lessons
        if ($user && $user->hasRole(['admin', 'super-admin'])) {
            return true;
        }

        // Preview lessons can be accessed by anyone
        if ($lesson->is_preview) {
            return true;
        }

        // Enrolled users can access lessons
        if ($user && $course->isUserEnrolled($user)) {
            return true;
        }

        return false;
    }

    /**
     * Track lesson view for analytics.
     */
    private function trackLessonView(Lesson $lesson)
    {
        // This would typically be stored in a lesson_views table
        // For now, we'll just update the last_accessed_at on the enrollment
        $enrollment = $lesson->course->enrollments()
            ->where('user_id', Auth::id())
            ->first();

        if ($enrollment) {
            $enrollment->update(['last_accessed_at' => now()]);
        }
    }

    /**
     * Get lesson progress for a user.
     */
    public function progress(Course $course, Lesson $lesson)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        $enrollment = $course->enrollments()->where('user_id', Auth::id())->first();
        
        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled'], 403);
        }

        $isCompleted = $enrollment->completedLessons()
            ->where('lesson_id', $lesson->id)
            ->exists();

        return response()->json([
            'is_completed' => $isCompleted,
            'overall_progress' => $enrollment->progress,
        ]);
    }

    /**
     * Download lesson resources.
     */
    public function downloadResources(Course $course, Lesson $lesson)
    {
        if (!$this->canAccessLesson($lesson)) {
            abort(403, 'You do not have access to this lesson.');
        }

        // This would handle downloading lesson resources/attachments
        // For now, return a placeholder response
        return response()->json(['message' => 'Resource download feature coming soon']);
    }
}
