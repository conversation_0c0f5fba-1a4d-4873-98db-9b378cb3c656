<nav class="main-navigation">
    <div class="nav-container">
        <div class="nav-content">
            <div class="nav-left">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="<?php echo e(route('home')); ?>" class="logo-link">
                        <span class="logo-icon">🌍</span>
                        <span class="logo-text">The Real World</span>
                    </a>
                </div>

                <!-- Main Navigation Links -->
                <div class="nav-links">
                    <a href="<?php echo e(route('courses.index')); ?>" class="nav-link <?php echo e(request()->routeIs('courses.*') ? 'active' : ''); ?>">
                        📚 Courses
                    </a>
                    <a href="<?php echo e(route('communities.index')); ?>" class="nav-link <?php echo e(request()->routeIs('communities.*') ? 'active' : ''); ?>">
                        🏘️ Community
                    </a>
                    <a href="<?php echo e(route('chat.index')); ?>" class="nav-link <?php echo e(request()->routeIs('chat.*') ? 'active' : ''); ?>">
                        💬 Chat
                    </a>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                            🏠 Dashboard
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Right Side Navigation -->
            <div class="nav-right">
                <!-- Search Bar -->
                <div class="nav-search">
                    <form action="<?php echo e(route('search.index')); ?>" method="GET" class="search-form">
                        <input type="text"
                               name="q"
                               placeholder="Search courses, instructors..."
                               class="search-input"
                               value="<?php echo e(request('q')); ?>">
                        <button type="submit" class="search-btn">🔍</button>
                    </form>
                </div>

                <?php if(auth()->guard()->check()): ?>
                    <!-- Notifications -->
                    <div class="nav-notifications">
                        <a href="<?php echo e(route('notifications.index')); ?>" class="notification-btn">
                            <span class="notification-icon">🔔</span>
                            <?php if(Auth::user()->unreadNotifications->count() > 0): ?>
                                <span class="notification-badge"><?php echo e(Auth::user()->unreadNotifications->count()); ?></span>
                            <?php endif; ?>
                        </a>
                    </div>

                    <!-- User Dropdown -->
                    <div class="nav-user-dropdown">
                        <button type="button" class="user-dropdown-btn" onclick="toggleUserDropdown()">
                            <div class="user-avatar">
                                <?php if(Auth::user()->avatar): ?>
                                    <img src="<?php echo e(Storage::url(Auth::user()->avatar)); ?>" alt="<?php echo e(Auth::user()->name); ?>">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <?php echo e(strtoupper(substr(Auth::user()->name, 0, 2))); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                            <span class="user-name"><?php echo e(Auth::user()->name); ?></span>
                            <span class="dropdown-arrow">▼</span>
                        </button>

                        <div class="user-dropdown-menu" id="userDropdownMenu" style="display: none;">
                            <div class="dropdown-header">
                                <div class="user-info">
                                    <span class="user-name"><?php echo e(Auth::user()->name); ?></span>
                                    <span class="user-email"><?php echo e(Auth::user()->email); ?></span>
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>

                            <a href="<?php echo e(route('dashboard')); ?>" class="dropdown-item">
                                <span class="item-icon">🏠</span>
                                Dashboard
                            </a>

                            <a href="<?php echo e(route('profile.show')); ?>" class="dropdown-item">
                                <span class="item-icon">👤</span>
                                Profile
                            </a>

                            <a href="<?php echo e(route('course.my-courses')); ?>" class="dropdown-item">
                                <span class="item-icon">📚</span>
                                My Courses
                            </a>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create courses')): ?>
                                <div class="dropdown-divider"></div>
                                <a href="<?php echo e(route('instructor.dashboard')); ?>" class="dropdown-item">
                                    <span class="item-icon">👨‍🏫</span>
                                    Instructor Panel
                                </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('access admin panel')): ?>
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="dropdown-item">
                                    <span class="item-icon">🛡️</span>
                                    Admin Panel
                                </a>
                            <?php endif; ?>

                            <div class="dropdown-divider"></div>

                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="dropdown-form">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item logout-btn">
                                    <span class="item-icon">🚪</span>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Guest Links -->
                    <div class="nav-auth">
                        <a href="<?php echo e(route('login')); ?>" class="auth-link login-link">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="auth-link register-link">Register</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-nav-toggle" onclick="toggleMobileMenu()">
                <span id="hamburger-icon">☰</span>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div class="mobile-nav-menu" id="mobileNavMenu">
        <div class="mobile-nav-links">
            <a href="<?php echo e(route('courses.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('courses.*') ? 'active' : ''); ?>">
                📚 Courses
            </a>
            <a href="<?php echo e(route('communities.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('communities.*') ? 'active' : ''); ?>">
                🏘️ Community
            </a>
            <a href="<?php echo e(route('chat.index')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('chat.*') ? 'active' : ''); ?>">
                💬 Chat
            </a>
            <?php if(auth()->guard()->check()): ?>
                <a href="<?php echo e(route('dashboard')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                    🏠 Dashboard
                </a>
                <a href="<?php echo e(route('profile.show')); ?>" class="mobile-nav-link">
                    👤 Profile
                </a>
                <a href="<?php echo e(route('course.my-courses')); ?>" class="mobile-nav-link">
                    📚 My Courses
                </a>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create courses')): ?>
                    <a href="<?php echo e(route('instructor.dashboard')); ?>" class="mobile-nav-link">
                        👨‍🏫 Instructor Panel
                    </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('access admin panel')): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="mobile-nav-link">
                        🛡️ Admin Panel
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <a href="<?php echo e(route('login')); ?>" class="mobile-nav-link">
                    🔑 Login
                </a>
                <a href="<?php echo e(route('register')); ?>" class="mobile-nav-link">
                    📝 Register
                </a>
            <?php endif; ?>
        </div>

        <!-- Mobile Search -->
        <div class="mobile-search">
            <form action="<?php echo e(route('search.index')); ?>" method="GET" class="search-form">
                <input type="text"
                       name="q"
                       placeholder="Search courses, instructors..."
                       class="search-input"
                       value="<?php echo e(request('q')); ?>">
                <button type="submit" class="search-btn">🔍</button>
            </form>
        </div>

        <?php if(auth()->guard()->check()): ?>
            <!-- Mobile Logout -->
            <div class="mobile-nav-links" style="border-top: 1px solid var(--dark-border); padding-top: 1rem;">
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="mobile-nav-link" style="width: 100%; text-align: left; background: transparent; border: none; color: var(--error-color);">
                        🚪 Logout
                    </button>
                </form>
            </div>
        <?php endif; ?>
    </div>
</nav>

<script>
    // User dropdown functionality
    function toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdownMenu');
        const isVisible = dropdown.style.display === 'block';
        dropdown.style.display = isVisible ? 'none' : 'block';
    }

    // Mobile menu functionality
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobileNavMenu');
        const hamburgerIcon = document.getElementById('hamburger-icon');
        const isActive = mobileMenu.classList.contains('active');

        if (isActive) {
            mobileMenu.classList.remove('active');
            hamburgerIcon.textContent = '☰';
        } else {
            mobileMenu.classList.add('active');
            hamburgerIcon.textContent = '✕';
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('userDropdownMenu');
        const button = document.querySelector('.user-dropdown-btn');
        const mobileMenu = document.getElementById('mobileNavMenu');
        const mobileToggle = document.querySelector('.mobile-nav-toggle');

        // Close user dropdown
        if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.style.display = 'none';
        }

        // Close mobile menu
        if (mobileMenu && mobileToggle && !mobileToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
            mobileMenu.classList.remove('active');
            document.getElementById('hamburger-icon').textContent = '☰';
        }
    });

    // Search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(searchInput => {
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.closest('form').submit();
                }
            });
        });

        // Close mobile menu when clicking on nav links
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                const mobileMenu = document.getElementById('mobileNavMenu');
                mobileMenu.classList.remove('active');
                document.getElementById('hamburger-icon').textContent = '☰';
            });
        });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            const mobileMenu = document.getElementById('mobileNavMenu');
            mobileMenu.classList.remove('active');
            document.getElementById('hamburger-icon').textContent = '☰';
        }
    });
</script>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>