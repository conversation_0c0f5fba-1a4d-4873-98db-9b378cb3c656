<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Payment;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PaymentApiController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Create payment intent for course enrollment.
     */
    public function createPaymentIntent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'payment_method' => 'required|in:stripe,crypto',
            'crypto_currency' => 'required_if:payment_method,crypto|in:BTC,ETH,USDT,USDC',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $course = Course::findOrFail($request->course_id);
        $user = Auth::user();

        // Validate payment eligibility
        $errors = $this->paymentService->validatePayment($user, $course);
        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => 'Payment validation failed',
                'errors' => $errors,
            ], 422);
        }

        try {
            $paymentData = $this->paymentService->createPaymentIntent(
                $user,
                $course,
                $request->payment_method
            );

            return response()->json([
                'success' => true,
                'data' => $paymentData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment intent: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Confirm payment.
     */
    public function confirmPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'payment_intent_id' => 'required|string',
            'payment_method' => 'required|in:stripe,crypto',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $course = Course::findOrFail($request->course_id);
        $user = Auth::user();

        try {
            $success = $this->paymentService->confirmPayment(
                $user,
                $course,
                $request->payment_intent_id,
                $request->payment_method
            );

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed successfully. Enrollment is being processed.',
                    'redirect_url' => route('courses.show', $course),
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment confirmation failed',
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment confirmation error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user's payment history.
     */
    public function paymentHistory(Request $request)
    {
        $user = Auth::user();

        $payments = Payment::where('user_id', $user->id)
            ->with(['course'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $payments->items(),
            'pagination' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
            ],
        ]);
    }

    /**
     * Get cryptocurrency prices.
     */
    public function cryptoPrices()
    {
        try {
            $prices = $this->paymentService->getCryptoPrices();

            return response()->json([
                'success' => true,
                'prices' => $prices,
                'updated_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch crypto prices',
                'prices' => [
                    'BTC' => 45000.00,
                    'ETH' => 3200.00,
                    'USDT' => 1.00,
                    'USDC' => 1.00,
                ],
                'updated_at' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Get user's payment methods.
     */
    public function paymentMethods()
    {
        $user = Auth::user();

        try {
            $methods = $this->paymentService->getPaymentMethods($user);

            return response()->json([
                'success' => true,
                'data' => $methods,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch payment methods',
                'data' => [],
            ]);
        }
    }

    /**
     * Save payment method.
     */
    public function savePaymentMethod(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();

        try {
            $success = $this->paymentService->savePaymentMethod(
                $user,
                $request->payment_method_id
            );

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment method saved successfully',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save payment method',
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving payment method: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete payment method.
     */
    public function deletePaymentMethod(string $methodId)
    {
        // Implementation would depend on payment service
        return response()->json([
            'success' => true,
            'message' => 'Payment method deleted successfully',
        ]);
    }

    /**
     * Handle Stripe webhook.
     */
    public function stripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');

        try {
            // Verify webhook signature and process
            $success = app(\App\Services\StripePaymentService::class)
                ->handleWebhook($request->all(), $signature);

            return response()->json(['success' => $success]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Handle crypto webhook.
     */
    public function cryptoWebhook(Request $request)
    {
        try {
            $success = app(\App\Services\CryptoPaymentService::class)
                ->handleWebhook($request->all());

            return response()->json(['success' => $success]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
}
