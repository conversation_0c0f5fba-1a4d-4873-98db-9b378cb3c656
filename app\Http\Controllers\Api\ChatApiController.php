<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChatRoom;
use App\Models\ChatRoomMember;
use App\Models\ChatMessage as Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ChatApiController extends Controller
{
    /**
     * Get user's chat rooms.
     */
    public function rooms(Request $request)
    {
        $user = Auth::user();

        $rooms = ChatRoom::whereHas('members', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['creator', 'lastMessage'])
            ->where('is_active', true)
            ->orderBy('updated_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $rooms->items(),
            'pagination' => [
                'current_page' => $rooms->currentPage(),
                'last_page' => $rooms->lastPage(),
                'per_page' => $rooms->perPage(),
                'total' => $rooms->total(),
            ],
        ]);
    }

    /**
     * Create a new chat room.
     */
    public function createRoom(Request $request)
    {
        if (!Auth::user()->hasRole(['instructor', 'admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Instructor privileges required.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:public,private',
            'course_id' => 'nullable|exists:courses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $room = ChatRoom::create(array_merge($validator->validated(), [
            'created_by' => Auth::id(),
            'slug' => \Str::slug($request->name),
            'is_active' => true,
        ]));

        // Auto-join creator as admin
        ChatRoomMember::create([
            'chat_room_id' => $room->id,
            'user_id' => Auth::id(),
            'role' => 'admin',
            'joined_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Chat room created successfully',
            'data' => $room->load(['creator']),
        ], 201);
    }

    /**
     * Show a specific chat room.
     */
    public function showRoom(ChatRoom $room)
    {
        if (!$room->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Chat room not found or not available',
            ], 404);
        }

        // Check if user is a member for private rooms
        if ($room->type === 'private') {
            $isMember = ChatRoomMember::where('user_id', Auth::id())
                ->where('chat_room_id', $room->id)
                ->exists();

            if (!$isMember) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Private room membership required.',
                ], 403);
            }
        }

        $room->load(['creator', 'course']);

        // Check if user is a member
        $isMember = ChatRoomMember::where('user_id', Auth::id())
            ->where('chat_room_id', $room->id)
            ->exists();

        return response()->json([
            'success' => true,
            'data' => array_merge($room->toArray(), [
                'is_member' => $isMember,
                'can_join' => Auth::check() && !$isMember,
            ]),
        ]);
    }

    /**
     * Join a chat room.
     */
    public function joinRoom(ChatRoom $room)
    {
        if (!$room->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Chat room not available',
            ], 404);
        }

        $user = Auth::user();

        // Check if already a member
        $existingMember = ChatRoomMember::where('user_id', $user->id)
            ->where('chat_room_id', $room->id)
            ->first();

        if ($existingMember) {
            return response()->json([
                'success' => false,
                'message' => 'Already a member of this chat room',
            ], 400);
        }

        ChatRoomMember::create([
            'chat_room_id' => $room->id,
            'user_id' => $user->id,
            'role' => 'member',
            'joined_at' => now(),
        ]);

        // Update member count
        $room->increment('member_count');

        return response()->json([
            'success' => true,
            'message' => 'Successfully joined the chat room',
        ]);
    }

    /**
     * Leave a chat room.
     */
    public function leaveRoom(ChatRoom $room)
    {
        $user = Auth::user();

        $member = ChatRoomMember::where('user_id', $user->id)
            ->where('chat_room_id', $room->id)
            ->first();

        if (!$member) {
            return response()->json([
                'success' => false,
                'message' => 'Not a member of this chat room',
            ], 400);
        }

        // Prevent creator from leaving
        if ($room->created_by === $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Chat room creator cannot leave the room',
            ], 400);
        }

        $member->delete();

        // Update member count
        $room->decrement('member_count');

        return response()->json([
            'success' => true,
            'message' => 'Successfully left the chat room',
        ]);
    }

    /**
     * Get messages from a chat room.
     */
    public function messages(Request $request, ChatRoom $room)
    {
        // Check if user is a member
        $isMember = ChatRoomMember::where('user_id', Auth::id())
            ->where('chat_room_id', $room->id)
            ->exists();

        if (!$isMember && $room->type === 'private') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Room membership required.',
            ], 403);
        }

        $query = Message::where('chat_room_id', $room->id)
            ->with(['user', 'replyTo.user']);

        // Filter by timestamp if provided (for loading new messages)
        if ($request->has('since')) {
            $query->where('created_at', '>', $request->since);
        }

        $messages = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => array_reverse($messages->items()), // Reverse to show oldest first
            'pagination' => [
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'per_page' => $messages->perPage(),
                'total' => $messages->total(),
            ],
        ]);
    }

    /**
     * Send a message to a chat room.
     */
    public function sendMessage(Request $request, ChatRoom $room)
    {
        // Check if user is a member
        $isMember = ChatRoomMember::where('user_id', Auth::id())
            ->where('chat_room_id', $room->id)
            ->exists();

        if (!$isMember) {
            return response()->json([
                'success' => false,
                'message' => 'Must be a room member to send messages',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:2000',
            'reply_to_id' => 'nullable|exists:messages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $message = Message::create([
            'chat_room_id' => $room->id,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'reply_to_id' => $request->reply_to_id,
            'message_type' => 'text',
        ]);

        // Update room's last activity
        $room->touch();

        // Update message count
        $room->increment('message_count');

        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully',
            'data' => $message->load(['user', 'replyTo.user']),
        ], 201);
    }

    /**
     * Get chat room members.
     */
    public function roomMembers(Request $request, ChatRoom $room)
    {
        // Check if user can view members
        $isMember = ChatRoomMember::where('user_id', Auth::id())
            ->where('chat_room_id', $room->id)
            ->exists();

        if (!$isMember && $room->type === 'private') {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Room membership required.',
            ], 403);
        }

        $members = ChatRoomMember::where('chat_room_id', $room->id)
            ->with(['user'])
            ->orderBy('joined_at', 'desc')
            ->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => $members->items(),
            'pagination' => [
                'current_page' => $members->currentPage(),
                'last_page' => $members->lastPage(),
                'per_page' => $members->perPage(),
                'total' => $members->total(),
            ],
        ]);
    }
}
