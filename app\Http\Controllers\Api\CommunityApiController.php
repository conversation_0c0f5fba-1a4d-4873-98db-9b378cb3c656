<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Community;
use App\Models\CommunityMember;
use App\Models\CommunityPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CommunityApiController extends Controller
{
    /**
     * Display a listing of communities.
     */
    public function index(Request $request)
    {
        $query = Community::with(['creator'])
            ->where('is_active', true);

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['name', 'member_count', 'post_count', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $communities = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $communities->items(),
            'pagination' => [
                'current_page' => $communities->currentPage(),
                'last_page' => $communities->lastPage(),
                'per_page' => $communities->perPage(),
                'total' => $communities->total(),
            ],
        ]);
    }

    /**
     * Display the specified community.
     */
    public function show(Community $community)
    {
        if (!$community->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Community not found or not available',
            ], 404);
        }

        $community->load(['creator']);

        // Check if user is a member (if authenticated)
        $isMember = false;
        if (Auth::check()) {
            $isMember = CommunityMember::where('user_id', Auth::id())
                ->where('community_id', $community->id)
                ->exists();
        }

        return response()->json([
            'success' => true,
            'data' => array_merge($community->toArray(), [
                'is_member' => $isMember,
                'can_join' => Auth::check() && !$isMember,
            ]),
        ]);
    }

    /**
     * Store a newly created community.
     */
    public function store(Request $request)
    {
        if (!Auth::user()->hasRole(['instructor', 'admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Instructor privileges required.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:communities',
            'description' => 'required|string',
            'type' => 'required|in:public,private',
            'course_id' => 'nullable|exists:courses,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $community = Community::create(array_merge($validator->validated(), [
            'created_by' => Auth::id(),
            'slug' => \Str::slug($request->name),
            'is_active' => true,
        ]));

        // Auto-join creator as admin
        CommunityMember::create([
            'community_id' => $community->id,
            'user_id' => Auth::id(),
            'role' => 'admin',
            'joined_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Community created successfully',
            'data' => $community->load(['creator']),
        ], 201);
    }

    /**
     * Join a community.
     */
    public function join(Community $community)
    {
        if (!$community->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Community not available',
            ], 404);
        }

        $user = Auth::user();

        // Check if already a member
        $existingMember = CommunityMember::where('user_id', $user->id)
            ->where('community_id', $community->id)
            ->first();

        if ($existingMember) {
            return response()->json([
                'success' => false,
                'message' => 'Already a member of this community',
            ], 400);
        }

        CommunityMember::create([
            'community_id' => $community->id,
            'user_id' => $user->id,
            'role' => 'member',
            'joined_at' => now(),
        ]);

        // Update member count
        $community->increment('member_count');

        return response()->json([
            'success' => true,
            'message' => 'Successfully joined the community',
        ]);
    }

    /**
     * Leave a community.
     */
    public function leave(Community $community)
    {
        $user = Auth::user();

        $member = CommunityMember::where('user_id', $user->id)
            ->where('community_id', $community->id)
            ->first();

        if (!$member) {
            return response()->json([
                'success' => false,
                'message' => 'Not a member of this community',
            ], 400);
        }

        // Prevent creator from leaving
        if ($community->created_by === $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Community creator cannot leave the community',
            ], 400);
        }

        $member->delete();

        // Update member count
        $community->decrement('member_count');

        return response()->json([
            'success' => true,
            'message' => 'Successfully left the community',
        ]);
    }

    /**
     * Get community posts.
     */
    public function posts(Request $request, Community $community)
    {
        // Check if user can view posts
        if ($community->type === 'private') {
            $isMember = Auth::check() && CommunityMember::where('user_id', Auth::id())
                ->where('community_id', $community->id)
                ->exists();

            if (!$isMember) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. Private community membership required.',
                ], 403);
            }
        }

        $posts = CommunityPost::where('community_id', $community->id)
            ->with(['user', 'replies.user'])
            ->whereNull('parent_id') // Only top-level posts
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $posts->items(),
            'pagination' => [
                'current_page' => $posts->currentPage(),
                'last_page' => $posts->lastPage(),
                'per_page' => $posts->perPage(),
                'total' => $posts->total(),
            ],
        ]);
    }

    /**
     * Create a new post in the community.
     */
    public function createPost(Request $request, Community $community)
    {
        // Check if user is a member
        $isMember = CommunityMember::where('user_id', Auth::id())
            ->where('community_id', $community->id)
            ->exists();

        if (!$isMember) {
            return response()->json([
                'success' => false,
                'message' => 'Must be a community member to post',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'content' => 'required|string|max:5000',
            'parent_id' => 'nullable|exists:community_posts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $post = CommunityPost::create(array_merge($validator->validated(), [
            'community_id' => $community->id,
            'user_id' => Auth::id(),
            'post_type' => $request->parent_id ? 'reply' : 'post',
            'is_approved' => true, // Auto-approve for now
        ]));

        // Update community post count
        if (!$request->parent_id) {
            $community->increment('post_count');
        }

        return response()->json([
            'success' => true,
            'message' => 'Post created successfully',
            'data' => $post->load(['user']),
        ], 201);
    }

    /**
     * Get community members.
     */
    public function members(Request $request, Community $community)
    {
        $members = CommunityMember::where('community_id', $community->id)
            ->with(['user'])
            ->orderBy('joined_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $members->items(),
            'pagination' => [
                'current_page' => $members->currentPage(),
                'last_page' => $members->lastPage(),
                'per_page' => $members->perPage(),
                'total' => $members->total(),
            ],
        ]);
    }
}
