<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crypto_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_id')->constrained()->onDelete('cascade');
            $table->string('currency', 10); // BTC, ETH, USDT, etc.
            $table->decimal('amount', 20, 8); // Crypto amount with high precision
            $table->decimal('usd_amount', 10, 2); // USD equivalent
            $table->decimal('exchange_rate', 15, 2); // Exchange rate at time of payment
            $table->string('payment_address'); // Crypto address to send payment to
            $table->string('transaction_hash')->nullable(); // Blockchain transaction hash
            $table->enum('status', ['pending', 'confirmed', 'expired', 'refunded'])->default('pending');
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->timestamp('expires_at')->nullable(); // Payment expiration time
            $table->timestamps();

            $table->index(['payment_address', 'status']);
            $table->index('transaction_hash');
            $table->index(['currency', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crypto_payments');
    }
};
