<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'You must be logged in to access the admin panel.');
        }

        $user = auth()->user();

        // Check if user has admin access permission
        if (!$user->can('access admin panel')) {
            abort(403, 'You do not have permission to access the admin panel.');
        }

        return $next($request);
    }
}
