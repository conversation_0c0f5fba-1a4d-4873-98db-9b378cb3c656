<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PaymentPolicy
{
    /**
     * Determine whether the user can view any payments.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view payments');
    }

    /**
     * Determine whether the user can view the payment.
     */
    public function view(User $user, Payment $payment): bool
    {
        // Users can view their own payments
        if ($user->id === $payment->user_id) {
            return true;
        }

        // Instructors can view payments for their courses
        if ($payment->course && $payment->course->instructor_id === $user->id) {
            return true;
        }

        // Admins can view all payments
        return $user->can('view payments') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can create payments.
     */
    public function create(User $user): bool
    {
        // All authenticated users can make payments
        return true;
    }

    /**
     * Determine whether the user can update the payment.
     */
    public function update(User $user, Payment $payment): bool
    {
        // Only admins can update payment records
        return $user->can('process payments') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can delete the payment.
     */
    public function delete(User $user, Payment $payment): bool
    {
        // Only super admins can delete payment records
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can restore the payment.
     */
    public function restore(User $user, Payment $payment): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can permanently delete the payment.
     */
    public function forceDelete(User $user, Payment $payment): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can process payments.
     */
    public function process(User $user): bool
    {
        return $user->can('process payments');
    }

    /**
     * Determine whether the user can refund the payment.
     */
    public function refund(User $user, Payment $payment): bool
    {
        // Payment must be refundable
        if (!in_array($payment->status, ['completed', 'paid'])) {
            return false;
        }

        // Check refund window (e.g., 30 days)
        if ($payment->created_at->diffInDays(now()) > 30) {
            return false;
        }

        return $user->can('refund payments');
    }

    /**
     * Determine whether the user can view financial reports.
     */
    public function viewFinancialReports(User $user): bool
    {
        return $user->can('view financial reports');
    }

    /**
     * Determine whether the user can manage payment methods.
     */
    public function managePaymentMethods(User $user): bool
    {
        return $user->can('manage payment methods');
    }

    /**
     * Determine whether the user can handle payment disputes.
     */
    public function handleDisputes(User $user, Payment $payment): bool
    {
        return $user->can('handle disputes');
    }

    /**
     * Determine whether the user can view payment analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return $user->can('view financial analytics');
    }

    /**
     * Determine whether the user can export payment data.
     */
    public function export(User $user): bool
    {
        return $user->can('export reports') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can view instructor earnings.
     */
    public function viewInstructorEarnings(User $user, User $instructor): bool
    {
        // Instructors can view their own earnings
        if ($user->id === $instructor->id) {
            return true;
        }

        // Admins can view all instructor earnings
        return $user->can('view financial reports') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can process payouts to instructors.
     */
    public function processPayout(User $user): bool
    {
        return $user->can('process payments') && $user->hasAnyRole(['admin', 'super-admin']);
    }
}
