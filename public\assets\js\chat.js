// Chat JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
});

function initializeChat() {
    // Setup chat room interactions
    setupChatRooms();
    
    // Setup real-time chat if on chat room page
    if (document.querySelector('.chat-container')) {
        setupRealTimeChat();
    }
    
    // Setup message input
    setupMessageInput();
    
    // Setup join/leave functionality
    setupJoinLeave();
    
    console.log('Chat initialized');
}

function setupChatRooms() {
    const chatRoomCards = document.querySelectorAll('.chat-room-card');
    
    chatRoomCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        // Track room views
        const enterButton = card.querySelector('.btn');
        if (enterButton) {
            enterButton.addEventListener('click', function() {
                const roomName = card.querySelector('.room-name a').textContent;
                trackEvent('chat_room_entered', { room: roomName });
            });
        }
    });
}

function setupRealTimeChat() {
    const chatContainer = document.querySelector('.chat-container');
    if (!chatContainer) return;
    
    const roomId = chatContainer.getAttribute('data-room-id');
    const messagesContainer = document.querySelector('.chat-messages');
    
    // Auto-scroll to bottom
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Setup message polling (in a real app, you'd use WebSockets)
    setInterval(() => {
        loadNewMessages(roomId);
    }, 3000);
    
    // Setup typing indicators
    setupTypingIndicators(roomId);
}

function setupMessageInput() {
    const messageForm = document.querySelector('#message-form');
    const messageInput = document.querySelector('#message-input');
    const sendButton = document.querySelector('#send-button');
    
    if (messageForm && messageInput && sendButton) {
        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            // Enable/disable send button
            sendButton.disabled = this.value.trim() === '';
        });
        
        // Send on Enter (Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (this.value.trim() !== '') {
                    messageForm.dispatchEvent(new Event('submit'));
                }
            }
        });
        
        // Handle form submission
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });
    }
}

function sendMessage() {
    const messageInput = document.querySelector('#message-input');
    const sendButton = document.querySelector('#send-button');
    const roomId = document.querySelector('.chat-container').getAttribute('data-room-id');
    
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Disable input while sending
    messageInput.disabled = true;
    sendButton.disabled = true;
    sendButton.textContent = 'Sending...';
    
    fetch(`/chat/${roomId}/messages`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add message to chat
            addMessageToChat(data.message);
            
            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // Scroll to bottom
            const messagesContainer = document.querySelector('.chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            trackEvent('message_sent', { room_id: roomId });
        } else {
            throw new Error(data.message || 'Failed to send message');
        }
    })
    .catch(error => {
        console.error('Send message error:', error);
        showNotification('Failed to send message. Please try again.', 'error');
    })
    .finally(() => {
        // Re-enable input
        messageInput.disabled = false;
        sendButton.disabled = false;
        sendButton.textContent = 'Send';
        messageInput.focus();
    });
}

function addMessageToChat(message) {
    const messagesContainer = document.querySelector('.chat-messages');
    if (!messagesContainer) return;
    
    const messageElement = createMessageElement(message);
    messagesContainer.appendChild(messageElement);
    
    // Animate in
    setTimeout(() => {
        messageElement.style.opacity = '1';
        messageElement.style.transform = 'translateY(0)';
    }, 10);
}

function createMessageElement(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${message.user_id === getCurrentUserId() ? 'own-message' : ''}`;
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(10px)';
    messageDiv.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
    
    const userInitials = message.user.name.split(' ').map(n => n[0]).join('').toUpperCase();
    
    messageDiv.innerHTML = `
        <div class="message-avatar">${userInitials}</div>
        <div class="message-content">
            <div class="message-header">
                <span class="message-author">${message.user.name}</span>
                <span class="message-time">${formatTimeAgo(message.created_at)}</span>
            </div>
            <div class="message-text">${escapeHtml(message.message)}</div>
        </div>
    `;
    
    return messageDiv;
}

function loadNewMessages(roomId) {
    const messagesContainer = document.querySelector('.chat-messages');
    if (!messagesContainer) return;
    
    const lastMessage = messagesContainer.querySelector('.message:last-child');
    const lastMessageTime = lastMessage ? lastMessage.getAttribute('data-time') : null;
    
    fetch(`/chat/${roomId}/messages?since=${lastMessageTime || ''}`)
    .then(response => response.json())
    .then(data => {
        if (data.messages && data.messages.length > 0) {
            data.messages.forEach(message => {
                addMessageToChat(message);
            });
            
            // Scroll to bottom if user is near bottom
            const isNearBottom = messagesContainer.scrollTop + messagesContainer.clientHeight >= messagesContainer.scrollHeight - 100;
            if (isNearBottom) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }
    })
    .catch(error => {
        console.error('Load messages error:', error);
    });
}

function setupTypingIndicators(roomId) {
    const messageInput = document.querySelector('#message-input');
    if (!messageInput) return;
    
    let typingTimeout;
    let isTyping = false;
    
    messageInput.addEventListener('input', function() {
        if (!isTyping) {
            isTyping = true;
            sendTypingIndicator(roomId, true);
        }
        
        clearTimeout(typingTimeout);
        typingTimeout = setTimeout(() => {
            isTyping = false;
            sendTypingIndicator(roomId, false);
        }, 1000);
    });
}

function sendTypingIndicator(roomId, typing) {
    fetch(`/chat/${roomId}/typing`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ typing: typing })
    })
    .catch(error => {
        console.error('Typing indicator error:', error);
    });
}

function setupJoinLeave() {
    // Join chat room functionality
    const joinButtons = document.querySelectorAll('.join-chat-btn');
    joinButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const roomId = this.getAttribute('data-room-id');
            joinChatRoom(roomId);
        });
    });
    
    // Leave chat room functionality
    const leaveButtons = document.querySelectorAll('.leave-chat-btn');
    leaveButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const roomId = this.getAttribute('data-room-id');
            const roomName = this.getAttribute('data-room-name');
            
            if (confirm(`Are you sure you want to leave "${roomName}"?`)) {
                leaveChatRoom(roomId);
            }
        });
    });
}

function joinChatRoom(roomId) {
    const joinButton = document.querySelector(`[data-room-id="${roomId}"].join-chat-btn`);
    
    if (joinButton) {
        joinButton.textContent = 'Joining...';
        joinButton.disabled = true;
        
        fetch(`/chat/${roomId}/join`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Successfully joined the chat room!', 'success');
                
                // Redirect to chat room
                setTimeout(() => {
                    window.location.href = `/chat/${roomId}`;
                }, 1000);
            } else {
                throw new Error(data.message || 'Failed to join chat room');
            }
        })
        .catch(error => {
            console.error('Join error:', error);
            joinButton.textContent = 'Join & Chat';
            joinButton.disabled = false;
            showNotification('Failed to join chat room. Please try again.', 'error');
        });
    }
}

function leaveChatRoom(roomId) {
    fetch(`/chat/${roomId}/leave`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Successfully left the chat room.', 'info');
            
            // Redirect to chat index
            setTimeout(() => {
                window.location.href = '/chat';
            }, 1000);
        } else {
            throw new Error(data.message || 'Failed to leave chat room');
        }
    })
    .catch(error => {
        console.error('Leave error:', error);
        showNotification('Failed to leave chat room. Please try again.', 'error');
    });
}

// Utility functions
function getCurrentUserId() {
    // This would typically be set from the server
    return window.currentUserId || null;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
    return Math.floor(diffInSeconds / 86400) + 'd ago';
}

function trackEvent(eventName, properties = {}) {
    console.log('Event tracked:', eventName, properties);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
