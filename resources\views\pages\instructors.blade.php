@extends('layouts.minimal')

@section('title', 'Instructors')

@section('content')
<div class="instructors-page">
    <div class="page-header">
        <h1>👨‍🏫 Our Instructors</h1>
        <p class="page-subtitle">Learn from successful entrepreneurs and industry experts</p>
    </div>

    <div class="coming-soon">
        <div class="coming-soon-icon">🎓</div>
        <h2>Instructor Profiles Coming Soon</h2>
        <p>We're building detailed profiles of our world-class instructors who have achieved real success in their fields.</p>
        
        <div class="instructor-preview">
            <h3>What Makes Our Instructors Special:</h3>
            <ul>
                <li>✅ Proven track record of success</li>
                <li>💰 Generated millions in revenue</li>
                <li>🎯 Real-world experience, not just theory</li>
                <li>🤝 Active mentorship and support</li>
                <li>📈 Continuous results and adaptation</li>
            </ul>
        </div>
        
        <div class="become-instructor">
            <h3>Want to Become an Instructor?</h3>
            <p>If you have real-world success and want to share your knowledge, we'd love to hear from you.</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">Apply to Teach</a>
        </div>
    </div>
</div>

<style>
.instructors-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.coming-soon {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
}

.coming-soon-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.instructor-preview {
    margin: 2rem 0;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.instructor-preview h3 {
    text-align: center;
    margin-bottom: 1rem;
}

.instructor-preview ul {
    list-style: none;
    padding: 0;
}

.instructor-preview li {
    padding: 0.5rem 0;
    color: #475569;
}

.become-instructor {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}
</style>
@endsection
