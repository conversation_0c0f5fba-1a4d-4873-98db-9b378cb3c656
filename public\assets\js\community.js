// Community JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeCommunity();
});

function initializeCommunity() {
    // Setup community card interactions
    setupCommunityCards();
    
    // Setup filters
    setupFilters();
    
    // Setup search
    setupSearch();
    
    // Setup join/leave functionality
    setupJoinLeave();
    
    console.log('Community page initialized');
}

function setupCommunityCards() {
    const communityCards = document.querySelectorAll('.community-card');
    
    communityCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        // Track community views
        const viewButton = card.querySelector('.btn');
        if (viewButton) {
            viewButton.addEventListener('click', function() {
                const communityTitle = card.querySelector('.community-title a').textContent;
                trackEvent('community_viewed', { community: communityTitle });
            });
        }
    });
}

function setupFilters() {
    const filterForm = document.querySelector('.filters-form');
    const filterSelects = document.querySelectorAll('.filter-select');
    
    if (filterForm) {
        // Auto-submit on filter change
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
        
        // Clear filters functionality
        const clearButton = filterForm.querySelector('.btn-secondary');
        if (clearButton) {
            clearButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Clear all form fields
                const searchInput = filterForm.querySelector('.search-input');
                if (searchInput) searchInput.value = '';
                
                filterSelects.forEach(select => {
                    select.selectedIndex = 0;
                });
                
                // Submit form
                filterForm.submit();
            });
        }
    }
}

function setupSearch() {
    const searchInput = document.querySelector('.search-input');
    
    if (searchInput) {
        // Add search icon
        const searchIcon = document.createElement('span');
        searchIcon.innerHTML = '🔍';
        searchIcon.className = 'search-icon';
        searchInput.parentElement.style.position = 'relative';
        searchInput.parentElement.appendChild(searchIcon);
        
        // Debounced search
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
        
        // Search on enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
        });
    }
}

function setupJoinLeave() {
    // Join community functionality
    const joinButtons = document.querySelectorAll('.join-community-btn');
    joinButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const communityId = this.getAttribute('data-community-id');
            joinCommunity(communityId);
        });
    });
    
    // Leave community functionality
    const leaveButtons = document.querySelectorAll('.leave-community-btn');
    leaveButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const communityId = this.getAttribute('data-community-id');
            const communityName = this.getAttribute('data-community-name');
            
            if (confirm(`Are you sure you want to leave "${communityName}"?`)) {
                leaveCommunity(communityId);
            }
        });
    });
}

function joinCommunity(communityId) {
    const joinButton = document.querySelector(`[data-community-id="${communityId}"].join-community-btn`);
    
    if (joinButton) {
        joinButton.textContent = 'Joining...';
        joinButton.disabled = true;
        
        fetch(`/communities/${communityId}/join`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                joinButton.textContent = '✅ Joined';
                joinButton.classList.remove('btn-primary');
                joinButton.classList.add('btn-success');
                
                // Update member count
                const memberCount = document.querySelector(`[data-community-id="${communityId}"] .member-count`);
                if (memberCount) {
                    const currentCount = parseInt(memberCount.textContent);
                    memberCount.textContent = (currentCount + 1) + ' members';
                }
                
                showNotification('Successfully joined the community!', 'success');
                
                // Redirect to community page after a delay
                setTimeout(() => {
                    window.location.href = `/communities/${communityId}`;
                }, 1500);
            } else {
                throw new Error(data.message || 'Failed to join community');
            }
        })
        .catch(error => {
            console.error('Join error:', error);
            joinButton.textContent = 'Join Community';
            joinButton.disabled = false;
            showNotification('Failed to join community. Please try again.', 'error');
        });
    }
}

function leaveCommunity(communityId) {
    const leaveButton = document.querySelector(`[data-community-id="${communityId}"].leave-community-btn`);
    
    if (leaveButton) {
        leaveButton.textContent = 'Leaving...';
        leaveButton.disabled = true;
        
        fetch(`/communities/${communityId}/leave`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Successfully left the community.', 'info');
                
                // Redirect to communities index
                setTimeout(() => {
                    window.location.href = '/communities';
                }, 1000);
            } else {
                throw new Error(data.message || 'Failed to leave community');
            }
        })
        .catch(error => {
            console.error('Leave error:', error);
            leaveButton.textContent = 'Leave Community';
            leaveButton.disabled = false;
            showNotification('Failed to leave community. Please try again.', 'error');
        });
    }
}

// Post creation functionality
function createPost(communityId, content) {
    fetch(`/communities/${communityId}/posts`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ content: content })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new post to the feed
            addPostToFeed(data.post);
            showNotification('Post created successfully!', 'success');
            
            // Clear the form
            const postForm = document.querySelector('#post-form');
            if (postForm) {
                postForm.reset();
            }
        } else {
            throw new Error(data.message || 'Failed to create post');
        }
    })
    .catch(error => {
        console.error('Post creation error:', error);
        showNotification('Failed to create post. Please try again.', 'error');
    });
}

function addPostToFeed(post) {
    const postsContainer = document.querySelector('.posts-container');
    if (postsContainer) {
        const postElement = createPostElement(post);
        postsContainer.insertBefore(postElement, postsContainer.firstChild);
    }
}

function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post-item';
    postDiv.innerHTML = `
        <div class="post-header">
            <div class="post-author">
                <span class="author-name">${post.user.name}</span>
                <span class="post-time">${formatTimeAgo(post.created_at)}</span>
            </div>
        </div>
        <div class="post-content">
            <p>${post.content}</p>
        </div>
        <div class="post-actions">
            <button class="post-action like-btn" data-post-id="${post.id}">
                👍 <span class="like-count">0</span>
            </button>
            <button class="post-action reply-btn" data-post-id="${post.id}">
                💬 Reply
            </button>
        </div>
    `;
    return postDiv;
}

// Utility functions
function trackEvent(eventName, properties = {}) {
    console.log('Event tracked:', eventName, properties);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
    return Math.floor(diffInSeconds / 86400) + 'd ago';
}

// Add community-specific styles
const communityStyles = document.createElement('style');
communityStyles.textContent = `
    .search-icon {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #666666;
        pointer-events: none;
    }
    
    .search-input {
        padding-right: 3rem !important;
    }
    
    .post-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: transform 0.3s ease;
    }
    
    .post-item:hover {
        transform: translateY(-2px);
    }
    
    .post-header {
        margin-bottom: 1rem;
    }
    
    .author-name {
        color: #ffffff;
        font-weight: 600;
    }
    
    .post-time {
        color: #a0a0a0;
        font-size: 0.875rem;
        margin-left: 0.5rem;
    }
    
    .post-content {
        color: #a0a0a0;
        line-height: 1.6;
        margin-bottom: 1rem;
    }
    
    .post-actions {
        display: flex;
        gap: 1rem;
    }
    
    .post-action {
        background: none;
        border: none;
        color: #a0a0a0;
        cursor: pointer;
        font-size: 0.875rem;
        transition: color 0.3s ease;
    }
    
    .post-action:hover {
        color: #ffffff;
    }
`;
document.head.appendChild(communityStyles);
