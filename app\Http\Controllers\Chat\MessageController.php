<?php

namespace App\Http\Controllers\Chat;

use App\Http\Controllers\Controller;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MessageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Store a new message.
     */
    public function store(Request $request, ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is a member of the chat room
        if (!$chatRoom->members()->where('user_id', $user->id)->exists()) {
            return response()->json(['error' => 'You are not a member of this chat room.'], 403);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
            'reply_to_id' => 'nullable|exists:chat_messages,id',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);

        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('chat-attachments', 'public');
                $attachments[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
        }

        $message = ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'user_id' => $user->id,
            'reply_to_id' => $request->reply_to_id,
            'message' => $request->message,
            'message_type' => !empty($attachments) ? 'file' : 'text',
            'attachments' => !empty($attachments) ? $attachments : null,
        ]);

        // Update chat room last message time
        $chatRoom->update([
            'last_message_at' => now(),
        ]);
        $chatRoom->increment('message_count');

        // Load relationships for response
        $message->load(['user', 'replyTo.user']);

        // Broadcast message to other users (WebSocket implementation would go here)
        // broadcast(new MessageSent($message))->toOthers();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'html' => view('chat.partials.message', compact('message'))->render()
            ]);
        }

        return redirect()->route('chat.show', $chatRoom);
    }

    /**
     * Get messages for a chat room (AJAX).
     */
    public function index(Request $request, ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is a member
        if (!$chatRoom->members()->where('user_id', $user->id)->exists()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $page = $request->get('page', 1);
        $perPage = 20;

        $messages = $chatRoom->messages()
            ->with(['user', 'replyTo.user'])
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->reverse()
            ->values();

        return response()->json([
            'messages' => $messages,
            'has_more' => $chatRoom->messages()->count() > ($page * $perPage)
        ]);
    }

    /**
     * Edit a message.
     */
    public function update(Request $request, ChatMessage $message)
    {
        $user = Auth::user();

        // Check if user owns the message
        if ($message->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if message can be edited (within 15 minutes)
        if ($message->created_at->diffInMinutes(now()) > 15) {
            return response()->json(['error' => 'Message can only be edited within 15 minutes'], 422);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $message->update([
            'message' => $request->message,
            'is_edited' => true,
            'edited_at' => now(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message,
            ]);
        }

        return redirect()->route('chat.show', $message->chatRoom);
    }

    /**
     * Delete a message.
     */
    public function destroy(ChatMessage $message)
    {
        $user = Auth::user();

        // Check if user owns the message or is a moderator
        $isModerator = $message->chatRoom->members()
            ->where('user_id', $user->id)
            ->whereIn('role', ['admin', 'moderator'])
            ->exists();

        if ($message->user_id !== $user->id && !$isModerator) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $message->update([
            'is_deleted' => true,
            'deleted_at' => now(),
        ]);

        // Delete attachments if any
        if ($message->attachments) {
            foreach ($message->attachments as $attachment) {
                Storage::disk('public')->delete($attachment['path']);
            }
        }

        $message->chatRoom->decrement('message_count');

        if (request()->expectsJson()) {
            return response()->json(['success' => true]);
        }

        return redirect()->route('chat.show', $message->chatRoom);
    }

    /**
     * React to a message (like, dislike, etc.).
     */
    public function react(Request $request, ChatMessage $message)
    {
        $user = Auth::user();

        $request->validate([
            'reaction' => 'required|in:like,dislike,love,laugh,angry,sad',
        ]);

        // Check if user already reacted
        $existingReaction = $message->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->reaction === $request->reaction) {
                // Remove reaction if same
                $existingReaction->delete();
                $action = 'removed';
            } else {
                // Update reaction
                $existingReaction->update(['reaction' => $request->reaction]);
                $action = 'updated';
            }
        } else {
            // Add new reaction
            $message->reactions()->create([
                'user_id' => $user->id,
                'reaction' => $request->reaction,
            ]);
            $action = 'added';
        }

        // Get updated reaction counts
        $reactionCounts = $message->reactions()
            ->selectRaw('reaction, count(*) as count')
            ->groupBy('reaction')
            ->pluck('count', 'reaction');

        return response()->json([
            'success' => true,
            'action' => $action,
            'reaction_counts' => $reactionCounts,
        ]);
    }

    /**
     * Search messages in a chat room.
     */
    public function search(Request $request, ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is a member
        if (!$chatRoom->members()->where('user_id', $user->id)->exists()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'query' => 'required|string|min:3',
        ]);

        $messages = $chatRoom->messages()
            ->with(['user'])
            ->where('message', 'like', '%' . $request->query . '%')
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();

        return response()->json([
            'messages' => $messages,
        ]);
    }
}
