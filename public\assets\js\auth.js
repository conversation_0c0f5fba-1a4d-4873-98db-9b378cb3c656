// Authentication JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeAuthForms();
});

function initializeAuthForms() {
    const loginForm = document.querySelector('form[action*="login"]');
    const registerForm = document.querySelector('form[action*="register"]');
    
    if (loginForm) {
        setupLoginForm(loginForm);
    }
    
    if (registerForm) {
        setupRegisterForm(registerForm);
    }
    
    // Password visibility toggle
    setupPasswordToggle();
}

function setupLoginForm(form) {
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const email = form.querySelector('input[name="email"]').value;
        const password = form.querySelector('input[name="password"]').value;
        
        // Basic validation
        if (!email || !password) {
            e.preventDefault();
            showError('Please fill in all fields');
            return;
        }
        
        if (!validateEmail(email)) {
            e.preventDefault();
            showError('Please enter a valid email address');
            return;
        }
        
        // Show loading state
        showLoading(submitBtn);
    });
}

function setupRegisterForm(form) {
    const passwordInput = form.querySelector('input[name="password"]');
    const confirmPasswordInput = form.querySelector('input[name="password_confirmation"]');
    
    // Real-time password validation
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            validatePasswordStrength(this.value);
        });
    }
    
    // Confirm password validation
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            validatePasswordMatch(passwordInput.value, this.value);
        });
    }
    
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const firstName = form.querySelector('input[name="first_name"]').value;
        const lastName = form.querySelector('input[name="last_name"]').value;
        const email = form.querySelector('input[name="email"]').value;
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        // Validation
        if (!firstName || !lastName || !email || !password || !confirmPassword) {
            e.preventDefault();
            showError('Please fill in all required fields');
            return;
        }
        
        if (!validateEmail(email)) {
            e.preventDefault();
            showError('Please enter a valid email address');
            return;
        }
        
        if (password.length < 8) {
            e.preventDefault();
            showError('Password must be at least 8 characters long');
            return;
        }
        
        if (password !== confirmPassword) {
            e.preventDefault();
            showError('Passwords do not match');
            return;
        }
        
        // Show loading state
        showLoading(submitBtn);
    });
}

function setupPasswordToggle() {
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    
    passwordInputs.forEach(input => {
        const wrapper = input.parentElement;
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'password-toggle';
        toggleBtn.innerHTML = '👁️';
        toggleBtn.setAttribute('aria-label', 'Toggle password visibility');
        
        wrapper.style.position = 'relative';
        wrapper.appendChild(toggleBtn);
        
        toggleBtn.addEventListener('click', function() {
            if (input.type === 'password') {
                input.type = 'text';
                toggleBtn.innerHTML = '🙈';
            } else {
                input.type = 'password';
                toggleBtn.innerHTML = '👁️';
            }
        });
    });
}

function validatePasswordStrength(password) {
    const strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;
    
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 8) strength++;
    else feedback.push('At least 8 characters');
    
    if (/[a-z]/.test(password)) strength++;
    else feedback.push('Lowercase letter');
    
    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('Uppercase letter');
    
    if (/[0-9]/.test(password)) strength++;
    else feedback.push('Number');
    
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('Special character');
    
    const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];
    
    strengthIndicator.textContent = strengthLevels[strength] || 'Very Weak';
    strengthIndicator.style.color = strengthColors[strength] || '#ef4444';
    
    if (feedback.length > 0) {
        strengthIndicator.title = 'Missing: ' + feedback.join(', ');
    }
}

function validatePasswordMatch(password, confirmPassword) {
    const matchIndicator = document.getElementById('password-match');
    if (!matchIndicator) return;
    
    if (confirmPassword === '') {
        matchIndicator.textContent = '';
        return;
    }
    
    if (password === confirmPassword) {
        matchIndicator.textContent = '✅ Passwords match';
        matchIndicator.style.color = '#10b981';
    } else {
        matchIndicator.textContent = '❌ Passwords do not match';
        matchIndicator.style.color = '#ef4444';
    }
}

function showError(message) {
    // Remove existing error messages
    const existingErrors = document.querySelectorAll('.auth-error');
    existingErrors.forEach(error => error.remove());
    
    // Create new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'auth-error';
    errorDiv.style.cssText = `
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid #ef4444;
        color: #f87171;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    `;
    errorDiv.textContent = message;
    
    // Insert at the top of the form
    const form = document.querySelector('.auth-form');
    if (form) {
        form.insertBefore(errorDiv, form.firstChild);
    }
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function showLoading(button) {
    if (button) {
        button.classList.add('loading');
        button.disabled = true;
        button.textContent = 'Please wait...';
    }
}

// Country selector enhancement
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country');
    if (countrySelect) {
        // Add more countries
        const countries = [
            { code: 'US', name: 'United States' },
            { code: 'UK', name: 'United Kingdom' },
            { code: 'CA', name: 'Canada' },
            { code: 'AU', name: 'Australia' },
            { code: 'DE', name: 'Germany' },
            { code: 'FR', name: 'France' },
            { code: 'IT', name: 'Italy' },
            { code: 'ES', name: 'Spain' },
            { code: 'NL', name: 'Netherlands' },
            { code: 'SE', name: 'Sweden' },
            { code: 'NO', name: 'Norway' },
            { code: 'DK', name: 'Denmark' },
            { code: 'FI', name: 'Finland' },
            { code: 'CH', name: 'Switzerland' },
            { code: 'AT', name: 'Austria' },
            { code: 'BE', name: 'Belgium' },
            { code: 'IE', name: 'Ireland' },
            { code: 'PT', name: 'Portugal' },
            { code: 'GR', name: 'Greece' },
            { code: 'PL', name: 'Poland' },
            { code: 'CZ', name: 'Czech Republic' },
            { code: 'HU', name: 'Hungary' },
            { code: 'RO', name: 'Romania' },
            { code: 'BG', name: 'Bulgaria' },
            { code: 'HR', name: 'Croatia' },
            { code: 'SI', name: 'Slovenia' },
            { code: 'SK', name: 'Slovakia' },
            { code: 'LT', name: 'Lithuania' },
            { code: 'LV', name: 'Latvia' },
            { code: 'EE', name: 'Estonia' }
        ];
        
        // Clear existing options except the first one
        while (countrySelect.children.length > 1) {
            countrySelect.removeChild(countrySelect.lastChild);
        }
        
        // Add countries
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country.code;
            option.textContent = country.name;
            countrySelect.appendChild(option);
        });
    }
});
