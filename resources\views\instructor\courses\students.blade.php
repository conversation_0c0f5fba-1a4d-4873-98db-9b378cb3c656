@extends('layouts.app')

@section('title', 'Course Students - ' . $course->title)

@section('content')
<div class="course-students">
    <!-- Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>👥 Course Students</h1>
                <p>{{ $course->title }}</p>
                <div class="course-meta">
                    <span class="meta-item">📚 {{ $course->category->name }}</span>
                    <span class="meta-item">👥 {{ $enrollments->total() }} students</span>
                    <span class="meta-item">💰 ${{ number_format($course->price, 2) }}</span>
                </div>
            </div>
            <div class="header-actions">
                <a href="{{ route('instructor.courses.index') }}" class="btn btn-outline">
                    ← Back to Courses
                </a>
                <a href="{{ route('courses.show', $course) }}" class="btn btn-secondary">
                    👁️ View Course
                </a>
            </div>
        </div>
    </div>

    <!-- Student Stats -->
    <div class="student-stats">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>Total Students</h3>
                    <p class="stat-value">{{ $enrollments->total() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3>Completed</h3>
                    <p class="stat-value">{{ $enrollments->where('completed_at', '!=', null)->count() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <h3>In Progress</h3>
                    <p class="stat-value">{{ $enrollments->where('completed_at', null)->where('progress', '>', 0)->count() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <h3>Revenue</h3>
                    <p class="stat-value">${{ number_format($enrollments->count() * $course->price, 2) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Students List -->
    <div class="students-list">
        <div class="list-header">
            <h2>Enrolled Students</h2>
            <div class="list-controls">
                <div class="search-box">
                    <input type="text" placeholder="Search students..." id="studentSearch">
                    <span class="search-icon">🔍</span>
                </div>
                <select class="filter-select" id="progressFilter">
                    <option value="">All Students</option>
                    <option value="not-started">Not Started</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                </select>
                <button class="btn btn-outline" onclick="exportStudents()">
                    📊 Export
                </button>
            </div>
        </div>

        @if($enrollments->count() > 0)
            <div class="students-table">
                <div class="table-header">
                    <div class="header-cell">Student</div>
                    <div class="header-cell">Enrolled</div>
                    <div class="header-cell">Progress</div>
                    <div class="header-cell">Last Activity</div>
                    <div class="header-cell">Status</div>
                    <div class="header-cell">Actions</div>
                </div>

                @foreach($enrollments as $enrollment)
                    <div class="table-row" data-progress="{{ $enrollment->getProgressStatus() }}">
                        <div class="table-cell student-info">
                            <div class="student-avatar">
                                @if($enrollment->user->avatar)
                                    <img src="{{ Storage::url($enrollment->user->avatar) }}" alt="{{ $enrollment->user->name }}">
                                @else
                                    <div class="avatar-placeholder">
                                        {{ strtoupper(substr($enrollment->user->name, 0, 2)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="student-details">
                                <h4>{{ $enrollment->user->name }}</h4>
                                <p>{{ $enrollment->user->email }}</p>
                                @if($enrollment->user->location)
                                    <small>📍 {{ $enrollment->user->location }}</small>
                                @endif
                            </div>
                        </div>

                        <div class="table-cell">
                            <div class="enrollment-date">
                                {{ $enrollment->created_at->format('M j, Y') }}
                            </div>
                            <small class="enrollment-time">
                                {{ $enrollment->created_at->diffForHumans() }}
                            </small>
                        </div>

                        <div class="table-cell">
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ $enrollment->progress }}%"></div>
                                </div>
                                <span class="progress-text">{{ $enrollment->progress }}%</span>
                            </div>
                            @if($enrollment->completed_at)
                                <small class="completion-date">
                                    ✅ Completed {{ $enrollment->completed_at->format('M j, Y') }}
                                </small>
                            @endif
                        </div>

                        <div class="table-cell">
                            @if($enrollment->last_accessed_at)
                                <div class="last-activity">
                                    {{ $enrollment->last_accessed_at->diffForHumans() }}
                                </div>
                            @else
                                <div class="last-activity never">
                                    Never accessed
                                </div>
                            @endif
                        </div>

                        <div class="table-cell">
                            <span class="status-badge {{ $enrollment->getProgressStatus() }}">
                                @switch($enrollment->getProgressStatus())
                                    @case('not-started')
                                        📋 Not Started
                                        @break
                                    @case('in-progress')
                                        📈 In Progress
                                        @break
                                    @case('completed')
                                        ✅ Completed
                                        @break
                                    @default
                                        📋 Unknown
                                @endswitch
                            </span>
                        </div>

                        <div class="table-cell">
                            <div class="action-buttons">
                                <button class="action-btn view" onclick="viewStudentProgress({{ $enrollment->user->id }})" title="View Progress">
                                    👁️
                                </button>
                                <button class="action-btn message" onclick="messageStudent({{ $enrollment->user->id }})" title="Send Message">
                                    💬
                                </button>
                                <button class="action-btn certificate" onclick="generateCertificate({{ $enrollment->id }})" title="Generate Certificate" {{ $enrollment->completed_at ? '' : 'disabled' }}>
                                    🏆
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                {{ $enrollments->links() }}
            </div>
        @else
            <div class="empty-state">
                <div class="empty-icon">👥</div>
                <h3>No Students Enrolled</h3>
                <p>When students enroll in your course, they will appear here.</p>
                <a href="{{ route('courses.show', $course) }}" class="btn btn-primary">
                    👁️ View Course Page
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Student Progress Modal -->
<div class="modal" id="progressModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📊 Student Progress</h3>
            <button type="button" class="close-btn" onclick="closeProgressModal()">✕</button>
        </div>
        <div class="modal-body" id="progressModalBody">
            <!-- Progress details will be loaded here -->
        </div>
    </div>
</div>

<style>
.course-students {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.course-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #ffffff;
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.student-stats {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-content h3 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: #3b82f6;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.students-list {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.list-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.list-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.students-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-weight: 600;
    color: #ffffff;
    font-size: 0.875rem;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    align-items: center;
    transition: all 0.3s ease;
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.student-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    font-weight: 600;
}

.student-details h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.student-details p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.student-details small {
    color: #a0a0a0;
    font-size: 0.625rem;
}

.enrollment-date {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.enrollment-time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.progress-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.progress-text {
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 600;
}

.completion-date {
    color: #22c55e;
    font-size: 0.625rem;
}

.last-activity {
    color: #ffffff;
    font-size: 0.875rem;
}

.last-activity.never {
    color: #a0a0a0;
    font-style: italic;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.not-started {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}

.status-badge.in-progress {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-badge.completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.action-btn:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.modal-body {
    padding: 2rem;
}

@media (max-width: 768px) {
    .course-students {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-controls {
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .table-header {
        display: none;
    }
    
    .table-row {
        display: flex;
        flex-direction: column;
        align-items: stretch;
    }
}
</style>

<script>
// Search functionality
document.getElementById('studentSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const studentRows = document.querySelectorAll('.table-row');
    
    studentRows.forEach(row => {
        const studentName = row.querySelector('.student-details h4').textContent.toLowerCase();
        const studentEmail = row.querySelector('.student-details p').textContent.toLowerCase();
        
        if (studentName.includes(searchTerm) || studentEmail.includes(searchTerm)) {
            row.style.display = 'grid';
        } else {
            row.style.display = 'none';
        }
    });
});

// Filter functionality
document.getElementById('progressFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const studentRows = document.querySelectorAll('.table-row');
    
    studentRows.forEach(row => {
        const progress = row.dataset.progress;
        
        if (filterValue === '' || progress === filterValue) {
            row.style.display = 'grid';
        } else {
            row.style.display = 'none';
        }
    });
});

function viewStudentProgress(userId) {
    // Load student progress details
    document.getElementById('progressModal').style.display = 'flex';
    document.getElementById('progressModalBody').innerHTML = '<p>Loading student progress...</p>';
    
    // Here you would fetch and display detailed progress
    console.log('View progress for user:', userId);
}

function messageStudent(userId) {
    // Open messaging interface
    console.log('Message student:', userId);
}

function generateCertificate(enrollmentId) {
    // Generate completion certificate
    console.log('Generate certificate for enrollment:', enrollmentId);
}

function exportStudents() {
    // Export student list
    console.log('Export students');
}

function closeProgressModal() {
    document.getElementById('progressModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('progressModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeProgressModal();
    }
});
</script>
@endsection
