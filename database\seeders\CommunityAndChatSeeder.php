<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Community;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Course;
use Illuminate\Support\Str;

class CommunityAndChatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing users and courses
        $instructor = User::where('email', '<EMAIL>')->first();
        $courses = Course::all();

        // Create sample communities
        $communities = [
            [
                'name' => 'The War Room',
                'description' => 'Elite community for high-achievers and entrepreneurs. Share strategies, network, and dominate your field.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Crypto Millionaires',
                'description' => 'Exclusive community for cryptocurrency traders and investors. Share signals, strategies, and market insights.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Fitness Warriors',
                'description' => 'Transform your body and mind. Share workouts, nutrition tips, and motivate each other to greatness.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Business Fundamentals Discussion',
                'description' => 'Course-specific community for students of the Business Fundamentals course.',
                'type' => 'course-specific',
                'course_id' => $courses->where('title', 'Escape The Matrix: Business Fundamentals')->first()?->id,
            ],
        ];

        foreach ($communities as $communityData) {
            $slug = Str::slug($communityData['name']);

            // Check if community already exists
            $community = Community::where('slug', $slug)->first();

            if (!$community) {
                $community = Community::create([
                    'name' => $communityData['name'],
                    'slug' => $slug,
                    'description' => $communityData['description'],
                    'type' => $communityData['type'],
                    'course_id' => $communityData['course_id'],
                    'created_by' => $instructor->id,
                    'is_active' => true,
                    'member_count' => rand(50, 500),
                    'post_count' => rand(100, 1000),
                ]);

                // Add creator as admin member
                $community->members()->attach($instructor->id, [
                    'role' => 'admin',
                    'joined_at' => now()
                ]);
            }
        }

        // Create sample chat rooms
        $chatRooms = [
            [
                'name' => 'General Chat',
                'description' => 'Main chat room for general discussions and networking.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Daily Wins',
                'description' => 'Share your daily victories and celebrate success with the community.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Crypto Signals',
                'description' => 'Real-time cryptocurrency trading signals and market analysis.',
                'type' => 'public',
                'course_id' => null,
            ],
            [
                'name' => 'Business Course Chat',
                'description' => 'Chat room for Business Fundamentals course students.',
                'type' => 'course',
                'course_id' => $courses->where('title', 'Escape The Matrix: Business Fundamentals')->first()?->id,
            ],
            [
                'name' => 'Crypto Course Chat',
                'description' => 'Chat room for Cryptocurrency Mastery course students.',
                'type' => 'course',
                'course_id' => $courses->where('title', 'Cryptocurrency Mastery: From Beginner to Pro')->first()?->id,
            ],
        ];

        foreach ($chatRooms as $roomData) {
            $slug = Str::slug($roomData['name']);

            // Check if chat room already exists
            $chatRoom = ChatRoom::where('slug', $slug)->first();

            if (!$chatRoom) {
                $chatRoom = ChatRoom::create([
                    'name' => $roomData['name'],
                    'slug' => $slug,
                    'description' => $roomData['description'],
                    'type' => $roomData['type'],
                    'course_id' => $roomData['course_id'],
                    'created_by' => $instructor->id,
                    'is_active' => true,
                    'member_count' => rand(20, 200),
                    'message_count' => rand(100, 2000),
                    'last_message_at' => now()->subMinutes(rand(1, 60)),
                ]);

                // Add creator as admin member
                $chatRoom->members()->attach($instructor->id, [
                    'role' => 'admin',
                    'joined_at' => now()
                ]);

            // Create sample messages
            $sampleMessages = [
                'Welcome to ' . $roomData['name'] . '! Let\'s build wealth together 💪',
                'Who\'s ready to escape the matrix today?',
                'Just closed another deal! The strategies from this community are incredible 🔥',
                'Remember: Success is not given, it\'s earned. Keep grinding!',
                'The difference between successful people and everyone else is action.',
                'Your network is your net worth. Connect, learn, grow.',
                'Every expert was once a beginner. Keep learning, keep growing.',
                'The best investment you can make is in yourself.',
            ];

                foreach (array_slice($sampleMessages, 0, rand(3, 6)) as $index => $messageText) {
                    ChatMessage::create([
                        'chat_room_id' => $chatRoom->id,
                        'user_id' => $instructor->id,
                        'message' => $messageText,
                        'message_type' => 'text',
                        'created_at' => now()->subMinutes(rand(1, 120)),
                    ]);
                }
            }
        }
    }
}
