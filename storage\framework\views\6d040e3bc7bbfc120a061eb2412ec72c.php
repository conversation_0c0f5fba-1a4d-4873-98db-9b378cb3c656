<?php $__env->startSection('title', 'Admin Dashboard'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-info">
                <h1>Dashboard Overview</h1>
                <p>Welcome back, <?php echo e(Auth::user()->name); ?>! Here's what's happening with your platform.</p>
            </div>
            <div class="header-actions">
                <div class="system-status" id="system-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">Checking...</span>
                </div>
            </div>
        </div>
    </div>

        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon">👥</div>
                <div class="metric-content">
                    <h3><?php echo e(number_format($metrics['total_users']['value'])); ?></h3>
                    <p>Total Users</p>
                    <div class="metric-change <?php echo e($metrics['total_users']['trend']); ?>">
                        <?php echo e($metrics['total_users']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['total_users']['change']); ?>%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">📚</div>
                <div class="metric-content">
                    <h3><?php echo e(number_format($metrics['total_courses']['value'])); ?></h3>
                    <p>Active Courses</p>
                    <div class="metric-change <?php echo e($metrics['total_courses']['trend']); ?>">
                        <?php echo e($metrics['total_courses']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['total_courses']['change']); ?>%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-content">
                    <h3>$<?php echo e(number_format($metrics['total_revenue']['value'], 2)); ?></h3>
                    <p>Total Revenue</p>
                    <div class="metric-change <?php echo e($metrics['total_revenue']['trend']); ?>">
                        <?php echo e($metrics['total_revenue']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['total_revenue']['change']); ?>%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">🎯</div>
                <div class="metric-content">
                    <h3><?php echo e(number_format($metrics['active_enrollments']['value'])); ?></h3>
                    <p>Active Enrollments</p>
                    <div class="metric-change <?php echo e($metrics['active_enrollments']['trend']); ?>">
                        <?php echo e($metrics['active_enrollments']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['active_enrollments']['change']); ?>%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">📅</div>
                <div class="metric-content">
                    <h3>$<?php echo e(number_format($metrics['monthly_revenue']['value'], 2)); ?></h3>
                    <p>Monthly Revenue</p>
                    <div class="metric-change <?php echo e($metrics['monthly_revenue']['trend']); ?>">
                        <?php echo e($metrics['monthly_revenue']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['monthly_revenue']['change']); ?>%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">✅</div>
                <div class="metric-content">
                    <h3><?php echo e($metrics['completion_rate']['value']); ?>%</h3>
                    <p>Completion Rate</p>
                    <div class="metric-change <?php echo e($metrics['completion_rate']['trend']); ?>">
                        <?php echo e($metrics['completion_rate']['change'] > 0 ? '+' : ''); ?><?php echo e($metrics['completion_rate']['change']); ?>%
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Revenue Trend (Last 30 Days)</h3>
                </div>
                <canvas id="revenueChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>User Growth (Last 12 Months)</h3>
                </div>
                <canvas id="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Activities -->
            <div class="content-card">
                <div class="card-header">
                    <h3>Recent Activities</h3>
                    <a href="#" class="view-all">View All</a>
                </div>
                <div class="activities-list">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="activity-item">
                            <div class="activity-icon <?php echo e($activity['color']); ?>">
                                <?php echo e($activity['icon']); ?>

                            </div>
                            <div class="activity-content">
                                <h4><?php echo e($activity['title']); ?></h4>
                                <p><?php echo e($activity['description']); ?></p>
                                <span class="activity-time"><?php echo e($activity['time'] ? $activity['time']->diffForHumans() : 'recently'); ?></span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="no-activities">No recent activities</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Top Courses -->
            <div class="content-card">
                <div class="card-header">
                    <h3>Top Performing Courses</h3>
                    <a href="<?php echo e(route('admin.courses.index')); ?>" class="view-all">View All</a>
                </div>
                <div class="courses-list">
                    <?php $__empty_1 = true; $__currentLoopData = $topCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="course-item">
                            <div class="course-info">
                                <h4><?php echo e($course['title']); ?></h4>
                                <p>by <?php echo e($course['instructor']); ?></p>
                            </div>
                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-value"><?php echo e($course['students']); ?></span>
                                    <span class="stat-label">Students</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value"><?php echo e($course['rating']); ?></span>
                                    <span class="stat-label">Rating</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value">$<?php echo e(number_format($course['revenue'])); ?></span>
                                    <span class="stat-label">Revenue</span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="no-courses">No courses available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="actions-grid">
                <a href="<?php echo e(route('admin.users.create')); ?>" class="action-btn">
                    <span class="action-icon">👤</span>
                    <span class="action-text">Add User</span>
                </a>
                <a href="<?php echo e(route('admin.courses.create')); ?>" class="action-btn">
                    <span class="action-icon">📚</span>
                    <span class="action-text">Create Course</span>
                </a>
                <a href="<?php echo e(route('admin.payments.index')); ?>" class="action-btn">
                    <span class="action-icon">💰</span>
                    <span class="action-text">View Payments</span>
                </a>
                <a href="<?php echo e(route('admin.analytics')); ?>" class="action-btn">
                    <span class="action-icon">📊</span>
                    <span class="action-text">Analytics</span>
                </a>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            checkSystemStatus();
        });

        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($revenueData->pluck('date')); ?>,
                    datasets: [{
                        label: 'Revenue ($)',
                        data: <?php echo json_encode($revenueData->pluck('revenue')); ?>,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value;
                                }
                            }
                        }
                    }
                }
            });

            // User Growth Chart
            const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
            new Chart(userGrowthCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($userGrowthData->pluck('month')); ?>,
                    datasets: [{
                        label: 'New Users',
                        data: <?php echo json_encode($userGrowthData->pluck('users')); ?>,
                        backgroundColor: '#10b981',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function checkSystemStatus() {
            fetch('<?php echo e(route("admin.system-status")); ?>')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('system-status');
                    const indicator = statusElement.querySelector('.status-indicator');
                    const text = statusElement.querySelector('.status-text');
                    
                    const allHealthy = Object.values(data).every(status => status.status === 'healthy');
                    
                    if (allHealthy) {
                        indicator.className = 'status-indicator healthy';
                        text.textContent = 'All Systems Operational';
                    } else {
                        indicator.className = 'status-indicator warning';
                        text.textContent = 'System Issues Detected';
                    }
                })
                .catch(error => {
                    console.error('System status check failed:', error);
                    const statusElement = document.getElementById('system-status');
                    const indicator = statusElement.querySelector('.status-indicator');
                    const text = statusElement.querySelector('.status-text');
                    
                    indicator.className = 'status-indicator error';
                    text.textContent = 'Status Check Failed';
                });
        }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>