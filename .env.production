# The Real World LMS - Production Environment Configuration
# Copy this file to .env and update the values for your production environment

# Application
APP_NAME="The Real World"
APP_ENV=production
APP_KEY=base64:GENERATE_NEW_KEY_WITH_php_artisan_key:generate
APP_DEBUG=false
APP_URL=https://your-domain.com

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=the_real_world_lms
DB_USERNAME=your_db_username
DB_PASSWORD=your_secure_db_password

# Cache Configuration
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_mailgun_username
MAIL_PASSWORD=your_mailgun_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS S3 Configuration (for file storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket-name
AWS_USE_PATH_STYLE_ENDPOINT=false

# Filesystem
FILESYSTEM_DISK=s3

# Broadcasting (for real-time features)
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_app_key
PUSHER_APP_SECRET=your_pusher_app_secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Payment Gateways
# Stripe Configuration
STRIPE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Cryptocurrency Payment Configuration
CRYPTO_ENABLED=true
CRYPTO_BITCOIN_ADDRESS=your_bitcoin_wallet_address
CRYPTO_ETHEREUM_ADDRESS=your_ethereum_wallet_address
CRYPTO_WEBHOOK_SECRET=your_crypto_webhook_secret

# Social Authentication (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI="${APP_URL}/auth/google/callback"

FACEBOOK_CLIENT_ID=your_facebook_client_id
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
FACEBOOK_REDIRECT_URI="${APP_URL}/auth/facebook/callback"

# Analytics and Monitoring
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
SENTRY_LARAVEL_DSN=your_sentry_dsn

# Security
# Generate strong random strings for these
SANCTUM_STATEFUL_DOMAINS=your-domain.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=strict

# Rate Limiting
THROTTLE_REQUESTS_PER_MINUTE=60
API_RATE_LIMIT=1000

# File Upload Limits
MAX_UPLOAD_SIZE=10240  # 10MB in KB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,mp4,mov,avi

# Course Configuration
MAX_COURSES_PER_INSTRUCTOR=50
MAX_LESSONS_PER_COURSE=100
MAX_STUDENTS_PER_COURSE=10000

# Community and Chat
MAX_COMMUNITIES_PER_USER=10
MAX_CHAT_ROOMS_PER_USER=20
CHAT_MESSAGE_RETENTION_DAYS=365

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# CDN Configuration (Optional)
CDN_URL=https://cdn.your-domain.com
CDN_ENABLED=true

# Performance
OPCACHE_ENABLED=true
REDIS_CACHE_TTL=3600
VIEW_CACHE_ENABLED=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_SECRET=your_maintenance_secret

# API Configuration
API_VERSION=v1
API_RATE_LIMIT_PER_MINUTE=100

# Webhook Configuration
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3

# Search Configuration (if using Elasticsearch)
SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=your_meilisearch_key

# Video Processing (if using video transcoding)
VIDEO_PROCESSING_ENABLED=false
VIDEO_PROCESSING_QUEUE=video-processing

# Notification Channels
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_EMAIL=<EMAIL>

# SSL Configuration
FORCE_HTTPS=true
HSTS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://your-domain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Timezone
APP_TIMEZONE=UTC

# Localization
APP_LOCALE=en
APP_FALLBACK_LOCALE=en

# Queue Configuration
QUEUE_FAILED_DRIVER=database-uuids
HORIZON_ENABLED=true

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_SECRET=your_health_check_secret

# Feature Flags
FEATURE_CRYPTO_PAYMENTS=true
FEATURE_LIVE_CHAT=true
FEATURE_VIDEO_CALLS=false
FEATURE_CERTIFICATES=true
FEATURE_GAMIFICATION=true

# Third-party Integrations
ZOOM_API_KEY=your_zoom_api_key
ZOOM_API_SECRET=your_zoom_api_secret

# Content Delivery
IMAGE_OPTIMIZATION=true
VIDEO_STREAMING=true
PROGRESSIVE_WEB_APP=true

# Security Headers
CONTENT_SECURITY_POLICY=true
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff

# Monitoring and Logging
NEW_RELIC_LICENSE_KEY=your_new_relic_key
DATADOG_API_KEY=your_datadog_key

# Backup Storage
BACKUP_DISK=s3
BACKUP_S3_BUCKET=your-backup-bucket

# Database Optimization
DB_SLOW_QUERY_LOG=true
DB_SLOW_QUERY_TIME=2

# Cache Tags
CACHE_PREFIX=trl_prod_

# Session Configuration
SESSION_COOKIE_NAME=the_real_world_session
SESSION_COOKIE_HTTPONLY=true

# CSRF Protection
CSRF_COOKIE_NAME=XSRF-TOKEN
CSRF_COOKIE_HTTPONLY=false
