/* Search Page Styles */

.search-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

/* Search Header */
.search-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-header h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.search-query {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
}

.search-query strong {
    color: #3b82f6;
}

.search-count {
    color: #666666;
    font-size: 0.875rem;
}

/* Search Form */
.search-form-container {
    max-width: 800px;
    margin: 0 auto 3rem;
}

.search-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
}

.search-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: #a0a0a0;
}

.search-button {
    padding: 1rem 2rem;
    background: #3b82f6;
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    white-space: nowrap;
}

.search-button:hover {
    background: #2563eb;
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ffffff;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.3s ease;
}

.filter-option:hover {
    color: #3b82f6;
}

.filter-option input[type="radio"] {
    accent-color: #3b82f6;
}

/* Search Results */
.search-results {
    max-width: 1000px;
    margin: 0 auto;
}

.search-result-item {
    display: flex;
    gap: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
}

.result-content {
    flex: 1;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.result-type {
    padding: 0.25rem 0.75rem;
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    font-size: 0.75rem;
    color: #3b82f6;
    font-weight: 600;
}

.result-date {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.result-title {
    margin-bottom: 0.75rem;
}

.result-title a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.result-title a:hover {
    color: #3b82f6;
}

.result-description {
    color: #a0a0a0;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.result-meta {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.meta-item {
    color: #666666;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.result-image {
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-results h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-results p {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.search-suggestions h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
    text-align: left;
    display: inline-block;
}

.search-suggestions li {
    color: #a0a0a0;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.search-suggestions li::before {
    content: "•";
    color: #3b82f6;
    position: absolute;
    left: 0;
}

.search-suggestions a {
    color: #3b82f6;
    text-decoration: none;
}

.search-suggestions a:hover {
    text-decoration: underline;
}

/* Search Welcome */
.search-welcome {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.search-welcome h3 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.search-welcome p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 2rem;
}

.popular-searches h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.search-tags {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.search-tag {
    padding: 0.5rem 1rem;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.search-tag:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
}

/* Quick Links */
.search-quick-links {
    max-width: 800px;
    margin: 3rem auto 0;
    text-align: center;
}

.search-quick-links h4 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.quick-link:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.link-icon {
    font-size: 2rem;
}

.link-text {
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-main {
        padding: 1rem 0;
    }
    
    .search-header h1 {
        font-size: 2rem;
    }
    
    .search-form {
        padding: 1.5rem;
    }
    
    .search-input-group {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .search-filters {
        gap: 1rem;
    }
    
    .search-result-item {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .result-meta {
        gap: 1rem;
    }
    
    .result-image {
        width: 100%;
        height: 120px;
    }
    
    .quick-links-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .search-tags {
        flex-direction: column;
        align-items: center;
    }
    
    .quick-links-grid {
        grid-template-columns: 1fr;
    }
}
