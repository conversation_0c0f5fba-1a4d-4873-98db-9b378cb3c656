<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ListUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all users with their roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::with('roles')->get();
        
        if ($users->isEmpty()) {
            $this->info('No users found.');
            return 0;
        }
        
        $this->info('Users in the system:');
        $this->line('');
        
        $headers = ['ID', 'Name', 'Email', 'Roles', 'Created'];
        $rows = [];
        
        foreach ($users as $user) {
            $roles = $user->roles->pluck('name')->join(', ') ?: 'No roles';
            $rows[] = [
                $user->id,
                $user->name,
                $user->email,
                $roles,
                $user->created_at->format('Y-m-d H:i')
            ];
        }
        
        $this->table($headers, $rows);
        
        return 0;
    }
}
