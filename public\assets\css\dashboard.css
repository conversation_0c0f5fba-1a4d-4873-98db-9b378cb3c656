/* Dashboard Specific Styles */

.dashboard-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.dashboard-header {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.welcome-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.welcome-section p {
    font-size: 1.125rem;
    color: #a0a0a0;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #a0a0a0;
    font-weight: 500;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-section {
    min-height: 300px;
}

.empty-state {
    text-align: center;
    padding: 2rem;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state h4 {
    font-size: 1.25rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #a0a0a0;
    margin-bottom: 1.5rem;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.quick-action:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
    color: inherit;
}

.action-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 8px;
}

.action-text h4 {
    font-size: 1rem;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.action-text p {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin: 0;
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Activity Feed */
.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.activity-content h5 {
    font-size: 0.875rem;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.activity-content p {
    font-size: 0.75rem;
    color: #a0a0a0;
    margin: 0;
}

.activity-time {
    font-size: 0.75rem;
    color: #666666;
    margin-left: auto;
}

/* Course Cards */
.course-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.course-card:hover {
    transform: translateY(-4px);
}

.course-thumbnail {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.course-info {
    padding: 1rem;
}

.course-title {
    font-size: 1rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.course-progress {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
    
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stats-overview {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .welcome-section h1 {
        font-size: 2rem;
    }
    
    .dashboard-main {
        padding: 1rem 0;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .welcome-section h1 {
        font-size: 1.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}
