<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Course management
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',
            'publish courses',

            // Lesson management
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',

            // Community management
            'view communities',
            'create communities',
            'edit communities',
            'delete communities',
            'moderate communities',

            // Chat management
            'view chats',
            'create chats',
            'edit chats',
            'delete chats',
            'moderate chats',

            // Payment management
            'view payments',
            'process payments',
            'refund payments',

            // Admin functions
            'view admin dashboard',
            'manage settings',
            'view analytics',
            'manage roles',
            'manage permissions',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except super admin functions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'view users', 'create users', 'edit users',
            'view courses', 'create courses', 'edit courses', 'delete courses', 'publish courses',
            'view lessons', 'create lessons', 'edit lessons', 'delete lessons',
            'view communities', 'create communities', 'edit communities', 'moderate communities',
            'view chats', 'create chats', 'edit chats', 'moderate chats',
            'view payments', 'process payments',
            'view admin dashboard', 'view analytics',
        ]);

        // Instructor - can manage their own courses and lessons
        $instructor = Role::create(['name' => 'instructor']);
        $instructor->givePermissionTo([
            'view courses', 'create courses', 'edit courses',
            'view lessons', 'create lessons', 'edit lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
        ]);

        // Moderator - can moderate communities and chats
        $moderator = Role::create(['name' => 'moderator']);
        $moderator->givePermissionTo([
            'view courses', 'view lessons',
            'view communities', 'moderate communities',
            'view chats', 'moderate chats',
        ]);

        // Student - basic user permissions
        $student = Role::create(['name' => 'student']);
        $student->givePermissionTo([
            'view courses', 'view lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
        ]);
    }
}
