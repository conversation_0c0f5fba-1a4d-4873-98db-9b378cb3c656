<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create comprehensive permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'ban users',
            'unban users',
            'impersonate users',

            // Course management
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',
            'publish courses',
            'unpublish courses',
            'approve courses',
            'reject courses',
            'view course analytics',
            'manage course enrollments',

            // Lesson management
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',
            'publish lessons',
            'unpublish lessons',
            'reorder lessons',

            // Community management
            'view communities',
            'create communities',
            'edit communities',
            'delete communities',
            'moderate communities',
            'pin posts',
            'unpin posts',
            'lock posts',
            'unlock posts',

            // Chat management
            'view chats',
            'create chats',
            'edit chats',
            'delete chats',
            'moderate chats',
            'mute users',
            'unmute users',
            'kick users',

            // Payment management
            'view payments',
            'process payments',
            'refund payments',
            'view financial reports',
            'manage payment methods',
            'handle disputes',

            // Live streaming
            'create streams',
            'manage streams',
            'moderate streams',
            'view stream analytics',

            // Notifications
            'send notifications',
            'manage notification settings',
            'view notification analytics',

            // Analytics & Reports
            'view analytics',
            'view detailed analytics',
            'export reports',
            'view financial analytics',
            'view user analytics',

            // Admin functions
            'access admin panel',
            'manage settings',
            'manage roles',
            'manage permissions',
            'view system logs',
            'manage system',
            'backup system',
            'restore system',

            // Content moderation
            'moderate content',
            'approve content',
            'reject content',
            'flag content',
            'review reports',

            // API access
            'access api',
            'manage api keys',
            'view api logs',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except super admin functions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            // User management
            'view users', 'create users', 'edit users', 'ban users', 'unban users',

            // Course management
            'view courses', 'create courses', 'edit courses', 'delete courses',
            'publish courses', 'unpublish courses', 'approve courses', 'reject courses',
            'view course analytics', 'manage course enrollments',

            // Lesson management
            'view lessons', 'create lessons', 'edit lessons', 'delete lessons',
            'publish lessons', 'unpublish lessons', 'reorder lessons',

            // Community management
            'view communities', 'create communities', 'edit communities', 'moderate communities',
            'pin posts', 'unpin posts', 'lock posts', 'unlock posts',

            // Chat management
            'view chats', 'create chats', 'edit chats', 'moderate chats',
            'mute users', 'unmute users', 'kick users',

            // Payment management
            'view payments', 'process payments', 'refund payments',
            'view financial reports', 'handle disputes',

            // Live streaming
            'manage streams', 'moderate streams', 'view stream analytics',

            // Analytics & Reports
            'view analytics', 'view detailed analytics', 'export reports',
            'view financial analytics', 'view user analytics',

            // Admin functions
            'access admin panel', 'manage settings', 'view system logs',

            // Content moderation
            'moderate content', 'approve content', 'reject content', 'review reports',

            // Notifications
            'send notifications', 'manage notification settings', 'view notification analytics',
        ]);

        // Instructor - can manage their own courses and lessons
        $instructor = Role::create(['name' => 'instructor']);
        $instructor->givePermissionTo([
            // Course management (own courses)
            'view courses', 'create courses', 'edit courses',
            'publish courses', 'unpublish courses', 'view course analytics',
            'manage course enrollments',

            // Lesson management (own lessons)
            'view lessons', 'create lessons', 'edit lessons',
            'publish lessons', 'unpublish lessons', 'reorder lessons',

            // Community participation
            'view communities', 'create communities',

            // Chat participation
            'view chats', 'create chats',

            // Live streaming
            'create streams', 'manage streams', 'view stream analytics',

            // Payment viewing (own earnings)
            'view payments',

            // Basic analytics (own content)
            'view analytics',

            // Notifications (to students)
            'send notifications',
        ]);

        // Moderator - can moderate communities and chats
        $moderator = Role::create(['name' => 'moderator']);
        $moderator->givePermissionTo([
            // Basic viewing permissions
            'view courses', 'view lessons', 'view users',

            // Community moderation
            'view communities', 'moderate communities',
            'pin posts', 'unpin posts', 'lock posts', 'unlock posts',

            // Chat moderation
            'view chats', 'moderate chats',
            'mute users', 'unmute users', 'kick users',

            // Content moderation
            'moderate content', 'approve content', 'reject content',
            'flag content', 'review reports',

            // Stream moderation
            'moderate streams',
        ]);

        // Student - basic user permissions
        $student = Role::create(['name' => 'student']);
        $student->givePermissionTo([
            'view courses', 'view lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
        ]);
    }
}
