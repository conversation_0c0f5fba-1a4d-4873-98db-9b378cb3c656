<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create comprehensive permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'ban users',
            'unban users',
            'impersonate users',

            // Course management
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',
            'publish courses',
            'unpublish courses',
            'approve courses',
            'reject courses',
            'view course analytics',
            'manage course enrollments',

            // Lesson management
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',
            'publish lessons',
            'unpublish lessons',
            'reorder lessons',

            // Community management
            'view communities',
            'create communities',
            'edit communities',
            'delete communities',
            'moderate communities',
            'pin posts',
            'unpin posts',
            'lock posts',
            'unlock posts',

            // Chat management
            'view chats',
            'create chats',
            'edit chats',
            'delete chats',
            'moderate chats',
            'mute users',
            'unmute users',
            'kick users',

            // Payment management
            'view payments',
            'process payments',
            'refund payments',
            'view financial reports',
            'manage payment methods',
            'handle disputes',

            // Live streaming
            'create streams',
            'manage streams',
            'moderate streams',
            'view stream analytics',

            // Notifications
            'send notifications',
            'manage notification settings',
            'view notification analytics',

            // Analytics & Reports
            'view analytics',
            'view detailed analytics',
            'export reports',
            'view financial analytics',
            'view user analytics',

            // Admin functions
            'access admin panel',
            'manage settings',
            'manage roles',
            'manage permissions',
            'view system logs',
            'manage system',
            'backup system',
            'restore system',

            // Content moderation
            'moderate content',
            'approve content',
            'reject content',
            'flag content',
            'review reports',

            // API access
            'access api',
            'manage api keys',
            'view api logs',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except super admin functions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'view users', 'create users', 'edit users',
            'view courses', 'create courses', 'edit courses', 'delete courses', 'publish courses',
            'view lessons', 'create lessons', 'edit lessons', 'delete lessons',
            'view communities', 'create communities', 'edit communities', 'moderate communities',
            'view chats', 'create chats', 'edit chats', 'moderate chats',
            'view payments', 'process payments',
            'view admin dashboard', 'view analytics',
        ]);

        // Instructor - can manage their own courses and lessons
        $instructor = Role::create(['name' => 'instructor']);
        $instructor->givePermissionTo([
            'view courses', 'create courses', 'edit courses',
            'view lessons', 'create lessons', 'edit lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
        ]);

        // Moderator - can moderate communities and chats
        $moderator = Role::create(['name' => 'moderator']);
        $moderator->givePermissionTo([
            'view courses', 'view lessons',
            'view communities', 'moderate communities',
            'view chats', 'moderate chats',
        ]);

        // Student - basic user permissions
        $student = Role::create(['name' => 'student']);
        $student->givePermissionTo([
            'view courses', 'view lessons',
            'view communities', 'create communities',
            'view chats', 'create chats',
        ]);
    }
}
