<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StreamChatMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'stream_id',
        'user_id',
        'message',
        'type',
        'is_pinned',
        'is_highlighted',
    ];

    protected $casts = [
        'is_pinned' => 'boolean',
        'is_highlighted' => 'boolean',
    ];

    /**
     * Get the stream that owns the message.
     */
    public function stream()
    {
        return $this->belongsTo(Stream::class);
    }

    /**
     * Get the user that sent the message.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for pinned messages.
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    /**
     * Scope for highlighted messages.
     */
    public function scopeHighlighted($query)
    {
        return $query->where('is_highlighted', true);
    }

    /**
     * Scope for regular chat messages.
     */
    public function scopeChat($query)
    {
        return $query->where('type', 'chat');
    }

    /**
     * Scope for system messages.
     */
    public function scopeSystem($query)
    {
        return $query->where('type', 'system');
    }

    /**
     * Get formatted timestamp.
     */
    public function getFormattedTimeAttribute()
    {
        return $this->created_at->format('H:i');
    }

    /**
     * Check if message is from instructor.
     */
    public function isFromInstructor()
    {
        return $this->user_id === $this->stream->instructor_id;
    }

    /**
     * Check if message is a system message.
     */
    public function isSystemMessage()
    {
        return $this->type === 'system';
    }
}
