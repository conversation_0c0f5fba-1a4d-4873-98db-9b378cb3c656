<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Chat - <?php echo e(config('app.name')); ?></title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/chat.css')); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="<?php echo e(route('home')); ?>">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                <li><a href="<?php echo e(route('communities.index')); ?>">Community</a></li>
                <li><a href="<?php echo e(route('chat.index')); ?>" class="active">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="chat-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Real-Time Chat</h1>
                    <p>Connect instantly with the community</p>
                </div>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create chats')): ?>
                    <div class="header-actions">
                        <a href="<?php echo e(route('chat.create')); ?>" class="btn btn-primary">
                            Create Chat Room
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Chat Rooms Layout -->
            <div class="chat-layout">
                <!-- My Chat Rooms -->
                <?php if($joinedRooms->count() > 0): ?>
                    <div class="chat-section">
                        <h3 class="section-title">My Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            <?php $__currentLoopData = $joinedRooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="chat-room-card active-room">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="<?php echo e(route('chat.show', $room)); ?>"><?php echo e($room->name); ?></a>
                                            </h4>
                                            <span class="room-type"><?php echo e(ucfirst($room->type)); ?></span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count"><?php echo e($room->member_count); ?></span>
                                        </div>
                                    </div>
                                    
                                    <?php if($room->description): ?>
                                        <p class="room-description"><?php echo e(Str::limit($room->description, 100)); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if($room->course): ?>
                                        <div class="room-course">
                                            <span class="course-label">Course:</span>
                                            <span class="course-name"><?php echo e($room->course->title); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value"><?php echo e($room->member_count); ?> members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value"><?php echo e($room->message_count); ?> messages</span>
                                        </div>
                                        <?php if($room->last_message_at): ?>
                                            <div class="stat">
                                                <span class="stat-icon">🕒</span>
                                                <span class="stat-value"><?php echo e($room->last_message_at->diffForHumans()); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="<?php echo e(route('chat.show', $room)); ?>" class="btn btn-primary btn-block">
                                            Enter Chat
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Public Chat Rooms -->
                <?php if($publicRooms->count() > 0): ?>
                    <div class="chat-section">
                        <h3 class="section-title">Public Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            <?php $__currentLoopData = $publicRooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="chat-room-card">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="<?php echo e(route('chat.show', $room)); ?>"><?php echo e($room->name); ?></a>
                                            </h4>
                                            <span class="room-type"><?php echo e(ucfirst($room->type)); ?></span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count"><?php echo e($room->member_count); ?></span>
                                        </div>
                                    </div>
                                    
                                    <?php if($room->description): ?>
                                        <p class="room-description"><?php echo e(Str::limit($room->description, 100)); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value"><?php echo e($room->member_count); ?> members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value"><?php echo e($room->message_count); ?> messages</span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="<?php echo e(route('chat.show', $room)); ?>" class="btn btn-secondary btn-block">
                                            Join & Chat
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Course Chat Rooms -->
                <?php if($courseRooms->count() > 0): ?>
                    <div class="chat-section">
                        <h3 class="section-title">Course Chat Rooms</h3>
                        <div class="chat-rooms-grid">
                            <?php $__currentLoopData = $courseRooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="chat-room-card course-room">
                                    <div class="room-header">
                                        <div class="room-info">
                                            <h4 class="room-name">
                                                <a href="<?php echo e(route('chat.show', $room)); ?>"><?php echo e($room->name); ?></a>
                                            </h4>
                                            <span class="room-type">Course Chat</span>
                                        </div>
                                        <div class="room-status">
                                            <span class="online-indicator"></span>
                                            <span class="member-count"><?php echo e($room->member_count); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-course">
                                        <span class="course-label">Course:</span>
                                        <a href="<?php echo e(route('courses.show', $room->course)); ?>" class="course-link">
                                            <?php echo e($room->course->title); ?>

                                        </a>
                                    </div>
                                    
                                    <?php if($room->description): ?>
                                        <p class="room-description"><?php echo e(Str::limit($room->description, 100)); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="room-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value"><?php echo e($room->member_count); ?> members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value"><?php echo e($room->message_count); ?> messages</span>
                                        </div>
                                    </div>
                                    
                                    <div class="room-actions">
                                        <a href="<?php echo e(route('chat.show', $room)); ?>" class="btn btn-primary btn-block">
                                            Enter Chat
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Empty State -->
                <?php if($joinedRooms->count() === 0 && $publicRooms->count() === 0 && $courseRooms->count() === 0): ?>
                    <div class="empty-state">
                        <div class="empty-icon">💬</div>
                        <h3>No chat rooms available</h3>
                        <p>Start by creating a chat room or enrolling in courses to access course-specific chats</p>
                        <div class="empty-actions">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create chats')): ?>
                                <a href="<?php echo e(route('chat.create')); ?>" class="btn btn-primary">
                                    Create Chat Room
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-secondary">
                                Browse Courses
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Chat Guidelines -->
            <div class="chat-guidelines">
                <h4>Chat Guidelines</h4>
                <div class="guidelines-grid">
                    <div class="guideline-item">
                        <span class="guideline-icon">🤝</span>
                        <span class="guideline-text">Be respectful to all members</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">💡</span>
                        <span class="guideline-text">Share valuable insights and knowledge</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">🚫</span>
                        <span class="guideline-text">No spam, advertising, or off-topic content</span>
                    </div>
                    <div class="guideline-item">
                        <span class="guideline-icon">🎯</span>
                        <span class="guideline-text">Stay focused on growth and success</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/chat.js')); ?>"></script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/chat/index.blade.php ENDPATH**/ ?>