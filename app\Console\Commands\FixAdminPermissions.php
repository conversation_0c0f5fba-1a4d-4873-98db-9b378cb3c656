<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;

class FixAdminPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:fix-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix admin permissions by adding missing permissions to super-admin role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing admin permissions...');
        
        // Create the missing permission if it doesn't exist
        $permission = Permission::firstOrCreate(['name' => 'view admin dashboard']);
        $this->info("Permission 'view admin dashboard' created/found.");
        
        // Get the super-admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        
        if (!$superAdminRole) {
            $this->error('Super-admin role not found!');
            return 1;
        }
        
        // Add the permission to super-admin role if not already assigned
        if (!$superAdminRole->hasPermissionTo('view admin dashboard')) {
            $superAdminRole->givePermissionTo('view admin dashboard');
            $this->info("Permission 'view admin dashboard' added to super-admin role.");
        } else {
            $this->info("Permission 'view admin dashboard' already exists for super-admin role.");
        }
        
        // Also ensure admin role has this permission
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        if (!$adminRole->hasPermissionTo('view admin dashboard')) {
            $adminRole->givePermissionTo('view admin dashboard');
            $this->info("Permission 'view admin dashboard' added to admin role.");
        }
        
        // List all permissions for super-admin role
        $this->info('Current super-admin permissions:');
        foreach ($superAdminRole->permissions as $perm) {
            $this->line("  - {$perm->name}");
        }
        
        $this->info('Admin permissions fixed successfully!');
        
        return 0;
    }
}
