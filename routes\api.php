<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CourseApiController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Api\PaymentApiController;
use App\Http\Controllers\Api\CommunityApiController;
use App\Http\Controllers\Api\ChatApiController;
use App\Http\Controllers\Api\NotificationApiController;
use App\Http\Controllers\SearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes
Route::prefix('v1')->group(function () {
    // Public course data
    Route::get('/courses', [CourseApiController::class, 'index']);
    Route::get('/courses/{course}', [CourseApiController::class, 'show']);

    // Search functionality
    Route::get('/search', [SearchController::class, 'index']);
    Route::get('/search/autocomplete', [SearchController::class, 'autocomplete']);

    // Crypto prices (for payment)
    Route::get('/crypto/prices', function () {
        return response()->json([
            'prices' => [
                'BTC' => 45000.00,
                'ETH' => 3200.00,
                'USDT' => 1.00,
                'USDC' => 1.00,
            ],
            'updated_at' => now()->toISOString(),
        ]);
    });

    // System status
    Route::get('/status', function () {
        return response()->json([
            'status' => 'online',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0',
        ]);
    });
});

// Protected API routes
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // User routes
    Route::get('/user', function (Request $request) {
        return $request->user()->load(['roles', 'enrolledCourses']);
    });

    // Notifications
    Route::get('/notifications', [NotificationApiController::class, 'index']);
    Route::post('/notifications/{notification}/read', [NotificationApiController::class, 'markAsRead']);
    Route::get('/notifications/unread-count', [NotificationApiController::class, 'unreadCount']);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toISOString(),
        'services' => [
            'database' => 'connected',
            'redis' => 'connected',
            'storage' => 'accessible',
        ],
    ]);
});
