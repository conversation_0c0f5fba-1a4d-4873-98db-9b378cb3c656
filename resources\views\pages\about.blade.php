@extends('layouts.minimal')

@section('title', 'About Us')

@section('content')
<div class="page-container">
    <div class="page-header">
        <h1>🌍 About The Real World</h1>
        <p class="page-subtitle">Escape the matrix through education and community</p>
    </div>

    <div class="page-content">
        <div class="content-section">
            <h2>Our Mission</h2>
            <p>
                The Real World is more than just an educational platform - it's a movement. We believe that traditional education has failed to prepare people for the realities of modern life and business. Our mission is to provide practical, real-world education that actually works.
            </p>
        </div>

        <div class="content-section">
            <h2>What We Offer</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📚</div>
                    <h3>Real-World Courses</h3>
                    <p>Learn practical skills from successful entrepreneurs and industry experts.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🏘️</div>
                    <h3>Exclusive Community</h3>
                    <p>Connect with like-minded individuals who are serious about success.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <h3>Live Mentorship</h3>
                    <p>Get direct access to mentors through live streams and chat.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>Proven Results</h3>
                    <p>Our students have generated millions in revenue using our methods.</p>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2>Our Values</h2>
            <ul class="values-list">
                <li><strong>Truth:</strong> We tell you what you need to hear, not what you want to hear.</li>
                <li><strong>Action:</strong> Knowledge without action is worthless. We focus on implementation.</li>
                <li><strong>Results:</strong> We measure success by the real-world results our students achieve.</li>
                <li><strong>Community:</strong> Success is easier when you're surrounded by winners.</li>
                <li><strong>Excellence:</strong> We demand the highest standards from ourselves and our students.</li>
            </ul>
        </div>

        <div class="content-section">
            <h2>Join The Movement</h2>
            <p>
                Ready to escape the matrix and build the life you deserve? Join thousands of students who have already transformed their lives through The Real World.
            </p>
            
            <div class="cta-buttons">
                <a href="{{ route('register') }}" class="btn btn-primary">Join Now</a>
                <a href="{{ route('courses.index') }}" class="btn btn-secondary">View Courses</a>
            </div>
        </div>
    </div>
</div>

<style>
.page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin: 0;
}

.content-section {
    margin-bottom: 3rem;
}

.content-section h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.content-section p {
    font-size: 1rem;
    line-height: 1.7;
    color: #475569;
    margin-bottom: 1rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.feature-card p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

.values-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.values-list li {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: #475569;
}

.values-list li strong {
    color: #1e293b;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .page-container {
        padding: 1rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>
@endsection
