@extends('layouts.admin')

@section('title', 'User Management')

@section('content')
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>User Management</h1>
                <p>Manage platform users, roles, and permissions</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                    + Add User
                </a>
            </div>
        </div>
    </div>

        <!-- Filters -->
        <div class="content-section">
            <div class="filters-card">
                <form method="GET" action="{{ route('admin.users.index') }}" class="filters-form">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="search">Search Users</label>
                            <input type="text" id="search" name="search" 
                                   value="{{ request('search') }}" 
                                   placeholder="Search by name or email..."
                                   class="search-input">
                        </div>
                        
                        <div class="filter-group">
                            <label for="role">Role</label>
                            <select id="role" name="role" class="filter-select">
                                <option value="">All Roles</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->name }}" 
                                            {{ request('role') === $role->name ? 'selected' : '' }}>
                                        {{ ucfirst($role->name) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="filter-select">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_from">From Date</label>
                            <input type="date" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}" class="filter-input">
                        </div>
                        
                        <div class="filter-group">
                            <label for="date_to">To Date</label>
                            <input type="date" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}" class="filter-input">
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Clear</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="content-section">
            <div class="table-card">
                <div class="table-header">
                    <h3>Users ({{ $users->total() }})</h3>
                    <div class="table-actions">
                        <button onclick="exportData('users')" class="btn btn-secondary btn-sm">
                            📊 Export CSV
                        </button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th data-sortable>ID</th>
                                <th data-sortable>Name</th>
                                <th data-sortable>Email</th>
                                <th>Roles</th>
                                <th data-sortable>Status</th>
                                <th data-sortable>Courses</th>
                                <th data-sortable>Payments</th>
                                <th data-sortable>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($users as $user)
                                <tr data-user-id="{{ $user->id }}">
                                    <td>{{ $user->id }}</td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">{{ substr($user->name, 0, 1) }}</div>
                                            <div class="user-details">
                                                <div class="user-name">{{ $user->name }}</div>
                                                @if($user->email_verified_at)
                                                    <span class="verified-badge">✓ Verified</span>
                                                @else
                                                    <span class="unverified-badge">⚠ Unverified</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>
                                        <div class="user-roles">
                                            @foreach($user->roles as $role)
                                                <span class="role-badge role-{{ $role->name }}">
                                                    {{ ucfirst($role->name) }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </td>
                                    <td>
                                        <span class="user-status {{ $user->is_active ? 'active' : 'inactive' }}">
                                            {{ $user->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>{{ $user->enrolled_courses_count }}</td>
                                    <td>${{ number_format($user->payments_count * 99, 2) }}</td>
                                    <td>{{ $user->created_at->format('M j, Y') }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.users.show', $user) }}" 
                                               class="btn btn-sm btn-secondary" title="View">
                                                👁️
                                            </a>
                                            <a href="{{ route('admin.users.edit', $user) }}" 
                                               class="btn btn-sm btn-primary" title="Edit">
                                                ✏️
                                            </a>
                                            <button onclick="toggleUserStatus({{ $user->id }})" 
                                                    class="btn btn-sm btn-warning" title="Toggle Status">
                                                {{ $user->is_active ? '🔒' : '🔓' }}
                                            </button>
                                            @if(!$user->hasRole('super-admin') && $user->id !== Auth::id())
                                                <form method="POST" action="{{ route('admin.users.impersonate', $user) }}" 
                                                      style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-info" title="Impersonate">
                                                        👤
                                                    </button>
                                                </form>
                                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" 
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        🗑️
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="empty-state">
                                        <div class="empty-content">
                                            <div class="empty-icon">👥</div>
                                            <h3>No users found</h3>
                                            <p>No users match your current filters.</p>
                                            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                                Add First User
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                @if($users->hasPages())
                    <div class="pagination-wrapper">
                        {{ $users->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="content-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h4>{{ $users->total() }}</h4>
                        <p>Total Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h4>{{ $users->where('is_active', true)->count() }}</h4>
                        <p>Active Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📧</div>
                    <div class="stat-content">
                        <h4>{{ $users->whereNotNull('email_verified_at')->count() }}</h4>
                        <p>Verified Users</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h4>{{ $users->where('created_at', '>=', now()->subDays(30))->count() }}</h4>
                        <p>New This Month</p>
                    </div>
                </div>
            </div>
        </div>
@endsection

@push('scripts')
<script>
        // Additional user management specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit search form on input
            const searchInput = document.getElementById('search');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        this.form.submit();
                    }
                }, 500);
            });
        });
        
        // Toggle user status function
        function toggleUserStatus(userId) {
            if (confirm('Are you sure you want to change this user\'s status?')) {
                toggleUserStatus(userId);
            }
        }
</script>
@endpush
