/* Landing Page Styles - The Real World */

/* Navigation */
.landing-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    padding: 1rem 0;
}

.landing-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.nav-link {
    color: #a0a0a0;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #ffffff;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(239, 68, 68, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding-top: 80px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-title .highlight {
    background: linear-gradient(135deg, #3b82f6, #ef4444);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #a0a0a0;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 700;
}

.hero-stats {
    display: flex;
    gap: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
}

.stat-label {
    font-size: 0.875rem;
    color: #a0a0a0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: #0f0f0f;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.25rem;
    color: #a0a0a0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #a0a0a0;
    line-height: 1.6;
}

/* Community Section */
.community {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
}

.community-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.community-text h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.community-text p {
    font-size: 1.125rem;
    color: #a0a0a0;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.community-features {
    list-style: none;
    padding: 0;
}

.community-features li {
    color: #ffffff;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.community-features li::before {
    content: "✅ ";
    margin-right: 0.5rem;
}

.chat-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.chat-header {
    background: rgba(59, 130, 246, 0.2);
    padding: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.chat-messages {
    padding: 1rem;
}

.chat-message {
    margin-bottom: 0.75rem;
    color: #a0a0a0;
    font-size: 0.875rem;
}

.chat-message strong {
    color: #ffffff;
}

/* Pricing Section */
.pricing {
    padding: 6rem 0;
    background: #0f0f0f;
}

.pricing-cards {
    display: flex;
    justify-content: center;
}

.pricing-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.pricing-card.featured {
    border-color: #3b82f6;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    color: #a0a0a0;
    vertical-align: top;
}

.amount {
    font-size: 4rem;
    font-weight: 700;
    color: #3b82f6;
}

.period {
    font-size: 1rem;
    color: #a0a0a0;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.pricing-features li {
    margin-bottom: 0.75rem;
    color: #a0a0a0;
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.25rem;
    color: #a0a0a0;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Footer */
.footer {
    background: #0a0a0a;
    padding: 4rem 0 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 2rem;
}

.footer-brand h3 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.footer-brand p {
    color: #a0a0a0;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.footer-column a {
    display: block;
    color: #a0a0a0;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #ffffff;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: #666666;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .community-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
    
    .nav-links {
        gap: 1rem;
    }
}
