/* Landing Page Styles - The Real World */

/* Landing Navigation */
.landing-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #ffffff;
}

.brand-icon {
    font-size: 1.8rem;
}

.brand-text {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: #a0a0a0;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.login-btn {
    color: #a0a0a0;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.register-btn,
.dashboard-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.register-btn:hover,
.dashboard-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

/* Mobile Menu */
.mobile-menu-btn {
    display: none;
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hamburger {
    display: block;
    width: 25px;
    height: 3px;
    background: #ffffff;
    position: relative;
    transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.hamburger::before {
    top: -8px;
}

.hamburger::after {
    top: 8px;
}

.mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(15, 15, 15, 0.98);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.mobile-menu-content {
    padding: 2rem;
}

.mobile-nav-link {
    display: block;
    padding: 1rem 0;
    color: #a0a0a0;
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: color 0.3s ease;
}

.mobile-nav-link:hover {
    color: #ffffff;
}

.mobile-nav-link:last-child {
    border-bottom: none;
}

.mobile-nav-actions {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-nav-btn {
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.mobile-nav-btn.login-btn {
    color: #a0a0a0;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mobile-nav-btn.register-btn,
.mobile-nav-btn.dashboard-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

@media (max-width: 768px) {
    .nav-links,
    .nav-actions {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(239, 68, 68, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding-top: 80px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-title .highlight {
    background: linear-gradient(135deg, #3b82f6, #ef4444);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #a0a0a0;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 700;
}

.hero-stats {
    display: flex;
    gap: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
}

.stat-label {
    font-size: 0.875rem;
    color: #a0a0a0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: #0f0f0f;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.25rem;
    color: #a0a0a0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #a0a0a0;
    line-height: 1.6;
}

/* Community Section */
.community {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
}

.community-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.community-text h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.community-text p {
    font-size: 1.125rem;
    color: #a0a0a0;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.community-features {
    list-style: none;
    padding: 0;
}

.community-features li {
    color: #ffffff;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.community-features li::before {
    content: "✅ ";
    margin-right: 0.5rem;
}

.chat-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.chat-header {
    background: rgba(59, 130, 246, 0.2);
    padding: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.chat-messages {
    padding: 1rem;
}

.chat-message {
    margin-bottom: 0.75rem;
    color: #a0a0a0;
    font-size: 0.875rem;
}

.chat-message strong {
    color: #ffffff;
}

/* Pricing Section */
.pricing {
    padding: 6rem 0;
    background: #0f0f0f;
}

.pricing-cards {
    display: flex;
    justify-content: center;
}

.pricing-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.pricing-card.featured {
    border-color: #3b82f6;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    color: #a0a0a0;
    vertical-align: top;
}

.amount {
    font-size: 4rem;
    font-weight: 700;
    color: #3b82f6;
}

.period {
    font-size: 1rem;
    color: #a0a0a0;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.pricing-features li {
    margin-bottom: 0.75rem;
    color: #a0a0a0;
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.25rem;
    color: #a0a0a0;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Footer */
.footer {
    background: #0a0a0a;
    padding: 4rem 0 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 2rem;
}

.footer-brand h3 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.footer-brand p {
    color: #a0a0a0;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.footer-column a {
    display: block;
    color: #a0a0a0;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #ffffff;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: #666666;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .community-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
    
    .nav-links {
        gap: 1rem;
    }
}

/* Landing Footer */
.landing-footer {
    background: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 4rem 0 2rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.footer-logo {
    font-size: 2rem;
}

.footer-brand h3 {
    color: #ffffff;
    font-weight: 700;
    margin: 0;
}

.footer-description {
    color: #a0a0a0;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #a0a0a0;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    transform: translateY(-2px);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #a0a0a0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ffffff;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #666666;
    margin: 0;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-stats {
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
}

/* Landing Body */
.landing-body {
    background: #0f0f0f;
    color: #ffffff;
    overflow-x: hidden;
}

.landing-body.mobile-menu-open {
    overflow: hidden;
}

.landing-main {
    padding-top: 70px;
}

/* Responsive Footer */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
    }

    .brand-section {
        grid-column: 1 / -1;
        text-align: center;
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
        font-size: 1.25rem;
    }
}
