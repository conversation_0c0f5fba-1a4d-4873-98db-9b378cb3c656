<?php

namespace App\Http\Controllers\Course;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EnrollmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Enroll user in a course.
     */
    public function enroll(Course $course)
    {
        $user = Auth::user();

        // Check if already enrolled
        if ($course->students()->where('user_id', $user->id)->exists()) {
            return redirect()->route('courses.show', $course)
                           ->with('info', 'You are already enrolled in this course.');
        }

        DB::transaction(function () use ($course, $user) {
            // Create enrollment record
            CourseEnrollment::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'enrolled_at' => now(),
                'amount_paid' => $course->is_free ? 0 : $course->price,
                'payment_status' => $course->is_free ? 'completed' : 'pending',
                'enrollment_type' => $course->is_free ? 'free' : 'paid',
            ]);

            // Update course student count
            $course->increment('total_students');
        });

        if ($course->is_free) {
            return redirect()->route('courses.show', $course)
                           ->with('success', 'Successfully enrolled in the course!');
        } else {
            // Redirect to payment page
            return redirect()->route('payment.course', $course)
                           ->with('info', 'Please complete payment to access the course.');
        }
    }

    /**
     * Display user's enrolled courses.
     */
    public function index()
    {
        $user = Auth::user();
        $enrollments = $user->enrolledCourses()
                           ->with(['category', 'instructor'])
                           ->orderBy('course_enrollments.enrolled_at', 'desc')
                           ->paginate(12);

        return view('course.my-courses', compact('enrollments'));
    }

    /**
     * Show enrollment details.
     */
    public function show(Course $course)
    {
        $user = Auth::user();
        $enrollment = $course->enrollments()
                            ->where('user_id', $user->id)
                            ->firstOrFail();

        $progress = $course->getCompletionPercentage($user);
        $completedLessons = $user->lessonCompletions()
                                ->whereHas('lesson', function ($query) use ($course) {
                                    $query->where('course_id', $course->id);
                                })->count();

        return view('course.enrollment', compact('course', 'enrollment', 'progress', 'completedLessons'));
    }

    /**
     * Unenroll from course (if allowed).
     */
    public function destroy(Course $course)
    {
        $user = Auth::user();
        $enrollment = $course->enrollments()
                            ->where('user_id', $user->id)
                            ->first();

        if (!$enrollment) {
            return redirect()->route('courses.show', $course)
                           ->with('error', 'You are not enrolled in this course.');
        }

        // Check if unenrollment is allowed (e.g., within refund period)
        $canUnenroll = $enrollment->enrolled_at->diffInDays(now()) <= 30;

        if (!$canUnenroll && !$course->is_free) {
            return redirect()->route('courses.show', $course)
                           ->with('error', 'Unenrollment period has expired.');
        }

        DB::transaction(function () use ($course, $enrollment) {
            // Delete enrollment
            $enrollment->delete();

            // Update course student count
            $course->decrement('total_students');
        });

        return redirect()->route('courses.index')
                       ->with('success', 'Successfully unenrolled from the course.');
    }

    /**
     * Get enrollment statistics for instructor.
     */
    public function statistics(Course $course)
    {
        $this->authorize('view', $course);

        $enrollments = $course->enrollments()
                             ->with('user')
                             ->orderBy('enrolled_at', 'desc')
                             ->paginate(20);

        $stats = [
            'total_enrollments' => $course->enrollments()->count(),
            'completed_enrollments' => $course->enrollments()->whereNotNull('completed_at')->count(),
            'revenue' => $course->enrollments()->where('payment_status', 'completed')->sum('amount_paid'),
            'average_progress' => $course->enrollments()->avg('progress') ?? 0,
        ];

        return view('course.enrollment-stats', compact('course', 'enrollments', 'stats'));
    }
}
