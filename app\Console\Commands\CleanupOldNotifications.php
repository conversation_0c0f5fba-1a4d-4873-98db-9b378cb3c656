<?php

namespace App\Console\Commands;

use App\Models\Notification;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupOldNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:cleanup-notifications {--days=90 : Number of days to keep notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old notifications from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Cleaning up notifications older than {$days} days...");

        $count = Notification::where('created_at', '<', $cutoffDate)->count();

        if ($count === 0) {
            $this->info('No old notifications found to clean up.');
            return 0;
        }

        if ($this->confirm("This will delete {$count} notifications. Continue?")) {
            $deleted = Notification::where('created_at', '<', $cutoffDate)->delete();
            $this->info("Successfully deleted {$deleted} old notifications.");
        } else {
            $this->info('Cleanup cancelled.');
        }

        return 0;
    }
}
