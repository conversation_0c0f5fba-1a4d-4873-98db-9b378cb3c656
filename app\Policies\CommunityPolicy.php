<?php

namespace App\Policies;

use App\Models\Community;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CommunityPolicy
{
    /**
     * Determine whether the user can view any communities.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view communities');
    }

    /**
     * Determine whether the user can view the community.
     */
    public function view(User $user, Community $community): bool
    {
        // Public communities can be viewed by anyone
        if ($community->is_public) {
            return true;
        }

        // Private communities require membership or admin access
        if ($community->members()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // Admins and moderators can view all communities
        return $user->hasAnyRole(['admin', 'super-admin', 'moderator']);
    }

    /**
     * Determine whether the user can create communities.
     */
    public function create(User $user): bool
    {
        return $user->can('create communities');
    }

    /**
     * Determine whether the user can update the community.
     */
    public function update(User $user, Community $community): bool
    {
        // Community owners can edit their communities
        if ($user->id === $community->creator_id && $user->can('edit communities')) {
            return true;
        }

        // Admins can edit all communities
        return $user->can('edit communities') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can delete the community.
     */
    public function delete(User $user, Community $community): bool
    {
        // Community owners can delete their communities
        if ($user->id === $community->creator_id && $user->can('delete communities')) {
            return true;
        }

        // Admins can delete any community
        return $user->can('delete communities') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can restore the community.
     */
    public function restore(User $user, Community $community): bool
    {
        return $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can permanently delete the community.
     */
    public function forceDelete(User $user, Community $community): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can moderate the community.
     */
    public function moderate(User $user, Community $community): bool
    {
        // Community owners can moderate their communities
        if ($user->id === $community->creator_id) {
            return true;
        }

        // Check if user is a moderator of this community
        if ($community->moderators()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // Global moderators and admins can moderate any community
        return $user->can('moderate communities') && $user->hasAnyRole(['moderator', 'admin', 'super-admin']);
    }

    /**
     * Determine whether the user can join the community.
     */
    public function join(User $user, Community $community): bool
    {
        // User cannot join if already a member
        if ($community->members()->where('user_id', $user->id)->exists()) {
            return false;
        }

        // Public communities can be joined by anyone
        if ($community->is_public) {
            return true;
        }

        // Private communities require invitation or admin approval
        return false;
    }

    /**
     * Determine whether the user can leave the community.
     */
    public function leave(User $user, Community $community): bool
    {
        // Community owners cannot leave their own community
        if ($user->id === $community->creator_id) {
            return false;
        }

        // User must be a member to leave
        return $community->members()->where('user_id', $user->id)->exists();
    }

    /**
     * Determine whether the user can post in the community.
     */
    public function post(User $user, Community $community): bool
    {
        // User must be a member to post
        if (!$community->members()->where('user_id', $user->id)->exists()) {
            return false;
        }

        // Check if user is banned from posting
        $membership = $community->members()->where('user_id', $user->id)->first();
        if ($membership && $membership->pivot->is_banned) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can pin posts in the community.
     */
    public function pinPosts(User $user, Community $community): bool
    {
        return $this->moderate($user, $community) && $user->can('pin posts');
    }

    /**
     * Determine whether the user can lock posts in the community.
     */
    public function lockPosts(User $user, Community $community): bool
    {
        return $this->moderate($user, $community) && $user->can('lock posts');
    }

    /**
     * Determine whether the user can manage community members.
     */
    public function manageMembers(User $user, Community $community): bool
    {
        // Community owners can manage members
        if ($user->id === $community->creator_id) {
            return true;
        }

        // Community moderators can manage members
        if ($community->moderators()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // Global admins can manage members
        return $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can invite others to the community.
     */
    public function invite(User $user, Community $community): bool
    {
        // Must be a member to invite others
        if (!$community->members()->where('user_id', $user->id)->exists()) {
            return false;
        }

        // Check community settings for who can invite
        if ($community->allow_member_invites) {
            return true;
        }

        // Only moderators and owners can invite if member invites are disabled
        return $this->moderate($user, $community);
    }
}
