<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Course;
use App\Models\Payment;
use App\Models\Community;
use App\Models\ChatRoom;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:access admin panel');
    }

    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // Get key metrics
        $metrics = $this->getKeyMetrics();

        // Get recent activities
        $recentActivities = $this->getRecentActivities();

        // Get revenue data for charts
        $revenueData = $this->getRevenueData();

        // Get user growth data
        $userGrowthData = $this->getUserGrowthData();

        // Get top performing courses
        $topCourses = $this->getTopCourses();

        return view('admin.dashboard', compact(
            'metrics',
            'recentActivities',
            'revenueData',
            'userGrowthData',
            'topCourses'
        ));
    }

    /**
     * Get key platform metrics.
     */
    private function getKeyMetrics()
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_users' => [
                'value' => User::count(),
                'change' => $this->calculatePercentageChange(
                    User::whereDate('created_at', $today)->count(),
                    User::whereDate('created_at', $yesterday)->count()
                ),
                'trend' => 'up'
            ],
            'total_courses' => [
                'value' => Course::where('status', 'published')->count(),
                'change' => $this->calculatePercentageChange(
                    Course::where('status', 'published')->whereDate('created_at', $today)->count(),
                    Course::where('status', 'published')->whereDate('created_at', $yesterday)->count()
                ),
                'trend' => 'up'
            ],
            'total_revenue' => [
                'value' => Payment::where('status', 'completed')->sum('amount'),
                'change' => $this->calculatePercentageChange(
                    Payment::where('status', 'completed')->whereDate('completed_at', $today)->sum('amount'),
                    Payment::where('status', 'completed')->whereDate('completed_at', $yesterday)->sum('amount')
                ),
                'trend' => 'up'
            ],
            'active_enrollments' => [
                'value' => CourseEnrollment::whereNull('completed_at')->count(),
                'change' => $this->calculatePercentageChange(
                    CourseEnrollment::whereDate('enrolled_at', $today)->count(),
                    CourseEnrollment::whereDate('enrolled_at', $yesterday)->count()
                ),
                'trend' => 'up'
            ],
            'monthly_revenue' => [
                'value' => Payment::where('status', 'completed')
                    ->whereBetween('completed_at', [$thisMonth, now()])
                    ->sum('amount'),
                'change' => $this->calculatePercentageChange(
                    Payment::where('status', 'completed')
                        ->whereBetween('completed_at', [$thisMonth, now()])
                        ->sum('amount'),
                    Payment::where('status', 'completed')
                        ->whereBetween('completed_at', [$lastMonth, $thisMonth])
                        ->sum('amount')
                ),
                'trend' => 'up'
            ],
            'completion_rate' => [
                'value' => $this->calculateCompletionRate(),
                'change' => 0, // Would need historical data for comparison
                'trend' => 'stable'
            ]
        ];
    }

    /**
     * Get recent platform activities.
     */
    private function getRecentActivities()
    {
        $activities = collect();

        // Recent user registrations
        $recentUsers = User::latest()->take(5)->get();
        foreach ($recentUsers as $user) {
            $activities->push([
                'type' => 'user_registered',
                'title' => 'New User Registration',
                'description' => $user->name . ' joined the platform',
                'time' => $user->created_at,
                'icon' => '👤',
                'color' => 'success'
            ]);
        }

        // Recent course enrollments
        $recentEnrollments = CourseEnrollment::with(['user', 'course'])
            ->latest()
            ->take(5)
            ->get();
        foreach ($recentEnrollments as $enrollment) {
            $activities->push([
                'type' => 'course_enrollment',
                'title' => 'Course Enrollment',
                'description' => $enrollment->user->name . ' enrolled in ' . $enrollment->course->title,
                'time' => $enrollment->enrolled_at,
                'icon' => '📚',
                'color' => 'primary'
            ]);
        }

        // Recent payments
        $recentPayments = Payment::with(['user', 'course'])
            ->where('status', 'completed')
            ->latest('completed_at')
            ->take(5)
            ->get();
        foreach ($recentPayments as $payment) {
            $activities->push([
                'type' => 'payment_completed',
                'title' => 'Payment Completed',
                'description' => $payment->user->name . ' paid $' . number_format($payment->amount, 2) . ' for ' . $payment->course->title,
                'time' => $payment->completed_at,
                'icon' => '💰',
                'color' => 'warning'
            ]);
        }

        return $activities->sortByDesc('time')->take(10)->values();
    }

    /**
     * Get revenue data for charts.
     */
    private function getRevenueData()
    {
        $last30Days = collect();

        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $revenue = Payment::where('status', 'completed')
                ->whereDate('completed_at', $date)
                ->sum('amount');

            $last30Days->push([
                'date' => $date->format('M j'),
                'revenue' => (float) $revenue
            ]);
        }

        return $last30Days;
    }

    /**
     * Get user growth data.
     */
    private function getUserGrowthData()
    {
        $last12Months = collect();

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $userCount = User::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            $last12Months->push([
                'month' => $date->format('M Y'),
                'users' => $userCount
            ]);
        }

        return $last12Months;
    }

    /**
     * Get top performing courses.
     */
    private function getTopCourses()
    {
        return Course::withCount(['students', 'reviews'])
            ->withAvg('reviews', 'rating')
            ->with('instructor')
            ->where('status', 'published')
            ->orderByDesc('students_count')
            ->take(5)
            ->get()
            ->map(function ($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'instructor' => $course->instructor->name,
                    'students' => $course->students_count,
                    'rating' => round($course->reviews_avg_rating ?? 0, 1),
                    'reviews' => $course->reviews_count,
                    'revenue' => Payment::where('course_id', $course->id)
                        ->where('status', 'completed')
                        ->sum('amount')
                ];
            });
    }

    /**
     * Calculate percentage change between two values.
     */
    private function calculatePercentageChange($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Calculate overall course completion rate.
     */
    private function calculateCompletionRate()
    {
        $totalEnrollments = CourseEnrollment::count();
        $completedEnrollments = CourseEnrollment::whereNotNull('completed_at')->count();

        if ($totalEnrollments == 0) {
            return 0;
        }

        return round(($completedEnrollments / $totalEnrollments) * 100, 1);
    }

    /**
     * Get system status information.
     */
    public function systemStatus()
    {
        $status = [
            'database' => $this->checkDatabaseConnection(),
            'storage' => $this->checkStorageSpace(),
            'cache' => $this->checkCacheConnection(),
            'queue' => $this->checkQueueConnection(),
        ];

        return response()->json($status);
    }

    /**
     * Check database connection.
     */
    private function checkDatabaseConnection()
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'healthy', 'message' => 'Database connection is working'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed'];
        }
    }

    /**
     * Check storage space.
     */
    private function checkStorageSpace()
    {
        $freeBytes = disk_free_space(storage_path());
        $totalBytes = disk_total_space(storage_path());
        $usedPercentage = (($totalBytes - $freeBytes) / $totalBytes) * 100;

        return [
            'status' => $usedPercentage > 90 ? 'warning' : 'healthy',
            'message' => 'Storage usage: ' . round($usedPercentage, 1) . '%',
            'free_space' => $this->formatBytes($freeBytes),
            'total_space' => $this->formatBytes($totalBytes)
        ];
    }

    /**
     * Check cache connection.
     */
    private function checkCacheConnection()
    {
        try {
            cache()->put('admin_health_check', 'test', 60);
            $value = cache()->get('admin_health_check');
            return [
                'status' => $value === 'test' ? 'healthy' : 'error',
                'message' => $value === 'test' ? 'Cache is working' : 'Cache test failed'
            ];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache connection failed'];
        }
    }

    /**
     * Check queue connection.
     */
    private function checkQueueConnection()
    {
        // This is a basic check - in production you'd want more sophisticated monitoring
        return ['status' => 'healthy', 'message' => 'Queue system is operational'];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
