<?php $__env->startSection('title', 'Contact Us'); ?>

<?php $__env->startSection('content'); ?>
<div class="contact-page">
    <div class="page-header">
        <h1>📞 Contact Us</h1>
        <p class="page-subtitle">Get in touch with The Real World team</p>
    </div>

    <div class="contact-container">
        <div class="contact-info">
            <h2>Get In Touch</h2>
            <p>Have questions about our courses, community, or platform? We're here to help!</p>
            
            <div class="contact-methods">
                <div class="contact-method">
                    <div class="method-icon">📧</div>
                    <div class="method-content">
                        <h3>Email Support</h3>
                        <p><EMAIL></p>
                        <span class="response-time">Response within 24 hours</span>
                    </div>
                </div>
                
                <div class="contact-method">
                    <div class="method-icon">💬</div>
                    <div class="method-content">
                        <h3>Live Chat</h3>
                        <p>Available for members</p>
                        <span class="response-time">Instant response</span>
                    </div>
                </div>
                
                <div class="contact-method">
                    <div class="method-icon">🏢</div>
                    <div class="method-content">
                        <h3>Business Inquiries</h3>
                        <p><EMAIL></p>
                        <span class="response-time">Response within 48 hours</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="contact-form-container">
            <h2>Send us a Message</h2>
            
            <form method="POST" action="<?php echo e(route('contact.submit')); ?>" class="contact-form">
                <?php echo csrf_field(); ?>
                
                <div class="form-group">
                    <label for="name" class="form-label">Full Name *</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-control" 
                           value="<?php echo e(old('name')); ?>" 
                           required>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           value="<?php echo e(old('email')); ?>" 
                           required>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="form-group">
                    <label for="subject" class="form-label">Subject *</label>
                    <select id="subject" name="subject" class="form-control" required>
                        <option value="">Select a subject</option>
                        <option value="general" <?php echo e(old('subject') == 'general' ? 'selected' : ''); ?>>General Inquiry</option>
                        <option value="courses" <?php echo e(old('subject') == 'courses' ? 'selected' : ''); ?>>Course Questions</option>
                        <option value="technical" <?php echo e(old('subject') == 'technical' ? 'selected' : ''); ?>>Technical Support</option>
                        <option value="billing" <?php echo e(old('subject') == 'billing' ? 'selected' : ''); ?>>Billing & Payments</option>
                        <option value="partnership" <?php echo e(old('subject') == 'partnership' ? 'selected' : ''); ?>>Partnership Opportunities</option>
                        <option value="feedback" <?php echo e(old('subject') == 'feedback' ? 'selected' : ''); ?>>Feedback & Suggestions</option>
                    </select>
                    <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="form-group">
                    <label for="message" class="form-label">Message *</label>
                    <textarea id="message" 
                              name="message" 
                              class="form-control" 
                              rows="6" 
                              placeholder="Tell us how we can help you..."
                              required><?php echo e(old('message')); ?></textarea>
                    <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <button type="submit" class="btn btn-primary submit-btn">
                    Send Message
                </button>
            </form>
        </div>
    </div>

    <div class="faq-section">
        <h2>Frequently Asked Questions</h2>
        
        <div class="faq-grid">
            <div class="faq-item">
                <h3>How do I access my courses?</h3>
                <p>Once you enroll in a course, you can access it through your dashboard. All course materials are available 24/7.</p>
            </div>
            
            <div class="faq-item">
                <h3>Can I get a refund?</h3>
                <p>We offer a 30-day money-back guarantee for all courses. See our refund policy for more details.</p>
            </div>
            
            <div class="faq-item">
                <h3>How do I join the community?</h3>
                <p>Community access is included with all course enrollments. You'll receive an invitation after purchase.</p>
            </div>
            
            <div class="faq-item">
                <h3>Are there live sessions?</h3>
                <p>Yes! We host regular live streams and Q&A sessions. Schedule is available in your member dashboard.</p>
            </div>
        </div>
    </div>
</div>

<style>
.contact-page {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin: 0;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 4rem;
}

.contact-info h2,
.contact-form-container h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.contact-info p {
    color: #64748b;
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
}

.method-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.method-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #1e293b;
}

.method-content p {
    font-size: 0.875rem;
    color: #475569;
    margin-bottom: 0.25rem;
}

.response-time {
    font-size: 0.75rem;
    color: #22c55e;
    font-weight: 500;
}

.contact-form {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

select.form-control {
    cursor: pointer;
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    display: block;
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.faq-section {
    margin-top: 4rem;
}

.faq-section h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #1e293b;
    text-align: center;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.faq-item {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
}

.faq-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1e293b;
}

.faq-item p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .contact-page {
        padding: 1rem;
    }
    
    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/pages/contact.blade.php ENDPATH**/ ?>