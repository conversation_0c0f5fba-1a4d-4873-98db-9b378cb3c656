#!/bin/bash

# The Real World - Laravel Deployment Script
# This script handles the deployment of The Real World LMS platform

set -e  # Exit on any error

echo "🚀 Starting deployment of The Real World LMS..."

# Configuration
APP_NAME="the-real-world-lms"
BACKUP_DIR="/var/backups/$APP_NAME"
LOG_FILE="/var/log/$APP_NAME-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# Check if running as correct user
check_user() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check PHP version
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    if ! php -v | grep -q "PHP 8"; then
        error "PHP 8.0+ is required. Current version: $PHP_VERSION"
    fi
    
    # Check Composer
    if ! command -v composer &> /dev/null; then
        error "Composer is not installed"
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    # Check NPM
    if ! command -v npm &> /dev/null; then
        error "NPM is not installed"
    fi
    
    log "✅ All requirements met"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory
    mkdir -p $BACKUP_DIR
    
    # Backup database
    if [ ! -z "$DB_DATABASE" ]; then
        mysqldump -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > $BACKUP_DIR/database_$(date +%Y%m%d_%H%M%S).sql
        log "✅ Database backup created"
    fi
    
    # Backup storage files
    if [ -d "storage/app" ]; then
        tar -czf $BACKUP_DIR/storage_$(date +%Y%m%d_%H%M%S).tar.gz storage/app
        log "✅ Storage backup created"
    fi
    
    # Backup .env file
    if [ -f ".env" ]; then
        cp .env $BACKUP_DIR/.env_$(date +%Y%m%d_%H%M%S)
        log "✅ Environment backup created"
    fi
}

# Update application code
update_code() {
    log "Updating application code..."
    
    # Pull latest changes
    git fetch origin
    git reset --hard origin/main
    
    log "✅ Code updated"
}

# Install dependencies
install_dependencies() {
    log "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    log "Installing Node.js dependencies..."
    npm ci --production
    
    log "✅ Dependencies installed"
}

# Build assets
build_assets() {
    log "Building frontend assets..."
    npm run build
    
    log "✅ Assets built"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Check if we need to run migrations
    if php artisan migrate:status | grep -q "Pending"; then
        php artisan migrate --force
        log "✅ Migrations completed"
    else
        log "✅ No pending migrations"
    fi
}

# Seed database if needed
seed_database() {
    log "Checking if database seeding is needed..."
    
    # Check if users table is empty (fresh installation)
    USER_COUNT=$(php artisan tinker --execute="echo App\Models\User::count();")
    
    if [ "$USER_COUNT" -eq "0" ]; then
        log "Fresh installation detected, seeding database..."
        php artisan db:seed --force
        log "✅ Database seeded"
    else
        log "✅ Database already has data, skipping seeding"
    fi
}

# Clear and cache application
optimize_application() {
    log "Optimizing application..."
    
    # Clear all caches
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    # Optimize for production
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    # Generate application key if needed
    if ! grep -q "APP_KEY=base64:" .env; then
        php artisan key:generate --force
    fi
    
    log "✅ Application optimized"
}

# Set proper permissions
set_permissions() {
    log "Setting file permissions..."
    
    # Set ownership
    sudo chown -R www-data:www-data storage bootstrap/cache
    
    # Set permissions
    chmod -R 775 storage bootstrap/cache
    
    log "✅ Permissions set"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Restart PHP-FPM
    sudo systemctl restart php8.2-fpm
    
    # Restart Nginx
    sudo systemctl restart nginx
    
    # Restart queue workers if using queues
    if systemctl is-active --quiet laravel-worker; then
        sudo systemctl restart laravel-worker
    fi
    
    log "✅ Services restarted"
}

# Run health checks
health_check() {
    log "Running health checks..."
    
    # Check if application is responding
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost)
    if [ "$HTTP_STATUS" != "200" ]; then
        error "Application health check failed. HTTP status: $HTTP_STATUS"
    fi
    
    # Check database connection
    php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        error "Database connection failed"
    fi
    
    # Check storage permissions
    if [ ! -w "storage/logs" ]; then
        error "Storage directory is not writable"
    fi
    
    log "✅ All health checks passed"
}

# Send deployment notification
send_notification() {
    log "Sending deployment notification..."
    
    # You can integrate with Slack, Discord, or email here
    # Example for Slack webhook:
    # curl -X POST -H 'Content-type: application/json' \
    #     --data '{"text":"🚀 The Real World LMS deployed successfully!"}' \
    #     $SLACK_WEBHOOK_URL
    
    log "✅ Notification sent"
}

# Main deployment function
deploy() {
    log "🚀 Starting deployment process..."
    
    # Load environment variables
    if [ -f ".env" ]; then
        source .env
    fi
    
    # Run deployment steps
    check_user
    check_requirements
    create_backup
    update_code
    install_dependencies
    build_assets
    run_migrations
    seed_database
    optimize_application
    set_permissions
    restart_services
    health_check
    send_notification
    
    log "🎉 Deployment completed successfully!"
    log "The Real World LMS is now live and ready to use!"
}

# Rollback function
rollback() {
    log "🔄 Starting rollback process..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t $BACKUP_DIR/database_*.sql | head -n1)
    
    if [ -z "$LATEST_BACKUP" ]; then
        error "No backup found for rollback"
    fi
    
    # Restore database
    mysql -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE < $LATEST_BACKUP
    
    # Restore storage if exists
    LATEST_STORAGE=$(ls -t $BACKUP_DIR/storage_*.tar.gz | head -n1)
    if [ ! -z "$LATEST_STORAGE" ]; then
        tar -xzf $LATEST_STORAGE
    fi
    
    # Clear caches
    php artisan cache:clear
    php artisan config:clear
    
    restart_services
    
    log "✅ Rollback completed"
}

# Script usage
usage() {
    echo "Usage: $0 {deploy|rollback|health-check}"
    echo ""
    echo "Commands:"
    echo "  deploy      - Deploy the application"
    echo "  rollback    - Rollback to previous version"
    echo "  health-check - Run health checks only"
    exit 1
}

# Main script logic
case "$1" in
    deploy)
        deploy
        ;;
    rollback)
        rollback
        ;;
    health-check)
        health_check
        ;;
    *)
        usage
        ;;
esac
