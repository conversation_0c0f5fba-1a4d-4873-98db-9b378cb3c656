<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Polyfill\Intl\Icu\DateFormat;

/**
 * Parser and formatter for year format.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
class YearTransformer extends Transformer
{
    public function format(\DateTime $dateTime, int $length): string
    {
        if (2 === $length) {
            return $dateTime->format('y');
        }

        return $this->padLeft($dateTime->format('Y'), $length);
    }

    public function getReverseMatchingRegExp(int $length): string
    {
        return 2 === $length ? '\d{2}' : '\d{1,4}';
    }

    public function extractDateOptions(string $matched, int $length): array
    {
        return [
            'year' => (int) $matched,
        ];
    }
}
