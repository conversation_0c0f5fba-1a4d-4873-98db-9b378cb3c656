<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'The Real World - Escape the Matrix')</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="@yield('description', 'Join The Real World and escape the matrix through education, community, and real-world skills. Learn from successful entrepreneurs and build your empire.')">
    <meta name="keywords" content="@yield('keywords', 'education, entrepreneurship, business, success, community, courses, mentorship, real world')">
    <meta name="author" content="The Real World">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'The Real World - Escape the Matrix')">
    <meta property="og:description" content="@yield('og_description', 'Join The Real World and escape the matrix through education and community')">
    <meta property="og:image" content="@yield('og_image', asset('assets/images/og-landing.jpg'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', 'The Real World - Escape the Matrix')">
    <meta name="twitter:description" content="@yield('twitter_description', 'Join The Real World and escape the matrix through education and community')">
    <meta name="twitter:image" content="@yield('twitter_image', asset('assets/images/twitter-landing.jpg'))">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Custom CSS -->
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/landing.css') }}" rel="stylesheet">
    
    @stack('styles')
</head>
<body class="landing-body">
    <!-- Landing Navigation -->
    <nav class="landing-nav">
        <div class="nav-container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="{{ route('home') }}" class="brand-link">
                        <span class="brand-icon">🌍</span>
                        <span class="brand-text">The Real World</span>
                    </a>
                </div>
                
                <div class="nav-links">
                    <a href="#features" class="nav-link">Features</a>
                    <a href="#courses" class="nav-link">Courses</a>
                    <a href="#community" class="nav-link">Community</a>
                    <a href="#testimonials" class="nav-link">Success Stories</a>
                    <a href="#pricing" class="nav-link">Pricing</a>
                </div>
                
                <div class="nav-actions">
                    @guest
                        <a href="{{ route('login') }}" class="nav-btn login-btn">Login</a>
                        <a href="{{ route('register') }}" class="nav-btn register-btn">Join Now</a>
                    @else
                        <a href="{{ route('dashboard') }}" class="nav-btn dashboard-btn">Dashboard</a>
                    @endguest
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button type="button" class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    <span class="hamburger"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu" style="display: none;">
            <div class="mobile-menu-content">
                <a href="#features" class="mobile-nav-link" onclick="closeMobileMenu()">Features</a>
                <a href="#courses" class="mobile-nav-link" onclick="closeMobileMenu()">Courses</a>
                <a href="#community" class="mobile-nav-link" onclick="closeMobileMenu()">Community</a>
                <a href="#testimonials" class="mobile-nav-link" onclick="closeMobileMenu()">Success Stories</a>
                <a href="#pricing" class="mobile-nav-link" onclick="closeMobileMenu()">Pricing</a>
                
                <div class="mobile-nav-actions">
                    @guest
                        <a href="{{ route('login') }}" class="mobile-nav-btn login-btn">Login</a>
                        <a href="{{ route('register') }}" class="mobile-nav-btn register-btn">Join Now</a>
                    @else
                        <a href="{{ route('dashboard') }}" class="mobile-nav-btn dashboard-btn">Dashboard</a>
                    @endguest
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="landing-main">
        @yield('content')
    </main>
    
    <!-- Landing Footer -->
    <footer class="landing-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section brand-section">
                    <div class="footer-brand">
                        <span class="footer-logo">🌍</span>
                        <h3>The Real World</h3>
                    </div>
                    <p class="footer-description">
                        Escape the matrix through education, community, and real-world skills. 
                        Join thousands of successful entrepreneurs building their empires.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link">📘</a>
                        <a href="#" class="social-link">🐦</a>
                        <a href="#" class="social-link">📸</a>
                        <a href="#" class="social-link">💼</a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Platform</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('courses.index') }}">Courses</a></li>
                        <li><a href="{{ route('communities.index') }}">Community</a></li>
                        <li><a href="{{ route('chat.index') }}">Live Chat</a></li>
                        <li><a href="{{ route('instructors.index') }}">Instructors</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('help') }}">Help Center</a></li>
                        <li><a href="{{ route('blog') }}">Blog</a></li>
                        <li><a href="{{ route('success-stories') }}">Success Stories</a></li>
                        <li><a href="{{ route('affiliate') }}">Affiliate Program</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('about') }}">About Us</a></li>
                        <li><a href="{{ route('careers') }}">Careers</a></li>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                        <li><a href="{{ route('press') }}">Press</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('privacy') }}">Privacy Policy</a></li>
                        <li><a href="{{ route('terms') }}">Terms of Service</a></li>
                        <li><a href="{{ route('cookies') }}">Cookie Policy</a></li>
                        <li><a href="{{ route('refund') }}">Refund Policy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; {{ date('Y') }} The Real World. All rights reserved.</p>
                    <div class="footer-bottom-links">
                        <span class="footer-stats">🔥 {{ number_format(50000) }}+ Active Members</span>
                        <span class="footer-stats">💰 ${{ number_format(10000000) }}+ Student Earnings</span>
                        <span class="footer-stats">🏆 {{ number_format(500) }}+ Success Stories</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button type="button" class="back-to-top" id="backToTop" onclick="scrollToTop()" style="display: none;">
        ↑
    </button>
    
    <!-- Custom JS -->
    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/landing.js') }}"></script>
    
    <script>
        // Mobile menu functionality
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            const isVisible = menu.style.display === 'block';
            menu.style.display = isVisible ? 'none' : 'block';
            document.body.classList.toggle('mobile-menu-open', !isVisible);
        }
        
        function closeMobileMenu() {
            document.getElementById('mobileMenu').style.display = 'none';
            document.body.classList.remove('mobile-menu-open');
        }
        
        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                        closeMobileMenu();
                    }
                });
            });
            
            // Back to top button
            window.addEventListener('scroll', function() {
                const backToTop = document.getElementById('backToTop');
                if (window.pageYOffset > 300) {
                    backToTop.style.display = 'block';
                } else {
                    backToTop.style.display = 'none';
                }
            });
        });
        
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobileMenu');
            const button = document.querySelector('.mobile-menu-btn');
            
            if (menu && button && !button.contains(event.target) && !menu.contains(event.target)) {
                closeMobileMenu();
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>
