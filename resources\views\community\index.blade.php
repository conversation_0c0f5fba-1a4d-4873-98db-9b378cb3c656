<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Community - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/community.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}">Courses</a></li>
                <li><a href="{{ route('communities.index') }}" class="active">Community</a></li>
                <li><a href="{{ route('chat.index') }}">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="community-main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <h1>Join The Brotherhood</h1>
                    <p>Connect with like-minded individuals on the path to success</p>
                </div>
                @can('create communities')
                    <div class="header-actions">
                        <a href="{{ route('communities.create') }}" class="btn btn-primary">
                            Create Community
                        </a>
                    </div>
                @endcan
            </div>

            <!-- Community Grid -->
            <div class="community-layout">
                <div class="community-content">
                    <!-- Filters -->
                    <div class="filters-section">
                        <form method="GET" action="{{ route('communities.index') }}" class="filters-form">
                            <div class="filter-group">
                                <input type="text" name="search" placeholder="Search communities..." 
                                       value="{{ request('search') }}" class="search-input">
                            </div>
                            
                            <div class="filter-group">
                                <select name="type" class="filter-select">
                                    <option value="">All Types</option>
                                    <option value="public" {{ request('type') == 'public' ? 'selected' : '' }}>
                                        Public
                                    </option>
                                    <option value="course-specific" {{ request('type') == 'course-specific' ? 'selected' : '' }}>
                                        Course Specific
                                    </option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="{{ route('communities.index') }}" class="btn btn-secondary">Clear</a>
                        </form>
                    </div>

                    <!-- Communities Grid -->
                    <div class="communities-grid">
                        @forelse($communities as $community)
                            <div class="community-card">
                                <div class="community-header">
                                    @if($community->banner)
                                        <img src="{{ Storage::url($community->banner) }}" alt="{{ $community->name }}" class="community-banner">
                                    @else
                                        <div class="community-banner-placeholder">
                                            <span class="community-icon">👥</span>
                                        </div>
                                    @endif
                                    
                                    <div class="community-type">
                                        <span class="type-badge type-{{ $community->type }}">
                                            {{ ucfirst(str_replace('-', ' ', $community->type)) }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="community-content">
                                    <h3 class="community-title">
                                        <a href="{{ route('communities.show', $community) }}">{{ $community->name }}</a>
                                    </h3>
                                    
                                    <p class="community-description">{{ Str::limit($community->description, 120) }}</p>
                                    
                                    @if($community->course)
                                        <div class="community-course">
                                            <span class="course-label">Course:</span>
                                            <a href="{{ route('courses.show', $community->course) }}" class="course-link">
                                                {{ $community->course->title }}
                                            </a>
                                        </div>
                                    @endif
                                    
                                    <div class="community-stats">
                                        <div class="stat">
                                            <span class="stat-icon">👥</span>
                                            <span class="stat-value">{{ $community->member_count }} members</span>
                                        </div>
                                        <div class="stat">
                                            <span class="stat-icon">💬</span>
                                            <span class="stat-value">{{ $community->post_count }} posts</span>
                                        </div>
                                    </div>
                                    
                                    <div class="community-creator">
                                        <span class="creator-label">Created by</span>
                                        <span class="creator-name">{{ $community->creator->name }}</span>
                                    </div>
                                </div>
                                
                                <div class="community-actions">
                                    <a href="{{ route('communities.show', $community) }}" class="btn btn-primary btn-block">
                                        View Community
                                    </a>
                                </div>
                            </div>
                        @empty
                            <div class="empty-state">
                                <div class="empty-icon">👥</div>
                                <h3>No communities found</h3>
                                <p>Be the first to create a community and start building connections</p>
                                @can('create communities')
                                    <a href="{{ route('communities.create') }}" class="btn btn-primary">
                                        Create First Community
                                    </a>
                                @endcan
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    @if($communities->hasPages())
                        <div class="pagination-wrapper">
                            {{ $communities->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="community-sidebar">
                    <!-- Popular Communities -->
                    <div class="sidebar-card">
                        <h4>Popular Communities</h4>
                        <div class="popular-communities">
                            @forelse($popularCommunities as $popular)
                                <div class="popular-item">
                                    <div class="popular-info">
                                        <h5><a href="{{ route('communities.show', $popular) }}">{{ $popular->name }}</a></h5>
                                        <span class="popular-stats">{{ $popular->member_count }} members</span>
                                    </div>
                                </div>
                            @empty
                                <p class="no-popular">No popular communities yet</p>
                            @endforelse
                        </div>
                    </div>

                    <!-- Community Guidelines -->
                    <div class="sidebar-card">
                        <h4>Community Guidelines</h4>
                        <ul class="guidelines-list">
                            <li>🤝 Respect all members</li>
                            <li>💡 Share valuable insights</li>
                            <li>🚫 No spam or self-promotion</li>
                            <li>🎯 Stay on topic</li>
                            <li>💪 Support each other's growth</li>
                        </ul>
                    </div>

                    <!-- Quick Stats -->
                    <div class="sidebar-card">
                        <h4>Community Stats</h4>
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <span class="stat-number">{{ $communities->total() }}</span>
                                <span class="stat-label">Total Communities</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-number">{{ $popularCommunities->sum('member_count') }}</span>
                                <span class="stat-label">Active Members</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-number">{{ $popularCommunities->sum('post_count') }}</span>
                                <span class="stat-label">Total Posts</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/community.js') }}"></script>
</body>
</html>
