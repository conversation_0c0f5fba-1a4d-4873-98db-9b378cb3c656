<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains performance optimization settings for The Real World
    | LMS platform. These settings help optimize database queries, caching,
    | and overall application performance.
    |
    */

    'cache' => [
        /*
        |--------------------------------------------------------------------------
        | Cache TTL Settings
        |--------------------------------------------------------------------------
        |
        | Time-to-live settings for various cached data in seconds.
        |
        */
        'courses' => [
            'list' => 3600, // 1 hour
            'details' => 1800, // 30 minutes
            'lessons' => 7200, // 2 hours
            'progress' => 300, // 5 minutes
        ],

        'users' => [
            'profile' => 1800, // 30 minutes
            'permissions' => 3600, // 1 hour
            'enrollments' => 900, // 15 minutes
        ],

        'communities' => [
            'list' => 1800, // 30 minutes
            'posts' => 600, // 10 minutes
            'members' => 900, // 15 minutes
        ],

        'payments' => [
            'history' => 3600, // 1 hour
            'analytics' => 7200, // 2 hours
        ],

        'admin' => [
            'dashboard' => 300, // 5 minutes
            'analytics' => 1800, // 30 minutes
            'reports' => 3600, // 1 hour
        ],
    ],

    'database' => [
        /*
        |--------------------------------------------------------------------------
        | Query Optimization
        |--------------------------------------------------------------------------
        |
        | Settings for database query optimization and performance monitoring.
        |
        */
        'slow_query_threshold' => 2000, // milliseconds
        'enable_query_log' => env('DB_QUERY_LOG', false),
        'max_query_time' => 5000, // milliseconds

        'pagination' => [
            'default_per_page' => 20,
            'max_per_page' => 100,
            'courses_per_page' => 12,
            'posts_per_page' => 15,
            'messages_per_page' => 50,
        ],

        'eager_loading' => [
            'courses' => ['instructor', 'category', 'reviews'],
            'users' => ['roles', 'enrollments'],
            'communities' => ['creator', 'members'],
            'payments' => ['user', 'course'],
        ],
    ],

    'assets' => [
        /*
        |--------------------------------------------------------------------------
        | Asset Optimization
        |--------------------------------------------------------------------------
        |
        | Settings for CSS, JS, and image optimization.
        |
        */
        'minify_css' => env('MINIFY_CSS', true),
        'minify_js' => env('MINIFY_JS', true),
        'compress_images' => env('COMPRESS_IMAGES', true),
        'use_cdn' => env('USE_CDN', false),
        'cdn_url' => env('CDN_URL', ''),

        'image_optimization' => [
            'quality' => 85,
            'max_width' => 1920,
            'max_height' => 1080,
            'thumbnail_size' => 300,
        ],
    ],

    'api' => [
        /*
        |--------------------------------------------------------------------------
        | API Performance
        |--------------------------------------------------------------------------
        |
        | Settings for API rate limiting and response optimization.
        |
        */
        'rate_limit' => [
            'per_minute' => 60,
            'per_hour' => 1000,
            'per_day' => 10000,
        ],

        'response_cache' => [
            'enabled' => true,
            'ttl' => 300, // 5 minutes
        ],

        'compression' => [
            'enabled' => true,
            'level' => 6, // gzip compression level
        ],
    ],

    'search' => [
        /*
        |--------------------------------------------------------------------------
        | Search Performance
        |--------------------------------------------------------------------------
        |
        | Settings for search functionality optimization.
        |
        */
        'cache_results' => true,
        'cache_ttl' => 1800, // 30 minutes
        'max_results' => 50,
        'min_query_length' => 2,

        'indexing' => [
            'courses' => ['title', 'description', 'tags'],
            'users' => ['name', 'email'],
            'communities' => ['name', 'description'],
            'posts' => ['title', 'content'],
        ],
    ],

    'monitoring' => [
        /*
        |--------------------------------------------------------------------------
        | Performance Monitoring
        |--------------------------------------------------------------------------
        |
        | Settings for performance monitoring and alerting.
        |
        */
        'enabled' => env('PERFORMANCE_MONITORING', true),
        'log_slow_requests' => true,
        'slow_request_threshold' => 3000, // milliseconds

        'memory' => [
            'limit' => '512M',
            'warning_threshold' => '400M',
        ],

        'alerts' => [
            'email' => env('ADMIN_EMAIL'),
            'slack_webhook' => env('SLACK_WEBHOOK_URL'),
        ],
    ],

    'optimization' => [
        /*
        |--------------------------------------------------------------------------
        | General Optimization
        |--------------------------------------------------------------------------
        |
        | General performance optimization settings.
        |
        */
        'enable_opcache' => true,
        'enable_redis_cache' => true,
        'enable_session_cache' => true,
        'enable_route_cache' => true,
        'enable_config_cache' => true,
        'enable_view_cache' => true,

        'lazy_loading' => [
            'images' => true,
            'videos' => true,
            'iframes' => true,
        ],

        'preloading' => [
            'critical_css' => true,
            'fonts' => true,
            'key_resources' => true,
        ],
    ],

    'security' => [
        /*
        |--------------------------------------------------------------------------
        | Security Performance
        |--------------------------------------------------------------------------
        |
        | Security-related performance settings.
        |
        */
        'csrf_token_cache' => true,
        'session_encryption' => true,
        'secure_headers' => true,

        'rate_limiting' => [
            'login_attempts' => 5,
            'password_reset' => 3,
            'api_calls' => 100,
        ],
    ],

    'features' => [
        /*
        |--------------------------------------------------------------------------
        | Feature Flags
        |--------------------------------------------------------------------------
        |
        | Enable/disable features for performance testing.
        |
        */
        'real_time_chat' => env('FEATURE_REAL_TIME_CHAT', true),
        'live_notifications' => env('FEATURE_LIVE_NOTIFICATIONS', true),
        'advanced_search' => env('FEATURE_ADVANCED_SEARCH', true),
        'analytics_tracking' => env('FEATURE_ANALYTICS', true),
        'video_streaming' => env('FEATURE_VIDEO_STREAMING', true),
    ],

    'cleanup' => [
        /*
        |--------------------------------------------------------------------------
        | Data Cleanup
        |--------------------------------------------------------------------------
        |
        | Settings for automatic data cleanup and maintenance.
        |
        */
        'old_sessions' => 30, // days
        'old_logs' => 90, // days
        'old_notifications' => 60, // days
        'failed_jobs' => 7, // days
        'temp_files' => 1, // days

        'schedule' => [
            'daily_cleanup' => '02:00',
            'weekly_optimization' => 'sunday 03:00',
            'monthly_reports' => 'first sunday 04:00',
        ],
    ],
];
