@extends('layouts.app')

@section('title', 'Course Management - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}" class="active">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>Course Management</h1>
                    <p>Manage all courses on the platform</p>
                </div>
                <div class="header-actions">
                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
                        ➕ Add New Course
                    </a>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-info">
                        <h3>{{ $totalCourses }}</h3>
                        <p>Total Courses</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-info">
                        <h3>{{ $publishedCourses }}</h3>
                        <p>Published</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📝</div>
                    <div class="stat-info">
                        <h3>{{ $draftCourses }}</h3>
                        <p>Drafts</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <h3>{{ $totalEnrollments }}</h3>
                        <p>Total Enrollments</p>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" action="{{ route('admin.courses.index') }}" class="filters-form">
                    <div class="filter-group">
                        <input type="text" 
                               name="search" 
                               placeholder="Search courses..." 
                               value="{{ request('search') }}"
                               class="filter-input">
                    </div>
                    
                    <div class="filter-group">
                        <select name="status" class="filter-select">
                            <option value="">All Status</option>
                            <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                            <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="archived" {{ request('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select name="instructor" class="filter-select">
                            <option value="">All Instructors</option>
                            @foreach($instructors as $instructor)
                                <option value="{{ $instructor->id }}" {{ request('instructor') == $instructor->id ? 'selected' : '' }}>
                                    {{ $instructor->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="{{ route('admin.courses.index') }}" class="btn btn-outline">Clear</a>
                </form>
            </div>

            <!-- Courses Table -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Courses ({{ $courses->total() }})</h3>
                    <div class="table-actions">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="exportCourses()">
                            📊 Export
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="bulkActions()">
                            ⚡ Bulk Actions
                        </button>
                    </div>
                </div>
                
                <div class="table-wrapper">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>Course</th>
                                <th>Instructor</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Price</th>
                                <th>Students</th>
                                <th>Rating</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($courses as $course)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="course_ids[]" value="{{ $course->id }}" class="course-checkbox">
                                    </td>
                                    <td>
                                        <div class="course-info">
                                            @if($course->thumbnail)
                                                <img src="{{ Storage::url($course->thumbnail) }}" alt="{{ $course->title }}" class="course-thumbnail">
                                            @else
                                                <div class="course-placeholder">📚</div>
                                            @endif
                                            <div class="course-details">
                                                <h4>{{ $course->title }}</h4>
                                                <p>{{ Str::limit($course->short_description, 60) }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="instructor-info">
                                            <span class="instructor-name">{{ $course->instructor->name }}</span>
                                            <span class="instructor-email">{{ $course->instructor->email }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="category-badge">{{ $course->category->name }}</span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ $course->status }}">
                                            @if($course->status === 'published')
                                                ✅ Published
                                            @elseif($course->status === 'draft')
                                                📝 Draft
                                            @else
                                                📦 Archived
                                            @endif
                                        </span>
                                    </td>
                                    <td>
                                        <span class="price">${{ number_format($course->price, 2) }}</span>
                                    </td>
                                    <td>
                                        <span class="student-count">{{ $course->enrollments_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="rating">
                                            <span class="rating-stars">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= ($course->rating ?? 0))
                                                        ⭐
                                                    @else
                                                        ☆
                                                    @endif
                                                @endfor
                                            </span>
                                            <span class="rating-value">{{ number_format($course->rating ?? 0, 1) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="date">{{ $course->created_at->format('M j, Y') }}</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.courses.show', $course) }}" class="btn btn-sm btn-info" title="View">
                                                👁️
                                            </a>
                                            <a href="{{ route('admin.courses.edit', $course) }}" class="btn btn-sm btn-warning" title="Edit">
                                                ✏️
                                            </a>
                                            <a href="{{ route('courses.show', $course) }}" class="btn btn-sm btn-secondary" title="Preview" target="_blank">
                                                🔗
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteCourse({{ $course->id }})" title="Delete">
                                                🗑️
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="no-data">
                                        <div class="no-data-message">
                                            <div class="no-data-icon">📚</div>
                                            <h3>No courses found</h3>
                                            <p>No courses match your current filters.</p>
                                            <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">Create First Course</a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($courses->hasPages())
                    <div class="pagination-wrapper">
                        {{ $courses->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </main>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-info h3 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.filters-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filters-form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-input, .filter-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.filter-input::placeholder {
    color: #a0a0a0;
}

.table-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.table-wrapper {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-table th {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.course-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.course-thumbnail {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 8px;
}

.course-placeholder {
    width: 60px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.course-details h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.course-details p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.instructor-info {
    display: flex;
    flex-direction: column;
}

.instructor-name {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.instructor-email {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-published {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-draft {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-archived {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.price {
    color: #ffffff;
    font-weight: 600;
}

.student-count {
    color: #ffffff;
    font-weight: 500;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    font-size: 0.75rem;
}

.rating-value {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.date {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.no-data {
    text-align: center;
    padding: 3rem;
}

.no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.no-data-icon {
    font-size: 4rem;
    opacity: 0.5;
}

.no-data-message h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.no-data-message p {
    color: #a0a0a0;
    margin: 0;
}

@media (max-width: 768px) {
    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
}
</style>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.course-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function deleteCourse(courseId) {
    if (confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
        // Implementation for course deletion
        console.log('Delete course:', courseId);
    }
}

function exportCourses() {
    // Implementation for course export
    console.log('Export courses');
}

function bulkActions() {
    const selectedCourses = document.querySelectorAll('.course-checkbox:checked');
    if (selectedCourses.length === 0) {
        alert('Please select at least one course.');
        return;
    }
    
    // Implementation for bulk actions
    console.log('Bulk actions for', selectedCourses.length, 'courses');
}
</script>
@endsection
