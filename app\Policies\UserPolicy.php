<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class UserPolicy
{
    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view users');
    }

    /**
     * Determine whether the user can view the user.
     */
    public function view(User $user, User $model): bool
    {
        // Users can view their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Users with view permission can view other users
        return $user->can('view users');
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        return $user->can('create users');
    }

    /**
     * Determine whether the user can update the user.
     */
    public function update(User $user, User $model): bool
    {
        // Users can update their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Users with edit permission can edit other users
        return $user->can('edit users');
    }

    /**
     * Determine whether the user can delete the user.
     */
    public function delete(User $user, User $model): bool
    {
        // Users cannot delete themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Super admins cannot be deleted by regular admins
        if ($model->hasRole('super-admin') && !$user->hasRole('super-admin')) {
            return false;
        }

        return $user->can('delete users');
    }

    /**
     * Determine whether the user can restore the user.
     */
    public function restore(User $user, User $model): bool
    {
        return $user->can('delete users') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can permanently delete the user.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can ban other users.
     */
    public function ban(User $user, User $model): bool
    {
        // Users cannot ban themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Super admins cannot be banned
        if ($model->hasRole('super-admin')) {
            return false;
        }

        // Admins cannot ban other admins unless they are super admin
        if ($model->hasRole('admin') && !$user->hasRole('super-admin')) {
            return false;
        }

        return $user->can('ban users');
    }

    /**
     * Determine whether the user can unban other users.
     */
    public function unban(User $user, User $model): bool
    {
        return $user->can('unban users');
    }

    /**
     * Determine whether the user can impersonate other users.
     */
    public function impersonate(User $user, User $model): bool
    {
        // Users cannot impersonate themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Super admins cannot be impersonated
        if ($model->hasRole('super-admin')) {
            return false;
        }

        // Only super admins can impersonate admins
        if ($model->hasRole('admin') && !$user->hasRole('super-admin')) {
            return false;
        }

        return $user->can('impersonate users');
    }

    /**
     * Determine whether the user can manage roles for other users.
     */
    public function manageRoles(User $user, User $model): bool
    {
        // Users cannot manage their own roles
        if ($user->id === $model->id) {
            return false;
        }

        // Only super admins can manage super admin roles
        if ($model->hasRole('super-admin') && !$user->hasRole('super-admin')) {
            return false;
        }

        return $user->can('manage roles');
    }

    /**
     * Determine whether the user can view user analytics.
     */
    public function viewAnalytics(User $user, User $model): bool
    {
        // Users can view their own analytics
        if ($user->id === $model->id) {
            return true;
        }

        return $user->can('view user analytics');
    }

    /**
     * Determine whether the user can send messages to other users.
     */
    public function message(User $user, User $model): bool
    {
        // Users cannot message themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Check if the target user allows messages
        if (isset($model->settings['allow_messages']) && !$model->settings['allow_messages']) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can follow other users.
     */
    public function follow(User $user, User $model): bool
    {
        // Users cannot follow themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Check if already following
        if ($user->following()->where('followed_id', $model->id)->exists()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can unfollow other users.
     */
    public function unfollow(User $user, User $model): bool
    {
        // Users cannot unfollow themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Check if currently following
        return $user->following()->where('followed_id', $model->id)->exists();
    }
}
