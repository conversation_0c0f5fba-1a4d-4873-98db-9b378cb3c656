@extends('layouts.app')

@section('title', 'Verify Email')

@section('content')
<div class="verify-container">
    <div class="verify-card">
        <div class="verify-header">
            <div class="verify-icon">📧</div>
            <h1>Verify Your Email Address</h1>
            <p>We've sent a verification link to <strong>{{ Auth::user()->email }}</strong></p>
        </div>

        @if (session('status') == 'verification-link-sent')
            <div class="alert alert-success">
                <div class="alert-icon">✅</div>
                <div class="alert-content">
                    <h4>Verification Link Sent!</h4>
                    <p>A new verification link has been sent to your email address.</p>
                </div>
            </div>
        @endif

        <div class="verify-content">
            <div class="verify-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Check Your Email</h3>
                        <p>Look for an email from The Real World in your inbox</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Click the Link</h3>
                        <p>Click the verification link in the email to verify your account</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Start Learning</h3>
                        <p>Once verified, you'll have full access to all platform features</p>
                    </div>
                </div>
            </div>

            <div class="verify-actions">
                <form method="POST" action="{{ route('verification.send') }}">
                    @csrf
                    <button type="submit" class="btn btn-primary">
                        🔄 Resend Verification Email
                    </button>
                </form>
                
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="btn btn-secondary">
                        🚪 Logout
                    </button>
                </form>
            </div>
        </div>

        <div class="verify-help">
            <h3>Didn't receive the email?</h3>
            <div class="help-items">
                <div class="help-item">
                    <div class="help-icon">📁</div>
                    <div class="help-text">
                        <h4>Check Your Spam Folder</h4>
                        <p>Sometimes verification emails end up in spam or junk folders</p>
                    </div>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">⏰</div>
                    <div class="help-text">
                        <h4>Wait a Few Minutes</h4>
                        <p>Email delivery can sometimes take a few minutes</p>
                    </div>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">✉️</div>
                    <div class="help-text">
                        <h4>Check Email Address</h4>
                        <p>Make sure {{ Auth::user()->email }} is correct</p>
                    </div>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">🆘</div>
                    <div class="help-text">
                        <h4>Contact Support</h4>
                        <p>Still having trouble? Email us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="verify-benefits">
        <h2>Why Verify Your Email?</h2>
        <div class="benefits-grid">
            <div class="benefit-card">
                <div class="benefit-icon">🔒</div>
                <h3>Account Security</h3>
                <p>Email verification helps protect your account from unauthorized access and ensures you can recover your password if needed.</p>
            </div>
            
            <div class="benefit-card">
                <div class="benefit-icon">📧</div>
                <h3>Important Updates</h3>
                <p>Receive course updates, new lesson notifications, and important announcements about your learning journey.</p>
            </div>
            
            <div class="benefit-card">
                <div class="benefit-icon">🎓</div>
                <h3>Full Access</h3>
                <p>Unlock all platform features including course enrollment, community participation, and progress tracking.</p>
            </div>
            
            <div class="benefit-card">
                <div class="benefit-icon">💰</div>
                <h3>Payment Security</h3>
                <p>Verified email is required for secure payment processing and purchase confirmations.</p>
            </div>
        </div>
    </div>
</div>

<style>
.verify-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 4rem;
}

.verify-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 500px;
}

.verify-header {
    text-align: center;
    margin-bottom: 2rem;
}

.verify-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.verify-header h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.verify-header p {
    color: #a0a0a0;
    font-size: 1rem;
    line-height: 1.5;
}

.verify-header strong {
    color: #3b82f6;
}

.alert {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.alert-icon {
    font-size: 1.5rem;
}

.alert-content h4 {
    color: #22c55e;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-content p {
    color: #22c55e;
    font-size: 0.875rem;
    margin: 0;
}

.verify-steps {
    margin-bottom: 2rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.step-content h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.step-content p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.verify-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.verify-actions form {
    flex: 1;
}

.btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.verify-help {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.verify-help h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.help-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.help-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.help-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.help-text h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.help-text p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.help-text a {
    color: #3b82f6;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

.verify-benefits {
    max-width: 400px;
}

.verify-benefits h2 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

.benefits-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.benefit-card h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.benefit-card p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

@media (max-width: 768px) {
    .verify-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .verify-card {
        padding: 2rem;
    }
    
    .verify-actions {
        flex-direction: column;
    }
    
    .verify-benefits {
        max-width: 100%;
    }
}
</style>

<script>
// Auto-refresh page every 30 seconds to check if email was verified
setInterval(function() {
    fetch('{{ route('verification.check') }}', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.verified) {
            window.location.href = '{{ route('dashboard') }}';
        }
    })
    .catch(error => {
        console.log('Verification check failed:', error);
    });
}, 30000);

// Show success message when resend button is clicked
document.querySelector('form[action="{{ route('verification.send') }}"]').addEventListener('submit', function() {
    setTimeout(function() {
        alert('Verification email sent! Please check your inbox.');
    }, 100);
});
</script>
@endsection
