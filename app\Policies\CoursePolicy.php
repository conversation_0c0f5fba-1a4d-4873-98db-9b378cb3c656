<?php

namespace App\Policies;

use App\Models\Course;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CoursePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view courses
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Course $course): bool
    {
        // Users can view published courses or their own courses
        return $course->status === 'published' ||
               $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole(['instructor', 'admin', 'super-admin']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Course $course): bool
    {
        return $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Course $course): bool
    {
        return $user->hasRole(['super-admin']);
    }

    /**
     * Determine whether the user can enroll in the course.
     */
    public function enroll(User $user, Course $course): bool
    {
        // Users cannot enroll in their own courses
        if ($course->instructor_id === $user->id) {
            return false;
        }

        // Check if course is published and has available spots
        if ($course->status !== 'published') {
            return false;
        }

        if ($course->max_students && $course->students_count >= $course->max_students) {
            return false;
        }

        // Check if user is already enrolled
        return !$user->enrolledCourses()->where('course_id', $course->id)->exists();
    }

    /**
     * Determine whether the user can manage course content.
     */
    public function manageContent(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can view course analytics.
     */
    public function viewAnalytics(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can publish/unpublish the course.
     */
    public function publish(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }
}
