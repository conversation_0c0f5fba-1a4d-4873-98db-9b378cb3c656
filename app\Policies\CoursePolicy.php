<?php

namespace App\Policies;

use App\Models\Course;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CoursePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view courses
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Course $course): bool
    {
        // Users can view published courses or their own courses
        return $course->status === 'published' ||
               $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create courses');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Course $course): bool
    {
        // Instructors can edit their own courses
        if ($course->instructor_id === $user->id && $user->can('edit courses')) {
            return true;
        }

        // Admins can edit all courses
        return $user->can('edit courses') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Course $course): bool
    {
        // Only admins can delete courses, or instructors can delete their own unpublished courses
        if ($user->hasAnyRole(['admin', 'super-admin']) && $user->can('delete courses')) {
            return true;
        }

        // Instructors can delete their own unpublished courses
        return $course->instructor_id === $user->id
            && $course->status !== 'published'
            && $user->can('delete courses');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Course $course): bool
    {
        return $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Course $course): bool
    {
        return $user->hasRole(['super-admin']);
    }

    /**
     * Determine whether the user can enroll in the course.
     */
    public function enroll(User $user, Course $course): bool
    {
        // Users cannot enroll in their own courses
        if ($course->instructor_id === $user->id) {
            return false;
        }

        // Check if course is published and has available spots
        if ($course->status !== 'published') {
            return false;
        }

        if ($course->max_students && $course->students_count >= $course->max_students) {
            return false;
        }

        // Check if user is already enrolled
        return !$user->enrolledCourses()->where('course_id', $course->id)->exists();
    }

    /**
     * Determine whether the user can manage course content.
     */
    public function manageContent(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can view course analytics.
     */
    public function viewAnalytics(User $user, Course $course): bool
    {
        return $course->instructor_id === $user->id ||
               $user->hasRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can publish/unpublish the course.
     */
    public function publish(User $user, Course $course): bool
    {
        // Instructors can publish their own courses
        if ($course->instructor_id === $user->id && $user->can('publish courses')) {
            return true;
        }

        // Admins can publish any course
        return $user->can('publish courses') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can approve the course.
     */
    public function approve(User $user, Course $course): bool
    {
        return $user->can('approve courses') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can reject the course.
     */
    public function reject(User $user, Course $course): bool
    {
        return $user->can('reject courses') && $user->hasAnyRole(['admin', 'super-admin']);
    }

    /**
     * Determine whether the user can manage course enrollments.
     */
    public function manageEnrollments(User $user, Course $course): bool
    {
        // Instructors can manage enrollments for their own courses
        if ($course->instructor_id === $user->id && $user->can('manage course enrollments')) {
            return true;
        }

        // Admins can manage enrollments for all courses
        return $user->can('manage course enrollments') && $user->hasAnyRole(['admin', 'super-admin']);
    }
}
