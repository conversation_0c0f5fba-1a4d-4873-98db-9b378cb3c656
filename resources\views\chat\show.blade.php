<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $chatRoom->name }} - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/chat.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}">Courses</a></li>
                <li><a href="{{ route('communities.index') }}">Community</a></li>
                <li><a href="{{ route('chat.index') }}" class="active">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="chat-main">
        <div class="container">
            <!-- Back to Chat Rooms -->
            <div class="chat-breadcrumb">
                <a href="{{ route('chat.index') }}" class="breadcrumb-link">← Back to Chat Rooms</a>
            </div>

            <!-- Chat Container -->
            <div class="chat-container" data-room-id="{{ $chatRoom->id }}">
                <!-- Chat Area -->
                <div class="chat-area">
                    <!-- Chat Header -->
                    <div class="chat-header">
                        <div class="chat-info">
                            <h2 class="chat-title">{{ $chatRoom->name }}</h2>
                            <p class="chat-subtitle">
                                {{ $chatRoom->description }}
                                @if($chatRoom->course)
                                    • Course: {{ $chatRoom->course->title }}
                                @endif
                            </p>
                        </div>
                        <div class="chat-actions">
                            <span class="online-count">
                                <span class="online-indicator"></span>
                                {{ $chatRoom->member_count }} members
                            </span>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="chat-messages" id="chat-messages">
                        @forelse($messages as $message)
                            <div class="message {{ $message->user_id === Auth::id() ? 'own-message' : '' }}" 
                                 data-message-id="{{ $message->id }}"
                                 data-time="{{ $message->created_at->timestamp }}">
                                <div class="message-avatar">
                                    {{ substr($message->user->name, 0, 1) }}
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="message-author">{{ $message->user->name }}</span>
                                        <span class="message-time">{{ $message->created_at ? $message->created_at->format('H:i') : '--:--' }}</span>
                                    </div>
                                    <div class="message-text">{{ $message->message }}</div>
                                    
                                    @if($message->attachments)
                                        <div class="message-attachments">
                                            @foreach($message->attachments as $attachment)
                                                <div class="attachment">
                                                    <a href="{{ Storage::url($attachment['path']) }}" target="_blank">
                                                        📎 {{ $attachment['name'] }}
                                                    </a>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                    
                                    @if($message->reply_to_id)
                                        <div class="message-reply-to">
                                            Replying to: {{ $message->replyTo->message ?? 'Deleted message' }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="empty-messages">
                                <div class="empty-icon">💬</div>
                                <h3>No messages yet</h3>
                                <p>Be the first to start the conversation!</p>
                            </div>
                        @endforelse
                    </div>

                    <!-- Chat Input -->
                    <div class="chat-input">
                        <form id="message-form" class="input-form">
                            @csrf
                            <div class="input-group">
                                <textarea id="message-input" 
                                         name="message" 
                                         placeholder="Type your message..." 
                                         class="message-input"
                                         rows="1"
                                         maxlength="2000"
                                         required></textarea>
                                <div class="input-actions">
                                    <label class="file-upload-btn" title="Attach file">
                                        📎
                                        <input type="file" name="attachments[]" multiple style="display: none;">
                                    </label>
                                    <button type="submit" id="send-button" class="send-button" disabled>
                                        Send
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Chat Sidebar -->
                <div class="chat-sidebar">
                    <!-- Online Members -->
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">Online Members</h4>
                        <div class="online-members" id="online-members">
                            @foreach($chatRoom->members()->take(10)->get() as $member)
                                <div class="online-member">
                                    <div class="member-avatar">{{ substr($member->name, 0, 1) }}</div>
                                    <span class="member-name">{{ $member->name }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Chat Info -->
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">Chat Info</h4>
                        <div class="chat-info-details">
                            <div class="info-item">
                                <span class="info-label">Created:</span>
                                <span class="info-value">{{ $chatRoom->created_at ? $chatRoom->created_at->format('M j, Y') : 'Unknown' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Members:</span>
                                <span class="info-value">{{ $chatRoom->member_count }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Messages:</span>
                                <span class="info-value">{{ $chatRoom->message_count }}</span>
                            </div>
                            @if($chatRoom->course)
                                <div class="info-item">
                                    <span class="info-label">Course:</span>
                                    <a href="{{ route('courses.show', $chatRoom->course) }}" class="info-link">
                                        {{ $chatRoom->course->title }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Chat Rules -->
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">Chat Rules</h4>
                        <ul class="chat-rules">
                            <li>🤝 Be respectful to all members</li>
                            <li>💡 Share valuable insights</li>
                            <li>🚫 No spam or off-topic content</li>
                            <li>🎯 Stay focused on success</li>
                        </ul>
                    </div>

                    <!-- Quick Actions -->
                    <div class="sidebar-section">
                        <h4 class="sidebar-title">Actions</h4>
                        <div class="quick-actions">
                            @if($isMember)
                                <form method="POST" action="{{ route('chat.leave', $chatRoom) }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-secondary btn-sm btn-block"
                                            onclick="return confirm('Are you sure you want to leave this chat room?')">
                                        Leave Chat
                                    </button>
                                </form>
                            @else
                                <form method="POST" action="{{ route('chat.join', $chatRoom) }}">
                                    @csrf
                                    <button type="submit" class="btn btn-primary btn-sm btn-block">
                                        Join Chat
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/chat.js') }}"></script>
    <script>
        // Set current user ID for chat functionality
        window.currentUserId = {{ Auth::id() }};
        
        // Initialize chat room
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-scroll to bottom
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Focus on message input
            document.getElementById('message-input').focus();
            
            // Start real-time updates
            startRealTimeUpdates();
        });
        
        function startRealTimeUpdates() {
            // Poll for new messages every 3 seconds
            setInterval(() => {
                loadNewMessages();
            }, 3000);
            
            // Update online members every 30 seconds
            setInterval(() => {
                updateOnlineMembers();
            }, 30000);
        }
        
        function loadNewMessages() {
            const messagesContainer = document.getElementById('chat-messages');
            const lastMessage = messagesContainer.querySelector('.message:last-child');
            const lastMessageTime = lastMessage ? lastMessage.getAttribute('data-time') : null;
            
            fetch(`{{ route('chat.messages.index', $chatRoom) }}?since=${lastMessageTime || ''}`)
                .then(response => response.json())
                .then(data => {
                    if (data.messages && data.messages.length > 0) {
                        data.messages.forEach(message => {
                            addMessageToChat(message);
                        });
                        
                        // Auto-scroll if user is near bottom
                        const isNearBottom = messagesContainer.scrollTop + messagesContainer.clientHeight >= messagesContainer.scrollHeight - 100;
                        if (isNearBottom) {
                            messagesContainer.scrollTop = messagesContainer.scrollHeight;
                        }
                    }
                })
                .catch(error => console.error('Error loading messages:', error));
        }
        
        function updateOnlineMembers() {
            fetch(`{{ route('chat.show', $chatRoom) }}/members`)
                .then(response => response.json())
                .then(data => {
                    if (data.members) {
                        const onlineMembersContainer = document.getElementById('online-members');
                        onlineMembersContainer.innerHTML = '';
                        
                        data.members.forEach(member => {
                            const memberElement = document.createElement('div');
                            memberElement.className = 'online-member';
                            memberElement.innerHTML = `
                                <div class="member-avatar">${member.name.charAt(0)}</div>
                                <span class="member-name">${member.name}</span>
                            `;
                            onlineMembersContainer.appendChild(memberElement);
                        });
                    }
                })
                .catch(error => console.error('Error updating members:', error));
        }
        
        function addMessageToChat(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.user_id === window.currentUserId ? 'own-message' : ''}`;
            messageElement.setAttribute('data-message-id', message.id);
            messageElement.setAttribute('data-time', Math.floor(new Date(message.created_at).getTime() / 1000));
            
            const userInitials = message.user.name.split(' ').map(n => n[0]).join('').toUpperCase();
            
            messageElement.innerHTML = `
                <div class="message-avatar">${userInitials}</div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">${message.user.name}</span>
                        <span class="message-time">${new Date(message.created_at).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit'})}</span>
                    </div>
                    <div class="message-text">${message.message}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageElement);
            
            // Animate in
            setTimeout(() => {
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateY(0)';
            }, 10);
        }
    </script>
</body>
</html>
