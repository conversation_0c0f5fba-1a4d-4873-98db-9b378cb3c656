@extends('layouts.app')

@section('title', 'Notifications')

@section('content')
<div class="notifications-page">
    <!-- Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>🔔 Notifications</h1>
                <p>Stay updated with your latest activities and messages</p>
            </div>
            <div class="header-actions">
                @if($unreadCount > 0)
                    <button type="button" class="btn btn-outline" onclick="markAllAsRead()">
                        Mark All as Read ({{ $unreadCount }})
                    </button>
                @endif
                <button type="button" class="btn btn-secondary" onclick="refreshNotifications()">
                    🔄 Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Stats -->
    <div class="notification-stats">
        <div class="stat-card">
            <div class="stat-icon">📬</div>
            <div class="stat-content">
                <h3>{{ $notifications->total() }}</h3>
                <p>Total Notifications</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">📭</div>
            <div class="stat-content">
                <h3>{{ $unreadCount }}</h3>
                <p>Unread</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3>{{ $notifications->total() - $unreadCount }}</h3>
                <p>Read</p>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="notifications-container">
        @if($notifications->count() > 0)
            <div class="notifications-list">
                @foreach($notifications as $notification)
                    <div class="notification-item {{ $notification->read_at ? 'read' : 'unread' }}" 
                         data-notification-id="{{ $notification->id }}">
                        <div class="notification-icon">
                            @switch($notification->type)
                                @case('App\Notifications\CourseEnrolled')
                                    📚
                                    @break
                                @case('App\Notifications\NewMessage')
                                    💬
                                    @break
                                @case('App\Notifications\PaymentReceived')
                                    💰
                                    @break
                                @case('App\Notifications\CourseCompleted')
                                    🎉
                                    @break
                                @case('App\Notifications\LiveStreamStarted')
                                    🎥
                                    @break
                                @case('App\Notifications\CommunityInvite')
                                    🏘️
                                    @break
                                @default
                                    📢
                            @endswitch
                        </div>
                        
                        <div class="notification-content">
                            <div class="notification-title">
                                {{ $notification->data['title'] ?? 'Notification' }}
                            </div>
                            <div class="notification-message">
                                {{ $notification->data['message'] ?? 'You have a new notification' }}
                            </div>
                            <div class="notification-meta">
                                <span class="notification-time">
                                    {{ $notification->created_at ? $notification->created_at->diffForHumans() : 'Recently' }}
                                </span>
                                @if(!$notification->read_at)
                                    <span class="unread-badge">New</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="notification-actions">
                            @if(!$notification->read_at)
                                <button type="button" 
                                        class="action-btn mark-read-btn" 
                                        onclick="markAsRead('{{ $notification->id }}')"
                                        title="Mark as read">
                                    ✓
                                </button>
                            @endif
                            
                            @if(isset($notification->data['url']))
                                <a href="{{ $notification->data['url'] }}" 
                                   class="action-btn view-btn"
                                   title="View">
                                    👁️
                                </a>
                            @endif
                            
                            <button type="button" 
                                    class="action-btn delete-btn" 
                                    onclick="deleteNotification('{{ $notification->id }}')"
                                    title="Delete">
                                🗑️
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                {{ $notifications->links() }}
            </div>
        @else
            <div class="empty-state">
                <div class="empty-icon">📭</div>
                <h3>No Notifications</h3>
                <p>You're all caught up! No new notifications at the moment.</p>
                <a href="{{ route('dashboard') }}" class="btn btn-primary">
                    Back to Dashboard
                </a>
            </div>
        @endif
    </div>
</div>

<style>
.notifications-page {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.notification-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-content h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.stat-content p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.notifications-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.notifications-list {
    divide-y: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.notification-item.unread {
    background: rgba(59, 130, 246, 0.1);
    border-left: 4px solid #3b82f6;
}

.notification-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.notification-message {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-time {
    color: #666;
    font-size: 0.75rem;
}

.unread-badge {
    background: #3b82f6;
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.mark-read-btn:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: #22c55e;
    color: #22c55e;
}

.view-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.delete-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

.pagination-container {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .notifications-page {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .notification-stats {
        grid-template-columns: 1fr;
    }
    
    .notification-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .notification-actions {
        align-self: flex-end;
    }
}
</style>

<script>
// Mark single notification as read
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
            item.classList.remove('unread');
            item.classList.add('read');
            
            // Remove unread badge and mark as read button
            const unreadBadge = item.querySelector('.unread-badge');
            const markReadBtn = item.querySelector('.mark-read-btn');
            if (unreadBadge) unreadBadge.remove();
            if (markReadBtn) markReadBtn.remove();
            
            // Update header count
            updateUnreadCount();
        }
    })
    .catch(error => console.error('Error:', error));
}

// Mark all notifications as read
function markAllAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}

// Delete notification
function deleteNotification(notificationId) {
    if (confirm('Are you sure you want to delete this notification?')) {
        fetch(`/notifications/${notificationId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
                item.remove();
                updateUnreadCount();
            }
        })
        .catch(error => console.error('Error:', error));
    }
}

// Refresh notifications
function refreshNotifications() {
    location.reload();
}

// Update unread count in header
function updateUnreadCount() {
    fetch('/notifications/unread-count')
        .then(response => response.json())
        .then(data => {
            // Update navigation badge if it exists
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                if (data.count > 0) {
                    badge.textContent = data.count;
                    badge.style.display = 'block';
                } else {
                    badge.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error:', error));
}
</script>
@endsection
