<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'Welcome'); ?> - <?php echo e(config('app.name')); ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/auth.css')); ?>" rel="stylesheet">
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="guest-body">
    <!-- Background Elements -->
    <div class="auth-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
    </div>
    
    <!-- Navigation (minimal for guest) -->
    <nav class="guest-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="<?php echo e(route('home')); ?>" class="brand-link">
                    <span class="brand-icon">🌍</span>
                    <span class="brand-text">The Real World</span>
                </a>
            </div>
            
            <div class="nav-links">
                <?php if(auth()->guard()->guest()): ?>
                    <a href="<?php echo e(route('login')); ?>" class="nav-link <?php echo e(request()->routeIs('login') ? 'active' : ''); ?>">
                        Login
                    </a>
                    <a href="<?php echo e(route('register')); ?>" class="nav-link <?php echo e(request()->routeIs('register') ? 'active' : ''); ?>">
                        Register
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link">
                        Dashboard
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="guest-main">
        <div class="auth-container">
            <!-- Flash Messages -->
            <?php if(session('status')): ?>
                <div class="alert alert-success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content"><?php echo e(session('status')); ?></div>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content"><?php echo e(session('error')); ?></div>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">
                        <ul class="error-list">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="guest-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>The Real World</h4>
                    <p>Escape the matrix through education and community.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                        <li><a href="<?php echo e(route('communities.index')); ?>">Community</a></li>
                        <li><a href="<?php echo e(route('about')); ?>">About</a></li>
                        <li><a href="<?php echo e(route('contact')); ?>">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('help')); ?>">Help Center</a></li>
                        <li><a href="<?php echo e(route('privacy')); ?>">Privacy Policy</a></li>
                        <li><a href="<?php echo e(route('terms')); ?>">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo e(date('Y')); ?> The Real World. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Custom JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/auth.js')); ?>"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
        
        // Form validation enhancement
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = submitBtn.innerHTML.replace(/Login|Register|Submit/, 'Processing...');
                }
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/guest.blade.php ENDPATH**/ ?>