<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Payment;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show payment options for a course.
     */
    public function showPaymentOptions(Course $course)
    {
        // Check if user is already enrolled
        if ($course->students()->where('user_id', Auth::id())->exists()) {
            return redirect()->route('courses.show', $course)
                           ->with('info', 'You are already enrolled in this course.');
        }

        // Get user's saved payment methods
        $paymentMethods = Auth::user()->paymentMethods()->where('is_active', true)->get();

        return view('payment.options', compact('course', 'paymentMethods'));
    }

    /**
     * Process payment for a course.
     */
    public function processPayment(Request $request, Course $course)
    {
        $request->validate([
            'payment_method' => 'required|in:stripe,crypto',
            'payment_method_id' => 'nullable|exists:payment_methods,id',
        ]);

        // Check if user is already enrolled
        if ($course->students()->where('user_id', Auth::id())->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'You are already enrolled in this course.'
            ], 400);
        }

        // Create payment record
        $payment = Payment::create([
            'user_id' => Auth::id(),
            'course_id' => $course->id,
            'payment_method' => $request->payment_method,
            'amount' => $course->price,
            'currency' => 'USD',
            'status' => 'pending',
            'payment_intent_id' => Str::uuid(),
        ]);

        try {
            switch ($request->payment_method) {
                case 'stripe':
                    return $this->processStripePayment($payment, $request);

                case 'crypto':
                    return $this->processCryptoPayment($payment, $request);

                default:
                    throw new \Exception('Invalid payment method');
            }
        } catch (\Exception $e) {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle successful payment.
     */
    public function handleSuccessfulPayment(Payment $payment)
    {
        // Update payment status
        $payment->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        // Enroll user in course
        $payment->course->students()->attach($payment->user_id, [
            'enrolled_at' => now(),
            'progress' => 0,
        ]);

        // Send confirmation email (implement as needed)
        // Mail::to($payment->user)->send(new CourseEnrollmentConfirmation($payment));

        return response()->json([
            'success' => true,
            'message' => 'Payment successful! You are now enrolled in the course.',
            'redirect_url' => route('courses.show', $payment->course)
        ]);
    }

    /**
     * Handle failed payment.
     */
    public function handleFailedPayment(Payment $payment, $reason = null)
    {
        $payment->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Payment failed. Please try again or contact support.'
        ], 400);
    }

    /**
     * Get payment history for user.
     */
    public function paymentHistory()
    {
        $payments = Auth::user()->payments()
            ->with(['course'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('payment.history', compact('payments'));
    }

    /**
     * Show payment details.
     */
    public function showPayment(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->load(['course', 'user']);

        return view('payment.show', compact('payment'));
    }

    /**
     * Process refund request.
     */
    public function requestRefund(Payment $payment)
    {
        $this->authorize('refund', $payment);

        if ($payment->status !== 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'Only completed payments can be refunded.'
            ], 400);
        }

        // Check refund eligibility (e.g., within 30 days)
        if ($payment->completed_at->diffInDays(now()) > 30) {
            return response()->json([
                'success' => false,
                'message' => 'Refund period has expired (30 days).'
            ], 400);
        }

        try {
            switch ($payment->payment_method) {
                case 'stripe':
                    return $this->processStripeRefund($payment);

                case 'crypto':
                    return $this->processCryptoRefund($payment);

                default:
                    throw new \Exception('Invalid payment method for refund');
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process Stripe payment (delegated to StripeController).
     */
    private function processStripePayment(Payment $payment, Request $request)
    {
        $stripeController = new \App\Http\Controllers\Payment\StripeController();
        return $stripeController->processPayment($payment, $request);
    }

    /**
     * Process crypto payment (delegated to CryptoController).
     */
    private function processCryptoPayment(Payment $payment, Request $request)
    {
        $cryptoController = new \App\Http\Controllers\Payment\CryptoController();
        return $cryptoController->processPayment($payment, $request);
    }

    /**
     * Process Stripe refund.
     */
    private function processStripeRefund(Payment $payment)
    {
        $stripeController = new \App\Http\Controllers\Payment\StripeController();
        return $stripeController->processRefund($payment);
    }

    /**
     * Process crypto refund.
     */
    private function processCryptoRefund(Payment $payment)
    {
        $cryptoController = new \App\Http\Controllers\Payment\CryptoController();
        return $cryptoController->processRefund($payment);
    }
}
