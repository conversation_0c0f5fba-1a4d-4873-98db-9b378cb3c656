<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', config('app.name'))</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/minimal.css') }}" rel="stylesheet">
    
    @stack('styles')
</head>
<body class="minimal-body">
    <!-- Minimal Header -->
    <header class="minimal-header">
        <div class="header-container">
            <div class="header-brand">
                <a href="{{ route('home') }}" class="brand-link">
                    <span class="brand-icon">🌍</span>
                    <span class="brand-text">The Real World</span>
                </a>
            </div>
            
            @if(!isset($hideNavigation) || !$hideNavigation)
                <nav class="header-nav">
                    @guest
                        <a href="{{ route('login') }}" class="nav-link">Login</a>
                        <a href="{{ route('register') }}" class="nav-link primary">Register</a>
                    @else
                        <a href="{{ route('dashboard') }}" class="nav-link">Dashboard</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline-form">
                            @csrf
                            <button type="submit" class="nav-link logout">Logout</button>
                        </form>
                    @endguest
                </nav>
            @endif
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="minimal-main">
        <!-- Flash Messages -->
        @if (session('success'))
            <div class="alert alert-success">
                <div class="alert-icon">✅</div>
                <div class="alert-content">{{ session('success') }}</div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-error">
                <div class="alert-icon">❌</div>
                <div class="alert-content">{{ session('error') }}</div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        @endif

        @if (session('warning'))
            <div class="alert alert-warning">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">{{ session('warning') }}</div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        @endif

        @if (session('info'))
            <div class="alert alert-info">
                <div class="alert-icon">ℹ️</div>
                <div class="alert-content">{{ session('info') }}</div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-error">
                <div class="alert-icon">❌</div>
                <div class="alert-content">
                    <ul class="error-list">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        @endif
        
        <!-- Page Content -->
        @yield('content')
    </main>
    
    <!-- Minimal Footer -->
    @if(!isset($hideFooter) || !$hideFooter)
        <footer class="minimal-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <p>&copy; {{ date('Y') }} The Real World. All rights reserved.</p>
                    <div class="footer-links">
                        <a href="{{ route('privacy') }}">Privacy</a>
                        <a href="{{ route('terms') }}">Terms</a>
                        <a href="{{ route('contact') }}">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    @endif
    
    <!-- Custom JS -->
    <script src="{{ asset('assets/js/app.js') }}"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
