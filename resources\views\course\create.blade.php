@extends('layouts.app')

@section('title', 'Create New Course')

@section('content')
<div class="container">
    <div class="create-course-container">
        <div class="page-header">
            <h1>Create New Course</h1>
            <p>Share your knowledge and help others escape the matrix</p>
        </div>

        <form method="POST" action="{{ route('courses.store') }}" enctype="multipart/form-data" class="create-course-form">
            @csrf

            <!-- Basic Information -->
            <div class="form-section">
                <h2>📚 Basic Information</h2>
                
                <div class="form-group">
                    <label for="title" class="form-label">Course Title *</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           class="form-control @error('title') is-invalid @enderror" 
                           value="{{ old('title') }}" 
                           placeholder="Enter an engaging course title"
                           required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="short_description" class="form-label">Short Description *</label>
                    <textarea id="short_description" 
                              name="short_description" 
                              class="form-control @error('short_description') is-invalid @enderror" 
                              rows="3" 
                              placeholder="Brief description that will appear in course listings"
                              required>{{ old('short_description') }}</textarea>
                    @error('short_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Full Description *</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control @error('description') is-invalid @enderror" 
                              rows="8" 
                              placeholder="Detailed course description, what students will learn, prerequisites, etc."
                              required>{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="category_id" class="form-label">Category *</label>
                        <select id="category_id" name="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
                            <option value="">Select a category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="difficulty_level" class="form-label">Difficulty Level *</label>
                        <select id="difficulty_level" name="difficulty_level" class="form-control @error('difficulty_level') is-invalid @enderror" required>
                            <option value="">Select difficulty</option>
                            <option value="beginner" {{ old('difficulty_level') === 'beginner' ? 'selected' : '' }}>🟢 Beginner</option>
                            <option value="intermediate" {{ old('difficulty_level') === 'intermediate' ? 'selected' : '' }}>🟡 Intermediate</option>
                            <option value="advanced" {{ old('difficulty_level') === 'advanced' ? 'selected' : '' }}>🔴 Advanced</option>
                        </select>
                        @error('difficulty_level')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Course Media -->
            <div class="form-section">
                <h2>🎨 Course Media</h2>
                
                <div class="form-group">
                    <label for="thumbnail" class="form-label">Course Thumbnail</label>
                    <div class="file-upload-area" onclick="document.getElementById('thumbnail').click()">
                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" class="file-input" onchange="previewImage(this, 'thumbnail-preview')">
                        <div class="upload-placeholder" id="thumbnail-placeholder">
                            <span class="upload-icon">🖼️</span>
                            <span class="upload-text">Click to upload course thumbnail</span>
                            <span class="upload-hint">Recommended: 1280x720px, JPG or PNG</span>
                        </div>
                        <div class="image-preview" id="thumbnail-preview" style="display: none;">
                            <img src="" alt="Thumbnail preview">
                            <button type="button" class="remove-image" onclick="removeImage('thumbnail', 'thumbnail-preview')">✕</button>
                        </div>
                    </div>
                    @error('thumbnail')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="trailer_video" class="form-label">Course Trailer Video</label>
                    <div class="file-upload-area" onclick="document.getElementById('trailer_video').click()">
                        <input type="file" id="trailer_video" name="trailer_video" accept="video/*" class="file-input">
                        <div class="upload-placeholder">
                            <span class="upload-icon">🎥</span>
                            <span class="upload-text">Click to upload course trailer</span>
                            <span class="upload-hint">Optional: MP4, MOV, or AVI (max 100MB)</span>
                        </div>
                    </div>
                    @error('trailer_video')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Course Details -->
            <div class="form-section">
                <h2>⚙️ Course Details</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price" class="form-label">Price (USD) *</label>
                        <div class="input-group">
                            <span class="input-prefix">$</span>
                            <input type="number" 
                                   id="price" 
                                   name="price" 
                                   class="form-control @error('price') is-invalid @enderror" 
                                   value="{{ old('price') }}" 
                                   min="0" 
                                   step="0.01"
                                   placeholder="99.00"
                                   required>
                        </div>
                        @error('price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="duration_hours" class="form-label">Duration (Hours) *</label>
                        <input type="number" 
                               id="duration_hours" 
                               name="duration_hours" 
                               class="form-control @error('duration_hours') is-invalid @enderror" 
                               value="{{ old('duration_hours') }}" 
                               min="1" 
                               placeholder="10"
                               required>
                        @error('duration_hours')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group">
                    <label for="requirements" class="form-label">Requirements</label>
                    <textarea id="requirements" 
                              name="requirements" 
                              class="form-control @error('requirements') is-invalid @enderror" 
                              rows="4" 
                              placeholder="List any prerequisites or requirements for this course">{{ old('requirements') }}</textarea>
                    @error('requirements')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="what_you_will_learn" class="form-label">What Students Will Learn</label>
                    <textarea id="what_you_will_learn" 
                              name="what_you_will_learn" 
                              class="form-control @error('what_you_will_learn') is-invalid @enderror" 
                              rows="4" 
                              placeholder="List the key learning outcomes and skills students will gain">{{ old('what_you_will_learn') }}</textarea>
                    @error('what_you_will_learn')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Course Settings -->
            <div class="form-section">
                <h2>🔧 Course Settings</h2>
                
                <div class="settings-grid">
                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">⭐ Featured Course</span>
                                <span class="toggle-desc">Display this course prominently on the homepage</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="allow_comments" value="1" {{ old('allow_comments', true) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">💬 Allow Comments</span>
                                <span class="toggle-desc">Students can leave comments and reviews</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="certificate_enabled" value="1" {{ old('certificate_enabled', true) ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">🏆 Certificate of Completion</span>
                                <span class="toggle-desc">Award certificates when students complete the course</span>
                            </div>
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="toggle-label">
                            <input type="checkbox" name="drip_content" value="1" {{ old('drip_content') ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                            <div class="toggle-content">
                                <span class="toggle-title">⏰ Drip Content</span>
                                <span class="toggle-desc">Release lessons gradually over time</span>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="{{ route('courses.index') }}" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" name="status" value="draft" class="btn btn-outline">
                    📝 Save as Draft
                </button>
                <button type="submit" name="status" value="published" class="btn btn-primary">
                    🚀 Publish Course
                </button>
            </div>
        </form>

        <!-- Course Creation Tips -->
        <div class="tips-section">
            <h3>💡 Course Creation Tips</h3>
            <div class="tips-grid">
                <div class="tip">
                    <div class="tip-icon">🎯</div>
                    <h4>Clear Learning Objectives</h4>
                    <p>Define what students will achieve by the end of your course. Be specific and measurable.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">📖</div>
                    <h4>Engaging Content</h4>
                    <p>Mix different content types: videos, text, quizzes, and practical exercises to keep students engaged.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🏗️</div>
                    <h4>Logical Structure</h4>
                    <p>Organize your content in a logical progression from basic concepts to advanced topics.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🎥</div>
                    <h4>Quality Production</h4>
                    <p>Ensure good audio and video quality. Clear, professional content builds trust and credibility.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.create-course-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #a0a0a0;
    font-size: 1.125rem;
}

.create-course-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.input-group {
    position: relative;
}

.input-prefix {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
    font-weight: 500;
    z-index: 1;
}

.input-group .form-control {
    padding-left: 2.5rem;
}

.file-upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 2.5rem;
}

.upload-text {
    color: #ffffff;
    font-weight: 500;
}

.upload-hint {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.image-preview {
    position: relative;
    display: inline-block;
}

.image-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 0.75rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.setting-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-content {
    display: flex;
    flex-direction: column;
}

.toggle-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toggle-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.tips-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.tips-section h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tip {
    text-align: center;
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.tip h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .create-course-container {
        padding: 1rem;
    }
    
    .create-course-form {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById(previewId);
            const placeholder = document.getElementById(previewId.replace('-preview', '-placeholder'));
            
            preview.querySelector('img').src = e.target.result;
            preview.style.display = 'block';
            placeholder.style.display = 'none';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeImage(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    const placeholder = document.getElementById(previewId.replace('-preview', '-placeholder'));
    
    input.value = '';
    preview.style.display = 'none';
    placeholder.style.display = 'flex';
}
</script>
@endsection
