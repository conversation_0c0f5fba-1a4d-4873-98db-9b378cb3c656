@extends('layouts.app')

@section('title', 'My Courses')

@section('content')
<div class="container">
    <div class="my-courses-container">
        <div class="page-header">
            <h1>My Courses</h1>
            <p>Continue your learning journey and track your progress</p>
        </div>

        @if($enrollments->count() > 0)
            <!-- Progress Overview -->
            <div class="progress-overview">
                <div class="overview-stats">
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-info">
                            <h3>{{ $enrollments->count() }}</h3>
                            <p>Enrolled Courses</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <h3>{{ $enrollments->where('completed_at', '!=', null)->count() }}</h3>
                            <p>Completed</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-info">
                            <h3>{{ $enrollments->where('completed_at', null)->count() }}</h3>
                            <p>In Progress</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-info">
                            <h3>{{ number_format($enrollments->avg('progress') ?? 0, 1) }}%</h3>
                            <p>Avg Progress</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="tab-btn active" data-filter="all">
                    All Courses ({{ $enrollments->count() }})
                </button>
                <button class="tab-btn" data-filter="in-progress">
                    In Progress ({{ $enrollments->where('completed_at', null)->count() }})
                </button>
                <button class="tab-btn" data-filter="completed">
                    Completed ({{ $enrollments->where('completed_at', '!=', null)->count() }})
                </button>
            </div>

            <!-- Courses Grid -->
            <div class="courses-grid">
                @foreach($enrollments as $enrollment)
                    <div class="course-card {{ $enrollment->completed_at ? 'completed' : 'in-progress' }}" data-status="{{ $enrollment->completed_at ? 'completed' : 'in-progress' }}">
                        <div class="course-thumbnail">
                            @if($enrollment->thumbnail)
                                <img src="{{ Storage::url($enrollment->thumbnail) }}" alt="{{ $enrollment->title }}">
                            @else
                                <div class="thumbnail-placeholder">
                                    <span class="placeholder-icon">📚</span>
                                </div>
                            @endif
                            
                            @if($enrollment->completed_at)
                                <div class="completion-badge">
                                    <span class="badge-icon">🏆</span>
                                    <span class="badge-text">Completed</span>
                                </div>
                            @else
                                <div class="progress-badge">
                                    <span class="progress-text">{{ number_format($enrollment->progress ?? 0, 0) }}%</span>
                                </div>
                            @endif
                        </div>

                        <div class="course-content">
                            <div class="course-header">
                                <h3 class="course-title">{{ $enrollment->title }}</h3>
                                <div class="course-meta">
                                    <span class="instructor">by {{ $enrollment->instructor->name }}</span>
                                    <span class="category">{{ $enrollment->category->name }}</span>
                                </div>
                            </div>

                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ $enrollment->progress ?? 0 }}%"></div>
                                </div>
                                <div class="progress-info">
                                    <span class="progress-percentage">{{ number_format($enrollment->progress ?? 0, 0) }}% Complete</span>
                                    @if($enrollment->last_accessed_at)
                                        <span class="last-accessed">Last accessed {{ $enrollment->last_accessed_at->diffForHumans() }}</span>
                                    @endif
                                </div>
                            </div>

                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-icon">📅</span>
                                    <span class="stat-text">Enrolled {{ $enrollment->pivot->created_at->format('M j, Y') }}</span>
                                </div>
                                @if($enrollment->duration_hours)
                                    <div class="stat">
                                        <span class="stat-icon">⏱️</span>
                                        <span class="stat-text">{{ $enrollment->duration_hours }} hours</span>
                                    </div>
                                @endif
                                @if($enrollment->difficulty_level)
                                    <div class="stat">
                                        <span class="stat-icon">
                                            @if($enrollment->difficulty_level === 'beginner')
                                                🟢
                                            @elseif($enrollment->difficulty_level === 'intermediate')
                                                🟡
                                            @else
                                                🔴
                                            @endif
                                        </span>
                                        <span class="stat-text">{{ ucfirst($enrollment->difficulty_level) }}</span>
                                    </div>
                                @endif
                            </div>

                            <div class="course-actions">
                                @if($enrollment->completed_at)
                                    <a href="{{ route('courses.show', $enrollment) }}" class="btn btn-secondary">
                                        📖 Review Course
                                    </a>
                                    <button type="button" class="btn btn-outline" onclick="downloadCertificate({{ $enrollment->id }})">
                                        🏆 Certificate
                                    </button>
                                @else
                                    <a href="{{ route('courses.show', $enrollment) }}" class="btn btn-primary">
                                        ▶️ Continue Learning
                                    </a>
                                    @if($enrollment->progress > 0)
                                        <button type="button" class="btn btn-outline" onclick="resetProgress({{ $enrollment->id }})">
                                            🔄 Reset Progress
                                        </button>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($enrollments->hasPages())
                <div class="pagination-wrapper">
                    {{ $enrollments->links() }}
                </div>
            @endif

        @else
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h2>No Courses Yet</h2>
                <p>You haven't enrolled in any courses yet. Start your learning journey today!</p>
                <div class="empty-actions">
                    <a href="{{ route('courses.index') }}" class="btn btn-primary">
                        🔍 Browse Courses
                    </a>
                    <a href="{{ route('communities.index') }}" class="btn btn-secondary">
                        👥 Join Communities
                    </a>
                </div>
            </div>
        @endif

        <!-- Learning Tips -->
        <div class="learning-tips">
            <h3>💡 Learning Tips</h3>
            <div class="tips-grid">
                <div class="tip">
                    <div class="tip-icon">🎯</div>
                    <h4>Set Goals</h4>
                    <p>Set daily or weekly learning goals to maintain consistent progress.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">📝</div>
                    <h4>Take Notes</h4>
                    <p>Write down key concepts and insights as you learn for better retention.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🤝</div>
                    <h4>Engage</h4>
                    <p>Participate in discussions and ask questions to deepen your understanding.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🔄</div>
                    <h4>Practice</h4>
                    <p>Apply what you learn through exercises and real-world projects.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.my-courses-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #a0a0a0;
    font-size: 1.125rem;
}

.progress-overview {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-info h3 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 0.5rem;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.course-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.course-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-icon {
    font-size: 4rem;
    color: white;
}

.completion-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(34, 197, 94, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.progress-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(59, 130, 246, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.instructor {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.category {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.course-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-percentage {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.last-accessed {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.course-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-icon {
    font-size: 1rem;
}

.stat-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.course-actions {
    display: flex;
    gap: 0.75rem;
}

.course-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 6rem;
    margin-bottom: 2rem;
    opacity: 0.5;
}

.empty-state h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 2rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.learning-tips {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.learning-tips h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tip {
    text-align: center;
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.tip h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .my-courses-container {
        padding: 1rem;
    }
    
    .overview-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-tabs {
        flex-direction: column;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .course-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .course-actions {
        flex-direction: column;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Filter functionality
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        this.classList.add('active');
        
        const filter = this.dataset.filter;
        const cards = document.querySelectorAll('.course-card');
        
        cards.forEach(card => {
            if (filter === 'all') {
                card.style.display = 'block';
            } else if (filter === 'completed' && card.classList.contains('completed')) {
                card.style.display = 'block';
            } else if (filter === 'in-progress' && card.classList.contains('in-progress')) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });
});

function downloadCertificate(enrollmentId) {
    // Implementation for certificate download
    console.log('Download certificate for enrollment:', enrollmentId);
    alert('Certificate download functionality would be implemented here');
}

function resetProgress(enrollmentId) {
    if (confirm('Are you sure you want to reset your progress? This action cannot be undone.')) {
        // Implementation for progress reset
        console.log('Reset progress for enrollment:', enrollmentId);
        alert('Progress reset functionality would be implemented here');
    }
}
</script>
@endsection
