<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'The Real World - Escape the Matrix'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Join The Real World and escape the matrix through education, community, and real-world skills. Learn from successful entrepreneurs and build your empire.'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', 'education, entrepreneurship, business, success, community, courses, mentorship, real world'); ?>">
    <meta name="author" content="The Real World">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $__env->yieldContent('og_title', 'The Real World - Escape the Matrix'); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('og_description', 'Join The Real World and escape the matrix through education and community'); ?>">
    <meta property="og:image" content="<?php echo $__env->yieldContent('og_image', asset('assets/images/og-landing.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $__env->yieldContent('twitter_title', 'The Real World - Escape the Matrix'); ?>">
    <meta name="twitter:description" content="<?php echo $__env->yieldContent('twitter_description', 'Join The Real World and escape the matrix through education and community'); ?>">
    <meta name="twitter:image" content="<?php echo $__env->yieldContent('twitter_image', asset('assets/images/twitter-landing.jpg')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/landing.css')); ?>" rel="stylesheet">
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="landing-body">
    <!-- Landing Navigation -->
    <nav class="landing-nav">
        <div class="nav-container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="<?php echo e(route('home')); ?>" class="brand-link">
                        <span class="brand-icon">🌍</span>
                        <span class="brand-text">The Real World</span>
                    </a>
                </div>
                
                <div class="nav-links">
                    <a href="#features" class="nav-link">Features</a>
                    <a href="#courses" class="nav-link">Courses</a>
                    <a href="#community" class="nav-link">Community</a>
                    <a href="#testimonials" class="nav-link">Success Stories</a>
                    <a href="#pricing" class="nav-link">Pricing</a>
                </div>
                
                <div class="nav-actions">
                    <?php if(auth()->guard()->guest()): ?>
                        <a href="<?php echo e(route('login')); ?>" class="nav-btn login-btn">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="nav-btn register-btn">Join Now</a>
                    <?php else: ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="nav-btn dashboard-btn">Dashboard</a>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button type="button" class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    <span class="hamburger"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobileMenu" style="display: none;">
            <div class="mobile-menu-content">
                <a href="#features" class="mobile-nav-link" onclick="closeMobileMenu()">Features</a>
                <a href="#courses" class="mobile-nav-link" onclick="closeMobileMenu()">Courses</a>
                <a href="#community" class="mobile-nav-link" onclick="closeMobileMenu()">Community</a>
                <a href="#testimonials" class="mobile-nav-link" onclick="closeMobileMenu()">Success Stories</a>
                <a href="#pricing" class="mobile-nav-link" onclick="closeMobileMenu()">Pricing</a>
                
                <div class="mobile-nav-actions">
                    <?php if(auth()->guard()->guest()): ?>
                        <a href="<?php echo e(route('login')); ?>" class="mobile-nav-btn login-btn">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="mobile-nav-btn register-btn">Join Now</a>
                    <?php else: ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="mobile-nav-btn dashboard-btn">Dashboard</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="landing-main">
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- Landing Footer -->
    <footer class="landing-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section brand-section">
                    <div class="footer-brand">
                        <span class="footer-logo">🌍</span>
                        <h3>The Real World</h3>
                    </div>
                    <p class="footer-description">
                        Escape the matrix through education, community, and real-world skills. 
                        Join thousands of successful entrepreneurs building their empires.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link">📘</a>
                        <a href="#" class="social-link">🐦</a>
                        <a href="#" class="social-link">📸</a>
                        <a href="#" class="social-link">💼</a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Platform</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                        <li><a href="<?php echo e(route('communities.index')); ?>">Community</a></li>
                        <li><a href="<?php echo e(route('chat.index')); ?>">Live Chat</a></li>
                        <li><a href="<?php echo e(route('instructors.index')); ?>">Instructors</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('help')); ?>">Help Center</a></li>
                        <li><a href="<?php echo e(route('blog')); ?>">Blog</a></li>
                        <li><a href="<?php echo e(route('success-stories')); ?>">Success Stories</a></li>
                        <li><a href="<?php echo e(route('affiliate')); ?>">Affiliate Program</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('about')); ?>">About Us</a></li>
                        <li><a href="<?php echo e(route('careers')); ?>">Careers</a></li>
                        <li><a href="<?php echo e(route('contact')); ?>">Contact</a></li>
                        <li><a href="<?php echo e(route('press')); ?>">Press</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('privacy')); ?>">Privacy Policy</a></li>
                        <li><a href="<?php echo e(route('terms')); ?>">Terms of Service</a></li>
                        <li><a href="<?php echo e(route('cookies')); ?>">Cookie Policy</a></li>
                        <li><a href="<?php echo e(route('refund')); ?>">Refund Policy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; <?php echo e(date('Y')); ?> The Real World. All rights reserved.</p>
                    <div class="footer-bottom-links">
                        <span class="footer-stats">🔥 <?php echo e(number_format(50000)); ?>+ Active Members</span>
                        <span class="footer-stats">💰 $<?php echo e(number_format(10000000)); ?>+ Student Earnings</span>
                        <span class="footer-stats">🏆 <?php echo e(number_format(500)); ?>+ Success Stories</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button type="button" class="back-to-top" id="backToTop" onclick="scrollToTop()" style="display: none;">
        ↑
    </button>
    
    <!-- Custom JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/landing.js')); ?>"></script>
    
    <script>
        // Mobile menu functionality
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            const isVisible = menu.style.display === 'block';
            menu.style.display = isVisible ? 'none' : 'block';
            document.body.classList.toggle('mobile-menu-open', !isVisible);
        }
        
        function closeMobileMenu() {
            document.getElementById('mobileMenu').style.display = 'none';
            document.body.classList.remove('mobile-menu-open');
        }
        
        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                        closeMobileMenu();
                    }
                });
            });
            
            // Back to top button
            window.addEventListener('scroll', function() {
                const backToTop = document.getElementById('backToTop');
                if (window.pageYOffset > 300) {
                    backToTop.style.display = 'block';
                } else {
                    backToTop.style.display = 'none';
                }
            });
        });
        
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobileMenu');
            const button = document.querySelector('.mobile-menu-btn');
            
            if (menu && button && !button.contains(event.target) && !menu.contains(event.target)) {
                closeMobileMenu();
            }
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/landing.blade.php ENDPATH**/ ?>