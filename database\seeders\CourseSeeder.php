<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseLesson;
use Illuminate\Support\Str;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create course categories
        $categories = [
            [
                'name' => 'Business & Entrepreneurship',
                'slug' => 'business-entrepreneurship',
                'description' => 'Learn how to start and scale profitable businesses',
                'icon' => '💰',
                'color' => '#3b82f6',
            ],
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'description' => 'Master crypto trading and blockchain technology',
                'icon' => '₿',
                'color' => '#f59e0b',
            ],
            [
                'name' => 'Fitness & Health',
                'slug' => 'fitness-health',
                'description' => 'Build a strong body and mind',
                'icon' => '💪',
                'color' => '#10b981',
            ],
            [
                'name' => 'Mindset & Psychology',
                'slug' => 'mindset-psychology',
                'description' => 'Develop mental strength and resilience',
                'icon' => '🧠',
                'color' => '#8b5cf6',
            ],
            [
                'name' => 'Sales & Marketing',
                'slug' => 'sales-marketing',
                'description' => 'Master the art of persuasion and customer acquisition',
                'icon' => '🎯',
                'color' => '#ef4444',
            ],
            [
                'name' => 'Investing & Trading',
                'slug' => 'investing-trading',
                'description' => 'Learn to multiply your money through smart investments',
                'icon' => '📈',
                'color' => '#06b6d4',
            ],
        ];

        foreach ($categories as $categoryData) {
            CourseCategory::create($categoryData);
        }

        // Create instructor user
        $instructor = User::create([
            'name' => 'Andrew Tate',
            'first_name' => 'Andrew',
            'last_name' => 'Tate',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $instructor->assignRole('instructor');

        // Create sample courses
        $courses = [
            [
                'title' => 'Escape The Matrix: Business Fundamentals',
                'short_description' => 'Learn the fundamentals of building a profitable business from scratch.',
                'description' => 'This comprehensive course will teach you everything you need to know about starting and scaling a profitable business. From idea validation to customer acquisition, you\'ll learn the strategies that successful entrepreneurs use to build million-dollar companies.',
                'category_id' => 1,
                'price' => 0,
                'difficulty_level' => 'beginner',
                'duration_hours' => 8,
                'what_you_learn' => [
                    'How to validate business ideas',
                    'Building a minimum viable product',
                    'Customer acquisition strategies',
                    'Scaling your business operations',
                    'Financial management for entrepreneurs'
                ],
                'requirements' => [
                    'Basic computer skills',
                    'Willingness to take action',
                    'Notebook for taking notes'
                ],
            ],
            [
                'title' => 'Cryptocurrency Mastery: From Beginner to Pro',
                'short_description' => 'Master cryptocurrency trading, DeFi, and blockchain technology.',
                'description' => 'Dive deep into the world of cryptocurrency and blockchain technology. Learn how to trade cryptocurrencies profitably, understand DeFi protocols, and position yourself for the future of finance.',
                'category_id' => 2,
                'price' => 49,
                'difficulty_level' => 'intermediate',
                'duration_hours' => 12,
                'what_you_learn' => [
                    'Cryptocurrency fundamentals',
                    'Technical analysis for crypto trading',
                    'DeFi protocols and yield farming',
                    'Risk management strategies',
                    'Portfolio diversification'
                ],
                'requirements' => [
                    'Basic understanding of finance',
                    'Computer with internet access',
                    'Small amount of capital to practice with'
                ],
            ],
            [
                'title' => 'Alpha Male Fitness: Build Your Warrior Body',
                'short_description' => 'Transform your physique and build unshakeable confidence.',
                'description' => 'This isn\'t just another fitness program. This is a complete transformation system designed to build not just your body, but your mind and spirit. Learn the training methods, nutrition strategies, and mindset principles that will turn you into a warrior.',
                'category_id' => 3,
                'price' => 29,
                'difficulty_level' => 'beginner',
                'duration_hours' => 6,
                'what_you_learn' => [
                    'Effective workout routines',
                    'Nutrition for muscle building',
                    'Supplement strategies',
                    'Recovery and sleep optimization',
                    'Building mental toughness'
                ],
                'requirements' => [
                    'Access to a gym or basic equipment',
                    'Commitment to consistency',
                    'Willingness to push your limits'
                ],
            ],
            [
                'title' => 'Millionaire Mindset: Psychology of Success',
                'short_description' => 'Develop the mental framework of highly successful individuals.',
                'description' => 'Success starts in the mind. This course will reprogram your thinking patterns, eliminate limiting beliefs, and install the mental frameworks that millionaires use to achieve extraordinary results.',
                'category_id' => 4,
                'price' => 39,
                'difficulty_level' => 'intermediate',
                'duration_hours' => 10,
                'what_you_learn' => [
                    'Identifying and eliminating limiting beliefs',
                    'Goal setting and achievement strategies',
                    'Developing unshakeable confidence',
                    'Stress management techniques',
                    'Building resilience and mental toughness'
                ],
                'requirements' => [
                    'Open mind and willingness to change',
                    'Journal for exercises',
                    'Quiet space for reflection'
                ],
            ],
        ];

        foreach ($courses as $courseData) {
            $course = Course::create([
                'title' => $courseData['title'],
                'slug' => Str::slug($courseData['title']),
                'description' => $courseData['description'],
                'short_description' => $courseData['short_description'],
                'category_id' => $courseData['category_id'],
                'instructor_id' => $instructor->id,
                'price' => $courseData['price'],
                'difficulty_level' => $courseData['difficulty_level'],
                'duration_hours' => $courseData['duration_hours'],
                'status' => 'published',
                'is_free' => $courseData['price'] == 0,
                'is_featured' => true,
                'what_you_learn' => $courseData['what_you_learn'],
                'requirements' => $courseData['requirements'],
                'published_at' => now(),
                'rating' => rand(40, 50) / 10,
                'rating_count' => rand(50, 200),
                'total_students' => rand(100, 1000),
            ]);

            // Create sample lessons for each course
            $lessonTitles = [
                'Introduction and Welcome',
                'Foundation Principles',
                'Core Strategies',
                'Advanced Techniques',
                'Implementation Guide',
                'Common Mistakes to Avoid',
                'Success Stories and Case Studies',
                'Next Steps and Action Plan',
            ];

            foreach ($lessonTitles as $index => $title) {
                CourseLesson::create([
                    'course_id' => $course->id,
                    'title' => $title,
                    'slug' => Str::slug($title),
                    'description' => 'Detailed lesson covering ' . strtolower($title),
                    'lesson_type' => 'video',
                    'video_duration' => rand(10, 45) . ' minutes',
                    'sort_order' => $index + 1,
                    'is_published' => true,
                ]);
            }

            // Update total lessons count
            $course->update(['total_lessons' => count($lessonTitles)]);
        }
    }
}
