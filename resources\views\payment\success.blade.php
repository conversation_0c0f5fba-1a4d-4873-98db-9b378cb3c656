<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Payment Successful - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/payment.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}">Courses</a></li>
                <li><a href="{{ route('communities.index') }}">Community</a></li>
                <li><a href="{{ route('chat.index') }}">Chat</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="payment-main">
        <div class="container">
            <div class="payment-success-container">
                <div class="success-card">
                    <div class="success-icon">
                        <div class="checkmark">✓</div>
                    </div>
                    
                    <h1>Payment Successful!</h1>
                    <p class="success-message">
                        Your payment has been processed successfully and you have been enrolled in the course.
                    </p>

                    @if($course)
                        <div class="course-info">
                            <h3>Course Enrolled:</h3>
                            <div class="enrolled-course">
                                <h4>{{ $course->title }}</h4>
                                <p>{{ $course->short_description }}</p>
                                <div class="course-meta">
                                    <span>👨‍🏫 {{ $course->instructor->name }}</span>
                                    <span>⏱️ {{ $course->duration_hours }} hours</span>
                                    <span>📊 {{ ucfirst($course->difficulty_level) }}</span>
                                </div>
                            </div>
                        </div>

                        @if($payment)
                            <div class="payment-details">
                                <h3>Payment Details:</h3>
                                <div class="payment-info">
                                    <div class="payment-row">
                                        <span>Amount Paid:</span>
                                        <span>${{ number_format($payment->amount, 2) }}</span>
                                    </div>
                                    <div class="payment-row">
                                        <span>Payment Method:</span>
                                        <span>{{ ucfirst($payment->payment_method) }}</span>
                                    </div>
                                    <div class="payment-row">
                                        <span>Transaction ID:</span>
                                        <span>{{ $payment->payment_intent_id }}</span>
                                    </div>
                                    <div class="payment-row">
                                        <span>Date:</span>
                                        <span>{{ $payment->created_at->format('M j, Y g:i A') }}</span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="next-steps">
                            <h3>What's Next?</h3>
                            <ul>
                                <li>✅ You now have full access to all course materials</li>
                                <li>✅ Start learning immediately with the first lesson</li>
                                <li>✅ Join the course community to connect with other students</li>
                                <li>✅ Track your progress on your dashboard</li>
                            </ul>
                        </div>

                        <div class="action-buttons">
                            <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-large">
                                🚀 Start Learning Now
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                📊 Go to Dashboard
                            </a>
                        </div>
                    @else
                        <div class="action-buttons">
                            <a href="{{ route('dashboard') }}" class="btn btn-primary btn-large">
                                📊 Go to Dashboard
                            </a>
                            <a href="{{ route('courses.index') }}" class="btn btn-secondary">
                                📚 Browse Courses
                            </a>
                        </div>
                    @endif

                    <div class="support-info">
                        <p>
                            <strong>Need help?</strong> 
                            Contact our support team at 
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="welcome-message">
                    <h2>Welcome to The Real World Community! 🎉</h2>
                    <p>
                        You've just taken a massive step towards escaping the matrix. 
                        Your journey to financial freedom and success starts now.
                    </p>
                    
                    <div class="community-benefits">
                        <div class="benefit">
                            <div class="benefit-icon">🎓</div>
                            <h4>Expert Knowledge</h4>
                            <p>Learn from successful entrepreneurs and industry experts</p>
                        </div>
                        <div class="benefit">
                            <div class="benefit-icon">👥</div>
                            <h4>Elite Community</h4>
                            <p>Connect with like-minded individuals on the same journey</p>
                        </div>
                        <div class="benefit">
                            <div class="benefit-icon">💰</div>
                            <h4>Real Results</h4>
                            <p>Apply proven strategies to build wealth and success</p>
                        </div>
                        <div class="benefit">
                            <div class="benefit-icon">🚀</div>
                            <h4>Lifetime Access</h4>
                            <p>Keep learning and growing with continuous updates</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script>
        // Confetti animation for success
        document.addEventListener('DOMContentLoaded', function() {
            // Simple confetti effect
            function createConfetti() {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.animationDelay = Math.random() * 2 + 's';
                confetti.style.backgroundColor = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'][Math.floor(Math.random() * 4)];
                document.body.appendChild(confetti);
                
                setTimeout(() => {
                    confetti.remove();
                }, 3000);
            }
            
            // Create confetti particles
            for (let i = 0; i < 50; i++) {
                setTimeout(createConfetti, i * 100);
            }
        });
    </script>

    <style>
        .payment-success-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .success-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .success-icon {
            margin-bottom: 2rem;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            font-size: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            animation: checkmarkPulse 0.6s ease-out;
        }

        @keyframes checkmarkPulse {
            0% { transform: scale(0); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .success-card h1 {
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .success-message {
            color: #a0a0a0;
            font-size: 1.25rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .course-info, .payment-details, .next-steps {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
        }

        .course-info h3, .payment-details h3, .next-steps h3 {
            color: #ffffff;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .enrolled-course h4 {
            color: #3b82f6;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .course-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .course-meta span {
            color: #a0a0a0;
            font-size: 0.875rem;
        }

        .payment-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .payment-row:last-child {
            border-bottom: none;
        }

        .next-steps ul {
            list-style: none;
            padding: 0;
        }

        .next-steps li {
            color: #a0a0a0;
            margin-bottom: 0.5rem;
            padding-left: 0.5rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        .support-info {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .support-info p {
            color: #a0a0a0;
            font-size: 0.875rem;
        }

        .support-info a {
            color: #3b82f6;
            text-decoration: none;
        }

        .welcome-message {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
        }

        .welcome-message h2 {
            color: #ffffff;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .community-benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .benefit {
            text-align: center;
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .benefit h4 {
            color: #ffffff;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .benefit p {
            color: #a0a0a0;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            top: -10px;
            z-index: 1000;
            animation: confettiFall 3s linear forwards;
        }

        @keyframes confettiFall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }

        @media (max-width: 768px) {
            .payment-success-container {
                padding: 1rem;
            }
            
            .success-card {
                padding: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .community-benefits {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
