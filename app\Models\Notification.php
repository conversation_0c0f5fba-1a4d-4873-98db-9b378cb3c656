<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'action_url',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include unread notifications.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope a query to only include read notifications.
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Get the notification icon based on type.
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            'course_enrollment' => '📚',
            'payment_success' => '💰',
            'payment_failed' => '❌',
            'course_completed' => '🎉',
            'new_message' => '💬',
            'community_post' => '📝',
            'achievement_earned' => '🏆',
            'system_update' => '🔔',
            default => '📢',
        };
    }

    /**
     * Get the notification color based on type.
     */
    public function getColorAttribute(): string
    {
        return match($this->type) {
            'course_enrollment' => 'blue',
            'payment_success' => 'green',
            'payment_failed' => 'red',
            'course_completed' => 'purple',
            'new_message' => 'blue',
            'community_post' => 'orange',
            'achievement_earned' => 'gold',
            'system_update' => 'gray',
            default => 'blue',
        };
    }
}
