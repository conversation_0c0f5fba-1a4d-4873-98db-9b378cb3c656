<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Lesson;
use App\Models\User;
use App\Models\Payment;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InstructorController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('instructor');
    }

    /**
     * Show the instructor dashboard.
     */
    public function dashboard()
    {
        $instructor = Auth::user();
        
        // Get instructor's courses
        $courses = Course::where('instructor_id', $instructor->id)
            ->withCount(['enrollments', 'lessons'])
            ->with(['category'])
            ->get();

        // Calculate metrics
        $metrics = [
            'total_courses' => $courses->count(),
            'total_students' => $courses->sum('enrollments_count'),
            'total_lessons' => $courses->sum('lessons_count'),
            'total_revenue' => $this->calculateInstructorRevenue($instructor->id),
            'published_courses' => $courses->where('status', 'published')->count(),
            'draft_courses' => $courses->where('status', 'draft')->count(),
        ];

        // Get recent enrollments
        $recentEnrollments = CourseEnrollment::whereHas('course', function($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['user', 'course'])
        ->latest()
        ->take(10)
        ->get();

        // Get revenue data for chart
        $revenueData = $this->getRevenueChartData($instructor->id);

        // Get top performing courses
        $topCourses = $courses->sortByDesc('enrollments_count')->take(5);

        return view('instructor.dashboard', compact(
            'courses',
            'metrics',
            'recentEnrollments',
            'revenueData',
            'topCourses'
        ));
    }

    /**
     * Show instructor's courses.
     */
    public function courses()
    {
        $instructor = Auth::user();
        
        $courses = Course::where('instructor_id', $instructor->id)
            ->withCount(['enrollments', 'lessons'])
            ->with(['category'])
            ->paginate(12);

        return view('instructor.courses.index', compact('courses'));
    }

    /**
     * Show students for a specific course.
     */
    public function students(Course $course)
    {
        $this->authorize('manageEnrollments', $course);

        $enrollments = CourseEnrollment::where('course_id', $course->id)
            ->with(['user'])
            ->paginate(20);

        return view('instructor.courses.students', compact('course', 'enrollments'));
    }

    /**
     * Show all students across instructor's courses.
     */
    public function allStudents()
    {
        $instructor = Auth::user();
        
        $students = User::whereHas('enrolledCourses', function($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->withCount(['enrolledCourses' => function($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        }])
        ->paginate(20);

        return view('instructor.students.index', compact('students'));
    }

    /**
     * Show lessons for a specific course.
     */
    public function lessons(Course $course)
    {
        $this->authorize('update', $course);

        $lessons = Lesson::where('course_id', $course->id)
            ->orderBy('order')
            ->paginate(20);

        return view('instructor.lessons.index', compact('course', 'lessons'));
    }

    /**
     * Show instructor analytics.
     */
    public function analytics()
    {
        $instructor = Auth::user();
        
        // Get comprehensive analytics data
        $analytics = [
            'overview' => $this->getOverviewAnalytics($instructor->id),
            'revenue' => $this->getRevenueAnalytics($instructor->id),
            'students' => $this->getStudentAnalytics($instructor->id),
            'courses' => $this->getCourseAnalytics($instructor->id),
        ];

        return view('instructor.analytics.index', compact('analytics'));
    }

    /**
     * Show analytics for a specific course.
     */
    public function courseAnalytics(Course $course)
    {
        $this->authorize('viewAnalytics', $course);

        $analytics = [
            'enrollments' => $this->getCourseEnrollmentData($course->id),
            'completion' => $this->getCourseCompletionData($course->id),
            'revenue' => $this->getCourseRevenueData($course->id),
            'engagement' => $this->getCourseEngagementData($course->id),
        ];

        return view('instructor.courses.analytics', compact('course', 'analytics'));
    }

    /**
     * Show instructor earnings.
     */
    public function earnings()
    {
        $instructor = Auth::user();
        
        $earnings = [
            'total' => $this->calculateInstructorRevenue($instructor->id),
            'this_month' => $this->calculateMonthlyRevenue($instructor->id),
            'pending' => $this->calculatePendingEarnings($instructor->id),
            'paid' => $this->calculatePaidEarnings($instructor->id),
        ];

        $payments = Payment::whereHas('course', function($query) use ($instructor) {
            $query->where('instructor_id', $instructor->id);
        })
        ->with(['course', 'user'])
        ->latest()
        ->paginate(20);

        return view('instructor.earnings.index', compact('earnings', 'payments'));
    }

    /**
     * Show instructor's streams.
     */
    public function streams()
    {
        $instructor = Auth::user();
        
        // This would integrate with a streaming service
        $streams = collect(); // Placeholder for now

        return view('instructor.streams.index', compact('streams'));
    }

    /**
     * Publish a course.
     */
    public function publishCourse(Course $course)
    {
        $this->authorize('publish', $course);

        $course->update(['status' => 'published']);

        return redirect()->back()->with('success', 'Course published successfully!');
    }

    /**
     * Unpublish a course.
     */
    public function unpublishCourse(Course $course)
    {
        $this->authorize('publish', $course);

        $course->update(['status' => 'draft']);

        return redirect()->back()->with('success', 'Course unpublished successfully!');
    }

    /**
     * Calculate instructor's total revenue.
     */
    private function calculateInstructorRevenue($instructorId)
    {
        return Payment::whereHas('course', function($query) use ($instructorId) {
            $query->where('instructor_id', $instructorId);
        })
        ->where('status', 'completed')
        ->sum('amount');
    }

    /**
     * Calculate instructor's monthly revenue.
     */
    private function calculateMonthlyRevenue($instructorId)
    {
        return Payment::whereHas('course', function($query) use ($instructorId) {
            $query->where('instructor_id', $instructorId);
        })
        ->where('status', 'completed')
        ->whereMonth('created_at', now()->month)
        ->whereYear('created_at', now()->year)
        ->sum('amount');
    }

    /**
     * Calculate pending earnings.
     */
    private function calculatePendingEarnings($instructorId)
    {
        return Payment::whereHas('course', function($query) use ($instructorId) {
            $query->where('instructor_id', $instructorId);
        })
        ->where('status', 'pending')
        ->sum('amount');
    }

    /**
     * Calculate paid earnings.
     */
    private function calculatePaidEarnings($instructorId)
    {
        return Payment::whereHas('course', function($query) use ($instructorId) {
            $query->where('instructor_id', $instructorId);
        })
        ->where('status', 'paid')
        ->sum('amount');
    }

    /**
     * Get revenue chart data.
     */
    private function getRevenueChartData($instructorId)
    {
        $data = Payment::whereHas('course', function($query) use ($instructorId) {
            $query->where('instructor_id', $instructorId);
        })
        ->where('status', 'completed')
        ->where('created_at', '>=', now()->subDays(30))
        ->selectRaw('DATE(created_at) as date, SUM(amount) as total')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        return $data->pluck('total', 'date');
    }

    /**
     * Get overview analytics.
     */
    private function getOverviewAnalytics($instructorId)
    {
        return [
            'total_revenue' => $this->calculateInstructorRevenue($instructorId),
            'total_students' => CourseEnrollment::whereHas('course', function($query) use ($instructorId) {
                $query->where('instructor_id', $instructorId);
            })->distinct('user_id')->count(),
            'total_courses' => Course::where('instructor_id', $instructorId)->count(),
            'avg_rating' => Course::where('instructor_id', $instructorId)->avg('rating') ?? 0,
        ];
    }

    /**
     * Get revenue analytics.
     */
    private function getRevenueAnalytics($instructorId)
    {
        return [
            'monthly' => $this->calculateMonthlyRevenue($instructorId),
            'yearly' => Payment::whereHas('course', function($query) use ($instructorId) {
                $query->where('instructor_id', $instructorId);
            })
            ->where('status', 'completed')
            ->whereYear('created_at', now()->year)
            ->sum('amount'),
        ];
    }

    /**
     * Get student analytics.
     */
    private function getStudentAnalytics($instructorId)
    {
        return [
            'new_this_month' => CourseEnrollment::whereHas('course', function($query) use ($instructorId) {
                $query->where('instructor_id', $instructorId);
            })
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count(),
        ];
    }

    /**
     * Get course analytics.
     */
    private function getCourseAnalytics($instructorId)
    {
        $courses = Course::where('instructor_id', $instructorId)->get();
        
        return [
            'published' => $courses->where('status', 'published')->count(),
            'draft' => $courses->where('status', 'draft')->count(),
            'avg_enrollments' => $courses->avg('enrollments_count') ?? 0,
        ];
    }

    /**
     * Get course enrollment data.
     */
    private function getCourseEnrollmentData($courseId)
    {
        return CourseEnrollment::where('course_id', $courseId)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date');
    }

    /**
     * Get course completion data.
     */
    private function getCourseCompletionData($courseId)
    {
        $total = CourseEnrollment::where('course_id', $courseId)->count();
        $completed = CourseEnrollment::where('course_id', $courseId)
            ->where('completed_at', '!=', null)
            ->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'completion_rate' => $total > 0 ? ($completed / $total) * 100 : 0,
        ];
    }

    /**
     * Get course revenue data.
     */
    private function getCourseRevenueData($courseId)
    {
        return Payment::where('course_id', $courseId)
            ->where('status', 'completed')
            ->selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('total', 'date');
    }

    /**
     * Get course engagement data.
     */
    private function getCourseEngagementData($courseId)
    {
        // This would include lesson views, time spent, etc.
        // Placeholder for now
        return [
            'avg_time_spent' => 0,
            'lesson_completion_rate' => 0,
        ];
    }
}
