{"version": 1, "defects": {"Tests\\Feature\\CourseManagementTest::users_can_view_courses_index": 8, "Tests\\Feature\\CourseManagementTest::users_can_view_course_details": 8, "Tests\\Feature\\CourseManagementTest::instructors_can_create_courses": 8, "Tests\\Feature\\CourseManagementTest::students_cannot_create_courses": 8, "Tests\\Feature\\CourseManagementTest::users_can_enroll_in_free_courses": 8, "Tests\\Feature\\CourseManagementTest::users_cannot_enroll_in_paid_courses_without_payment": 8, "Tests\\Feature\\CourseManagementTest::course_search_works_correctly": 8, "Tests\\Feature\\CourseManagementTest::course_filtering_by_category_works": 8, "Tests\\Feature\\CourseManagementTest::instructors_can_add_lessons_to_their_courses": 8, "Tests\\Feature\\CourseManagementTest::only_course_instructors_can_edit_their_courses": 8}, "times": {"Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.006, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.052, "Tests\\Feature\\PaymentSystemTest::test_example": 0.003, "Tests\\Feature\\UserAuthenticationTest::test_example": 0.005}}