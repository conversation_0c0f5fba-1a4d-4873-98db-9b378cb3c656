/* Instructor Layout Styles */
.instructor-body {
    margin: 0;
    padding: 0;
    font-family: 'Figtree', sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Instructor Sidebar */
.instructor-sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
}

.sidebar-collapsed .instructor-sidebar {
    width: 80px;
}

.sidebar-header {
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-collapsed .sidebar-header {
    padding: 1rem;
}

.sidebar-logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: #ffffff;
}

.sidebar-logo p {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin: 0;
}

.sidebar-collapsed .sidebar-logo h2,
.sidebar-collapsed .sidebar-logo p {
    display: none;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #a0a0a0;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.nav-link.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border-right: 3px solid #22c55e;
}

.nav-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
    width: 24px;
    text-align: center;
}

.sidebar-collapsed .nav-text {
    display: none;
}

.nav-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 0;
}

.nav-form {
    margin: 0;
}

.nav-logout {
    color: #ef4444;
}

.nav-logout:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Main Content Area */
.instructor-main {
    flex: 1;
    margin-left: 280px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar-collapsed .instructor-main {
    margin-left: 80px;
}

/* Top Bar */
.instructor-topbar {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hamburger {
    display: block;
    width: 20px;
    height: 2px;
    background: currentColor;
    position: relative;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: currentColor;
    transition: all 0.3s ease;
}

.hamburger::before {
    top: -6px;
}

.hamburger::after {
    top: 6px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #a0a0a0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #a0a0a0;
}

.breadcrumb-separator {
    color: #666;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Quick Stats */
.quick-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.75rem;
    color: #a0a0a0;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: #22c55e;
}

.instructor-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #ffffff;
}

.user-role {
    font-size: 0.75rem;
    color: #a0a0a0;
}

/* Content Area */
.instructor-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    position: relative;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #f59e0b;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: #3b82f6;
}

.alert-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-content strong {
    display: block;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-close {
    background: none;
    border: none;
    color: currentColor;
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.alert-close:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instructor-sidebar {
        transform: translateX(-100%);
    }
    
    .instructor-main {
        margin-left: 0;
    }
    
    .sidebar-collapsed .instructor-sidebar {
        transform: translateX(0);
        width: 280px;
    }
    
    .instructor-topbar {
        padding: 1rem;
    }
    
    .instructor-content {
        padding: 1rem;
    }
    
    .quick-stats {
        display: none;
    }
    
    .user-info {
        display: none;
    }
}

/* Mobile Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-overlay.active {
    display: block;
    opacity: 1;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 1024px) {
    .instructor-sidebar {
        z-index: 1001;
    }

    .instructor-sidebar.open {
        transform: translateX(0);
    }
}

/* Additional Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 10000;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
