@extends('layouts.minimal')

@section('title', 'Press')

@section('content')
<div class="press-page">
    <div class="page-header">
        <h1>📰 Press</h1>
        <p class="page-subtitle">Media resources and press information</p>
    </div>

    <div class="coming-soon">
        <div class="coming-soon-icon">📺</div>
        <h2>Press Kit Coming Soon</h2>
        <p>We're preparing comprehensive press materials including company information, media assets, and press releases.</p>
        
        <div class="press-contact">
            <h3>Media Inquiries</h3>
            <p>For press inquiries, interviews, or media requests, please contact:</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Response Time:</strong> Within 24 hours</p>
        </div>
        
        <div class="company-info">
            <h3>Quick Facts</h3>
            <ul>
                <li>🌍 <strong>Founded:</strong> 2024</li>
                <li>🎯 <strong>Mission:</strong> Escape the matrix through education</li>
                <li>👥 <strong>Community:</strong> 50,000+ active members</li>
                <li>💰 <strong>Student Earnings:</strong> $10M+ generated</li>
                <li>📚 <strong>Courses:</strong> Real-world business education</li>
            </ul>
        </div>
    </div>
</div>

<style>
.press-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.coming-soon {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
}

.coming-soon-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.press-contact, .company-info {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.press-contact h3, .company-info h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.company-info ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.company-info li {
    padding: 0.5rem 0;
    color: #475569;
}
</style>
@endsection
