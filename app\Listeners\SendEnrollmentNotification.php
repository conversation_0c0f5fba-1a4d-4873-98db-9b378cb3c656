<?php

namespace App\Listeners;

use App\Events\CourseEnrolled;
use App\Models\Notification;
use App\Notifications\CourseEnrollmentNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendEnrollmentNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CourseEnrolled $event): void
    {
        try {
            // Create notification for the student
            Notification::create([
                'user_id' => $event->user->id,
                'type' => 'course_enrollment',
                'title' => 'Course Enrollment Successful',
                'message' => "You have successfully enrolled in '{$event->course->title}'",
                'data' => [
                    'course_id' => $event->course->id,
                    'course_title' => $event->course->title,
                    'instructor_name' => $event->course->instructor->name,
                    'enrollment_date' => now()->toDateString(),
                ],
                'action_url' => route('courses.show', $event->course),
            ]);

            // Create notification for the instructor
            Notification::create([
                'user_id' => $event->course->instructor_id,
                'type' => 'new_student',
                'title' => 'New Student Enrolled',
                'message' => "{$event->user->name} has enrolled in your course '{$event->course->title}'",
                'data' => [
                    'student_id' => $event->user->id,
                    'student_name' => $event->user->name,
                    'course_id' => $event->course->id,
                    'course_title' => $event->course->title,
                    'enrollment_date' => now()->toDateString(),
                ],
                'action_url' => route('courses.show', $event->course),
            ]);

            // Send email notification to student
            $event->user->notify(new CourseEnrollmentNotification($event->course));

            Log::info('Enrollment notifications sent', [
                'user_id' => $event->user->id,
                'course_id' => $event->course->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send enrollment notification', [
                'user_id' => $event->user->id,
                'course_id' => $event->course->id,
                'error' => $e->getMessage(),
            ]);

            // Re-throw the exception to retry the job
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(CourseEnrolled $event, \Throwable $exception): void
    {
        Log::error('Enrollment notification job failed', [
            'user_id' => $event->user->id,
            'course_id' => $event->course->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
