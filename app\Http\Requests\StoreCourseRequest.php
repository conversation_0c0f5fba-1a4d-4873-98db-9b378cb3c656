<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::user()->hasRole(['instructor', 'admin', 'super-admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:course_categories,id',
            'price' => 'required|numeric|min:0|max:9999.99',
            'difficulty' => 'required|in:beginner,intermediate,advanced',
            'duration' => 'nullable|integer|min:1',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_preview' => 'nullable|mimes:mp4,mov,avi,wmv|max:51200', // 50MB
            'tags' => 'nullable|string|max:500',
            'requirements' => 'nullable|string|max:1000',
            'what_you_learn' => 'nullable|string|max:1000',
            'status' => 'nullable|in:draft,published,archived',
            'is_featured' => 'nullable|boolean',
            'max_students' => 'nullable|integer|min:1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Course title is required.',
            'title.max' => 'Course title cannot exceed 255 characters.',
            'description.required' => 'Course description is required.',
            'description.max' => 'Course description cannot exceed 2000 characters.',
            'category_id.required' => 'Please select a course category.',
            'category_id.exists' => 'Selected category does not exist.',
            'price.required' => 'Course price is required.',
            'price.numeric' => 'Course price must be a valid number.',
            'price.min' => 'Course price cannot be negative.',
            'price.max' => 'Course price cannot exceed $9,999.99.',
            'difficulty.required' => 'Please select course difficulty level.',
            'difficulty.in' => 'Invalid difficulty level selected.',
            'thumbnail.image' => 'Thumbnail must be an image file.',
            'thumbnail.mimes' => 'Thumbnail must be a JPEG, PNG, JPG, or GIF file.',
            'thumbnail.max' => 'Thumbnail size cannot exceed 2MB.',
            'video_preview.mimes' => 'Preview video must be MP4, MOV, AVI, or WMV format.',
            'video_preview.max' => 'Preview video size cannot exceed 50MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => 'category',
            'what_you_learn' => 'learning outcomes',
            'max_students' => 'maximum students',
        ];
    }
}
