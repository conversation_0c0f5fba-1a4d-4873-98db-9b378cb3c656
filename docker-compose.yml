# The Real World LMS - Docker Compose Configuration
version: '3.8'

services:
  # Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: the-real-world-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./public/uploads:/var/www/html/public/uploads
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_DATABASE=the_real_world_lms
      - DB_USERNAME=laravel
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - mysql
      - redis
    networks:
      - the-real-world-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: the-real-world-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: the_real_world_lms
      MYSQL_USER: laravel
      MYSQL_PASSWORD: secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - the-real-world-network

  # Redis Cache & Sessions
  redis:
    image: redis:7-alpine
    container_name: the-real-world-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - the-real-world-network

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: the-real-world-queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - DB_DATABASE=the_real_world_lms
      - DB_USERNAME=laravel
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - mysql
      - redis
    networks:
      - the-real-world-network

  # Scheduler (Cron)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: the-real-world-scheduler
    restart: unless-stopped
    command: sh -c "while true; do php artisan schedule:run; sleep 60; done"
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - DB_DATABASE=the_real_world_lms
      - DB_USERNAME=laravel
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - the-real-world-network

  # Nginx Load Balancer (for scaling)
  nginx:
    image: nginx:alpine
    container_name: the-real-world-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./public:/var/www/html/public
    depends_on:
      - app
    networks:
      - the-real-world-network

  # Elasticsearch (for search functionality)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: the-real-world-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - the-real-world-network

  # Meilisearch (alternative search engine)
  meilisearch:
    image: getmeili/meilisearch:latest
    container_name: the-real-world-meilisearch
    restart: unless-stopped
    ports:
      - "7700:7700"
    environment:
      - MEILI_MASTER_KEY=your_master_key_here
      - MEILI_ENV=production
    volumes:
      - meilisearch_data:/meili_data
    networks:
      - the-real-world-network

  # MinIO (S3-compatible object storage)
  minio:
    image: minio/minio:latest
    container_name: the-real-world-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - the-real-world-network

  # Mailhog (for email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: the-real-world-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - the-real-world-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: the-real-world-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - the-real-world-network

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: the-real-world-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - the-real-world-network

  # Backup service
  backup:
    image: alpine:latest
    container_name: the-real-world-backup
    restart: unless-stopped
    volumes:
      - mysql_data:/backup/mysql
      - ./storage:/backup/storage
      - ./docker/backup:/scripts
    command: sh -c "while true; do /scripts/backup.sh; sleep 86400; done"
    depends_on:
      - mysql
    networks:
      - the-real-world-network

# Networks
networks:
  the-real-world-network:
    driver: bridge

# Volumes
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  meilisearch_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
