<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Admin Dashboard - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/admin.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="admin-body">
    <!-- Admin Sidebar -->
    <div class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
            <p>The Real World</p>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}" class="active">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <a href="{{ route('dashboard') }}" class="btn btn-secondary btn-sm">
                ← Back to Site
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Bar -->
        <div class="admin-topbar">
            <div class="topbar-left">
                <h1>Dashboard Overview</h1>
                <p>Welcome back, {{ Auth::user()->name }}!</p>
            </div>
            <div class="topbar-right">
                <div class="system-status" id="system-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">Checking...</span>
                </div>
                <div class="user-menu">
                    <span>{{ Auth::user()->name }}</span>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-link">Logout</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon">👥</div>
                <div class="metric-content">
                    <h3>{{ number_format($metrics['total_users']['value']) }}</h3>
                    <p>Total Users</p>
                    <div class="metric-change {{ $metrics['total_users']['trend'] }}">
                        {{ $metrics['total_users']['change'] > 0 ? '+' : '' }}{{ $metrics['total_users']['change'] }}%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">📚</div>
                <div class="metric-content">
                    <h3>{{ number_format($metrics['total_courses']['value']) }}</h3>
                    <p>Active Courses</p>
                    <div class="metric-change {{ $metrics['total_courses']['trend'] }}">
                        {{ $metrics['total_courses']['change'] > 0 ? '+' : '' }}{{ $metrics['total_courses']['change'] }}%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-content">
                    <h3>${{ number_format($metrics['total_revenue']['value'], 2) }}</h3>
                    <p>Total Revenue</p>
                    <div class="metric-change {{ $metrics['total_revenue']['trend'] }}">
                        {{ $metrics['total_revenue']['change'] > 0 ? '+' : '' }}{{ $metrics['total_revenue']['change'] }}%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">🎯</div>
                <div class="metric-content">
                    <h3>{{ number_format($metrics['active_enrollments']['value']) }}</h3>
                    <p>Active Enrollments</p>
                    <div class="metric-change {{ $metrics['active_enrollments']['trend'] }}">
                        {{ $metrics['active_enrollments']['change'] > 0 ? '+' : '' }}{{ $metrics['active_enrollments']['change'] }}%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">📅</div>
                <div class="metric-content">
                    <h3>${{ number_format($metrics['monthly_revenue']['value'], 2) }}</h3>
                    <p>Monthly Revenue</p>
                    <div class="metric-change {{ $metrics['monthly_revenue']['trend'] }}">
                        {{ $metrics['monthly_revenue']['change'] > 0 ? '+' : '' }}{{ $metrics['monthly_revenue']['change'] }}%
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-icon">✅</div>
                <div class="metric-content">
                    <h3>{{ $metrics['completion_rate']['value'] }}%</h3>
                    <p>Completion Rate</p>
                    <div class="metric-change {{ $metrics['completion_rate']['trend'] }}">
                        {{ $metrics['completion_rate']['change'] > 0 ? '+' : '' }}{{ $metrics['completion_rate']['change'] }}%
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Revenue Trend (Last 30 Days)</h3>
                </div>
                <canvas id="revenueChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>User Growth (Last 12 Months)</h3>
                </div>
                <canvas id="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Activities -->
            <div class="content-card">
                <div class="card-header">
                    <h3>Recent Activities</h3>
                    <a href="#" class="view-all">View All</a>
                </div>
                <div class="activities-list">
                    @forelse($recentActivities as $activity)
                        <div class="activity-item">
                            <div class="activity-icon {{ $activity['color'] }}">
                                {{ $activity['icon'] }}
                            </div>
                            <div class="activity-content">
                                <h4>{{ $activity['title'] }}</h4>
                                <p>{{ $activity['description'] }}</p>
                                <span class="activity-time">{{ $activity['time']->diffForHumans() }}</span>
                            </div>
                        </div>
                    @empty
                        <p class="no-activities">No recent activities</p>
                    @endforelse
                </div>
            </div>

            <!-- Top Courses -->
            <div class="content-card">
                <div class="card-header">
                    <h3>Top Performing Courses</h3>
                    <a href="{{ route('admin.courses.index') }}" class="view-all">View All</a>
                </div>
                <div class="courses-list">
                    @forelse($topCourses as $course)
                        <div class="course-item">
                            <div class="course-info">
                                <h4>{{ $course['title'] }}</h4>
                                <p>by {{ $course['instructor'] }}</p>
                            </div>
                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-value">{{ $course['students'] }}</span>
                                    <span class="stat-label">Students</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value">{{ $course['rating'] }}</span>
                                    <span class="stat-label">Rating</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value">${{ number_format($course['revenue']) }}</span>
                                    <span class="stat-label">Revenue</span>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="no-courses">No courses available</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="actions-grid">
                <a href="{{ route('admin.users.create') }}" class="action-btn">
                    <span class="action-icon">👤</span>
                    <span class="action-text">Add User</span>
                </a>
                <a href="{{ route('admin.courses.create') }}" class="action-btn">
                    <span class="action-icon">📚</span>
                    <span class="action-text">Create Course</span>
                </a>
                <a href="{{ route('admin.payments.index') }}" class="action-btn">
                    <span class="action-icon">💰</span>
                    <span class="action-text">View Payments</span>
                </a>
                <a href="{{ route('admin.analytics') }}" class="action-btn">
                    <span class="action-icon">📊</span>
                    <span class="action-text">Analytics</span>
                </a>
            </div>
        </div>
    </div>

    <script src="{{ asset('assets/js/admin.js') }}"></script>
    <script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            checkSystemStatus();
        });

        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: {!! json_encode($revenueData->pluck('date')) !!},
                    datasets: [{
                        label: 'Revenue ($)',
                        data: {!! json_encode($revenueData->pluck('revenue')) !!},
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value;
                                }
                            }
                        }
                    }
                }
            });

            // User Growth Chart
            const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
            new Chart(userGrowthCtx, {
                type: 'bar',
                data: {
                    labels: {!! json_encode($userGrowthData->pluck('month')) !!},
                    datasets: [{
                        label: 'New Users',
                        data: {!! json_encode($userGrowthData->pluck('users')) !!},
                        backgroundColor: '#10b981',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function checkSystemStatus() {
            fetch('{{ route("admin.system-status") }}')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('system-status');
                    const indicator = statusElement.querySelector('.status-indicator');
                    const text = statusElement.querySelector('.status-text');
                    
                    const allHealthy = Object.values(data).every(status => status.status === 'healthy');
                    
                    if (allHealthy) {
                        indicator.className = 'status-indicator healthy';
                        text.textContent = 'All Systems Operational';
                    } else {
                        indicator.className = 'status-indicator warning';
                        text.textContent = 'System Issues Detected';
                    }
                })
                .catch(error => {
                    console.error('System status check failed:', error);
                    const statusElement = document.getElementById('system-status');
                    const indicator = statusElement.querySelector('.status-indicator');
                    const text = statusElement.querySelector('.status-text');
                    
                    indicator.className = 'status-indicator error';
                    text.textContent = 'Status Check Failed';
                });
        }
    </script>
</body>
</html>
