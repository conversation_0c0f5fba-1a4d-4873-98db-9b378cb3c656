<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class InstructorAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'You must be logged in to access instructor features.');
        }

        $user = auth()->user();

        // Check if user is an instructor or has higher privileges
        if (!$user->hasAnyRole(['instructor', 'admin', 'super-admin'])) {
            abort(403, 'You must be an instructor to access this feature.');
        }

        return $next($request);
    }
}
