<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Payment;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:manage users');
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with(['roles', 'enrolledCourses'])
            ->withCount(['payments', 'enrolledCourses']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Role filter
        if ($request->has('role') && $request->role) {
            $query->whereHas('roles', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active');
        }

        // Date range filter
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);
        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_active' => $request->has('is_active'),
            'email_verified_at' => now(), // Auto-verify admin created users
        ]);

        // Assign roles
        if ($request->has('roles')) {
            $user->assignRole($request->roles);
        }

        return redirect()->route('admin.users.index')
                        ->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load([
            'roles',
            'enrolledCourses.course',
            'payments.course',
            'createdCourses',
            'communities',
            'chatRooms'
        ]);

        // Get user statistics
        $stats = [
            'total_spent' => $user->payments()->where('status', 'completed')->sum('amount'),
            'courses_completed' => $user->enrolledCourses()->whereNotNull('completed_at')->count(),
            'avg_progress' => $user->enrolledCourses()->avg('progress') ?? 0,
            'last_login' => $user->last_login_at,
            'account_age' => $user->created_at->diffInDays(now()),
        ];

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the user.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();

        return view('admin.users.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'is_active' => $request->has('is_active'),
        ];

        // Update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // Update roles
        $user->syncRoles($request->roles ?? []);

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of super admin
        if ($user->hasRole('super-admin')) {
            return redirect()->back()
                           ->with('error', 'Cannot delete super admin user.');
        }

        // Prevent self-deletion
        if ($user->id === auth()->id()) {
            return redirect()->back()
                           ->with('error', 'Cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', 'User deleted successfully!');
    }

    /**
     * Toggle user active status.
     */
    public function toggleStatus(User $user)
    {
        // Prevent deactivating super admin
        if ($user->hasRole('super-admin') && $user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot deactivate super admin user.'
            ], 400);
        }

        // Prevent self-deactivation
        if ($user->id === auth()->id() && $user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot deactivate your own account.'
            ], 400);
        }

        $user->update(['is_active' => !$user->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully!',
            'is_active' => $user->is_active
        ]);
    }

    /**
     * Impersonate a user.
     */
    public function impersonate(User $user)
    {
        // Prevent impersonating super admin
        if ($user->hasRole('super-admin')) {
            return redirect()->back()
                           ->with('error', 'Cannot impersonate super admin user.');
        }

        // Store original user ID in session
        session(['impersonate_original_user' => auth()->id()]);

        // Login as the target user
        auth()->login($user);

        return redirect()->route('dashboard')
                        ->with('info', 'You are now impersonating ' . $user->name);
    }

    /**
     * Stop impersonating and return to original user.
     */
    public function stopImpersonating()
    {
        $originalUserId = session('impersonate_original_user');

        if (!$originalUserId) {
            return redirect()->route('dashboard')
                           ->with('error', 'No impersonation session found.');
        }

        $originalUser = User::find($originalUserId);

        if (!$originalUser) {
            return redirect()->route('login')
                           ->with('error', 'Original user not found.');
        }

        // Clear impersonation session
        session()->forget('impersonate_original_user');

        // Login as original user
        auth()->login($originalUser);

        return redirect()->route('admin.users.index')
                        ->with('success', 'Stopped impersonating user.');
    }

    /**
     * Export users data.
     */
    public function export(Request $request)
    {
        $query = User::with(['roles', 'enrolledCourses'])
            ->withCount(['payments', 'enrolledCourses']);

        // Apply same filters as index
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->has('role') && $request->role) {
            $query->whereHas('roles', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->get();

        // Generate CSV
        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Roles', 'Status', 'Courses Enrolled',
                'Total Payments', 'Created At', 'Last Login'
            ]);

            // CSV data
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->roles->pluck('name')->implode(', '),
                    $user->is_active ? 'Active' : 'Inactive',
                    $user->enrolled_courses_count,
                    $user->payments_count,
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
