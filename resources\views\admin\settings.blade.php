@extends('layouts.app')

@section('title', 'System Settings - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}" class="active">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>System Settings</h1>
                    <p>Configure platform settings and preferences</p>
                </div>
                <div class="header-actions">
                    <button type="button" class="btn btn-secondary" onclick="resetToDefaults()">
                        🔄 Reset to Defaults
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                        💾 Save All Changes
                    </button>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <div class="settings-container">
                <!-- Settings Navigation -->
                <div class="settings-nav">
                    <button class="settings-tab active" data-tab="general">
                        <span class="tab-icon">🏢</span>
                        <span class="tab-text">General</span>
                    </button>
                    <button class="settings-tab" data-tab="payment">
                        <span class="tab-icon">💳</span>
                        <span class="tab-text">Payment</span>
                    </button>
                    <button class="settings-tab" data-tab="email">
                        <span class="tab-icon">📧</span>
                        <span class="tab-text">Email</span>
                    </button>
                    <button class="settings-tab" data-tab="security">
                        <span class="tab-icon">🔒</span>
                        <span class="tab-text">Security</span>
                    </button>
                    <button class="settings-tab" data-tab="features">
                        <span class="tab-icon">🚀</span>
                        <span class="tab-text">Features</span>
                    </button>
                    <button class="settings-tab" data-tab="maintenance">
                        <span class="tab-icon">🔧</span>
                        <span class="tab-text">Maintenance</span>
                    </button>
                </div>

                <!-- Settings Content -->
                <div class="settings-content">
                    <!-- General Settings -->
                    <div class="settings-panel active" id="general-panel">
                        <div class="panel-header">
                            <h2>General Settings</h2>
                            <p>Basic platform configuration and branding</p>
                        </div>

                        <form class="settings-form" id="general-form">
                            <div class="form-section">
                                <h3>Platform Information</h3>

                                <div class="form-group">
                                    <label for="site_name" class="form-label">Site Name</label>
                                    <input type="text" id="site_name" name="site_name" class="form-control"
                                           value="{{ config('app.name', 'The Real World') }}">
                                </div>

                                <div class="form-group">
                                    <label for="site_description" class="form-label">Site Description</label>
                                    <textarea id="site_description" name="site_description" class="form-control" rows="3">Escape the matrix and build real wealth through proven strategies and elite mentorship.</textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="site_url" class="form-label">Site URL</label>
                                        <input type="url" id="site_url" name="site_url" class="form-control"
                                               value="{{ config('app.url') }}">
                                    </div>
                                    <div class="form-group">
                                        <label for="admin_email" class="form-label">Admin Email</label>
                                        <input type="email" id="admin_email" name="admin_email" class="form-control"
                                               value="<EMAIL>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Branding & Appearance</h3>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="logo_upload" class="form-label">Site Logo</label>
                                        <div class="file-upload-area">
                                            <input type="file" id="logo_upload" name="logo" accept="image/*" class="file-input">
                                            <div class="upload-placeholder">
                                                <span class="upload-icon">🖼️</span>
                                                <span class="upload-text">Click to upload logo</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="favicon_upload" class="form-label">Favicon</label>
                                        <div class="file-upload-area">
                                            <input type="file" id="favicon_upload" name="favicon" accept="image/*" class="file-input">
                                            <div class="upload-placeholder">
                                                <span class="upload-icon">⭐</span>
                                                <span class="upload-text">Click to upload favicon</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Theme Settings</label>
                                    <div class="theme-options">
                                        <div class="theme-option">
                                            <input type="radio" id="theme_dark" name="theme" value="dark" checked>
                                            <label for="theme_dark" class="theme-label">
                                                <div class="theme-preview dark"></div>
                                                <span>Dark Theme</span>
                                            </label>
                                        </div>
                                        <div class="theme-option">
                                            <input type="radio" id="theme_light" name="theme" value="light">
                                            <label for="theme_light" class="theme-label">
                                                <div class="theme-preview light"></div>
                                                <span>Light Theme</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Localization</h3>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="timezone" class="form-label">Default Timezone</label>
                                        <select id="timezone" name="timezone" class="form-control">
                                            <option value="UTC">UTC</option>
                                            <option value="America/New_York">Eastern Time</option>
                                            <option value="America/Chicago">Central Time</option>
                                            <option value="America/Denver">Mountain Time</option>
                                            <option value="America/Los_Angeles" selected>Pacific Time</option>
                                            <option value="Europe/London">London</option>
                                            <option value="Europe/Paris">Paris</option>
                                            <option value="Asia/Tokyo">Tokyo</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="currency" class="form-label">Default Currency</label>
                                        <select id="currency" name="currency" class="form-control">
                                            <option value="USD" selected>USD - US Dollar</option>
                                            <option value="EUR">EUR - Euro</option>
                                            <option value="GBP">GBP - British Pound</option>
                                            <option value="CAD">CAD - Canadian Dollar</option>
                                            <option value="AUD">AUD - Australian Dollar</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Payment Settings -->
                    <div class="settings-panel" id="payment-panel">
                        <div class="panel-header">
                            <h2>Payment Settings</h2>
                            <p>Configure payment methods and processing</p>
                        </div>

                        <form class="settings-form" id="payment-form">
                            <div class="form-section">
                                <h3>Stripe Configuration</h3>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="stripe_enabled" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Stripe Payments</span>
                                    </label>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="stripe_public_key" class="form-label">Stripe Publishable Key</label>
                                        <input type="text" id="stripe_public_key" name="stripe_public_key" class="form-control"
                                               placeholder="pk_test_...">
                                    </div>
                                    <div class="form-group">
                                        <label for="stripe_secret_key" class="form-label">Stripe Secret Key</label>
                                        <input type="password" id="stripe_secret_key" name="stripe_secret_key" class="form-control"
                                               placeholder="sk_test_...">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="stripe_webhook_secret" class="form-label">Webhook Secret</label>
                                    <input type="password" id="stripe_webhook_secret" name="stripe_webhook_secret" class="form-control"
                                           placeholder="whsec_...">
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Cryptocurrency Settings</h3>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="crypto_enabled" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Cryptocurrency Payments</span>
                                    </label>
                                </div>

                                <div class="crypto-options">
                                    <div class="crypto-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="bitcoin_enabled" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">₿ Bitcoin (BTC)</span>
                                        </label>
                                    </div>
                                    <div class="crypto-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="ethereum_enabled" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">Ξ Ethereum (ETH)</span>
                                        </label>
                                    </div>
                                    <div class="crypto-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="usdt_enabled" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">💎 Tether (USDT)</span>
                                        </label>
                                    </div>
                                    <div class="crypto-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="usdc_enabled" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">💎 USD Coin (USDC)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Payment Processing</h3>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="payment_timeout" class="form-label">Payment Timeout (minutes)</label>
                                        <input type="number" id="payment_timeout" name="payment_timeout" class="form-control"
                                               value="30" min="5" max="120">
                                    </div>
                                    <div class="form-group">
                                        <label for="refund_period" class="form-label">Refund Period (days)</label>
                                        <input type="number" id="refund_period" name="refund_period" class="form-control"
                                               value="30" min="0" max="365">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Email Settings -->
                    <div class="settings-panel" id="email-panel">
                        <div class="panel-header">
                            <h2>Email Settings</h2>
                            <p>Configure email delivery and notifications</p>
                        </div>

                        <form class="settings-form" id="email-form">
                            <div class="form-section">
                                <h3>SMTP Configuration</h3>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="mail_host" class="form-label">SMTP Host</label>
                                        <input type="text" id="mail_host" name="mail_host" class="form-control"
                                               value="smtp.gmail.com">
                                    </div>
                                    <div class="form-group">
                                        <label for="mail_port" class="form-label">SMTP Port</label>
                                        <input type="number" id="mail_port" name="mail_port" class="form-control"
                                               value="587">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="mail_username" class="form-label">SMTP Username</label>
                                        <input type="text" id="mail_username" name="mail_username" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label for="mail_password" class="form-label">SMTP Password</label>
                                        <input type="password" id="mail_password" name="mail_password" class="form-control">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="mail_from_address" class="form-label">From Email Address</label>
                                    <input type="email" id="mail_from_address" name="mail_from_address" class="form-control"
                                           value="<EMAIL>">
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Notification Settings</h3>

                                <div class="notification-options">
                                    <div class="notification-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="welcome_email" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">Send Welcome Email to New Users</span>
                                        </label>
                                    </div>
                                    <div class="notification-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="enrollment_email" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">Send Course Enrollment Confirmations</span>
                                        </label>
                                    </div>
                                    <div class="notification-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="payment_email" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">Send Payment Confirmations</span>
                                        </label>
                                    </div>
                                    <div class="notification-option">
                                        <label class="toggle-label">
                                            <input type="checkbox" name="admin_notifications" checked>
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-text">Send Admin Notifications</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Security Settings -->
                    <div class="settings-panel" id="security-panel">
                        <div class="panel-header">
                            <h2>Security Settings</h2>
                            <p>Configure security and access controls</p>
                        </div>

                        <form class="settings-form" id="security-form">
                            <div class="form-section">
                                <h3>Authentication</h3>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="require_email_verification" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Require Email Verification</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="two_factor_auth">
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Two-Factor Authentication</span>
                                    </label>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                                        <input type="number" id="session_timeout" name="session_timeout" class="form-control"
                                               value="120" min="15" max="1440">
                                    </div>
                                    <div class="form-group">
                                        <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                                        <input type="number" id="max_login_attempts" name="max_login_attempts" class="form-control"
                                               value="5" min="3" max="10">
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Content Security</h3>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="content_moderation" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Content Moderation</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="spam_protection" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Spam Protection</span>
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Features Settings -->
                    <div class="settings-panel" id="features-panel">
                        <div class="panel-header">
                            <h2>Feature Settings</h2>
                            <p>Enable or disable platform features</p>
                        </div>

                        <form class="settings-form" id="features-form">
                            <div class="form-section">
                                <h3>Core Features</h3>

                                <div class="feature-grid">
                                    <div class="feature-card">
                                        <div class="feature-icon">📚</div>
                                        <div class="feature-info">
                                            <h4>Course System</h4>
                                            <p>Enable course creation and enrollment</p>
                                        </div>
                                        <label class="toggle-label">
                                            <input type="checkbox" name="courses_enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>

                                    <div class="feature-card">
                                        <div class="feature-icon">👥</div>
                                        <div class="feature-info">
                                            <h4>Community Forums</h4>
                                            <p>Enable community discussions and posts</p>
                                        </div>
                                        <label class="toggle-label">
                                            <input type="checkbox" name="community_enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>

                                    <div class="feature-card">
                                        <div class="feature-icon">💬</div>
                                        <div class="feature-info">
                                            <h4>Live Chat</h4>
                                            <p>Enable real-time messaging and chat rooms</p>
                                        </div>
                                        <label class="toggle-label">
                                            <input type="checkbox" name="chat_enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>

                                    <div class="feature-card">
                                        <div class="feature-icon">🔍</div>
                                        <div class="feature-info">
                                            <h4>Search System</h4>
                                            <p>Enable global search functionality</p>
                                        </div>
                                        <label class="toggle-label">
                                            <input type="checkbox" name="search_enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Maintenance Settings -->
                    <div class="settings-panel" id="maintenance-panel">
                        <div class="panel-header">
                            <h2>Maintenance Settings</h2>
                            <p>System maintenance and optimization</p>
                        </div>

                        <form class="settings-form" id="maintenance-form">
                            <div class="form-section">
                                <h3>Maintenance Mode</h3>

                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="maintenance_mode">
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Enable Maintenance Mode</span>
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label for="maintenance_message" class="form-label">Maintenance Message</label>
                                    <textarea id="maintenance_message" name="maintenance_message" class="form-control" rows="3">We're currently performing scheduled maintenance. Please check back soon!</textarea>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>System Cleanup</h3>

                                <div class="cleanup-actions">
                                    <div class="cleanup-action">
                                        <div class="action-info">
                                            <h4>Clear Cache</h4>
                                            <p>Clear application cache and optimize performance</p>
                                        </div>
                                        <button type="button" class="btn btn-secondary" onclick="clearCache()">
                                            🗑️ Clear Cache
                                        </button>
                                    </div>

                                    <div class="cleanup-action">
                                        <div class="action-info">
                                            <h4>Optimize Database</h4>
                                            <p>Optimize database tables and indexes</p>
                                        </div>
                                        <button type="button" class="btn btn-secondary" onclick="optimizeDatabase()">
                                            ⚡ Optimize DB
                                        </button>
                                    </div>

                                    <div class="cleanup-action">
                                        <div class="action-info">
                                            <h4>Clean Old Notifications</h4>
                                            <p>Remove notifications older than 30 days</p>
                                        </div>
                                        <button type="button" class="btn btn-secondary" onclick="cleanNotifications()">
                                            🧹 Clean Notifications
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.settings-nav {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.settings-tab {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.settings-tab:hover,
.settings-tab.active {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.tab-icon {
    font-size: 1.25rem;
}

.settings-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.settings-panel {
    display: none;
}

.settings-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h2 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.panel-header p {
    color: #a0a0a0;
    font-size: 1rem;
    margin: 0;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-text {
    color: #ffffff;
    font-weight: 500;
}

.file-upload-area {
    position: relative;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 2rem;
}

.upload-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.theme-options {
    display: flex;
    gap: 1rem;
}

.theme-option {
    flex: 1;
}

.theme-option input[type="radio"] {
    display: none;
}

.theme-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-option input[type="radio"]:checked + .theme-label {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-preview.dark {
    background: linear-gradient(135deg, #1f2937, #111827);
}

.theme-preview.light {
    background: linear-gradient(135deg, #f9fafb, #e5e7eb);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.feature-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.feature-icon {
    font-size: 2rem;
}

.feature-info {
    flex: 1;
}

.feature-info h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.feature-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.cleanup-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cleanup-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.action-info h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.action-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
    }

    .settings-nav {
        position: static;
        display: flex;
        overflow-x: auto;
        gap: 0.5rem;
    }

    .settings-tab {
        white-space: nowrap;
        margin-bottom: 0;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .cleanup-action {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
</style>

<script>
// Settings tab functionality
document.querySelectorAll('.settings-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.settings-tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.settings-panel').forEach(p => p.classList.remove('active'));

        // Add active class to clicked tab and corresponding panel
        this.classList.add('active');
        document.getElementById(this.dataset.tab + '-panel').classList.add('active');
    });
});

function saveAllSettings() {
    // Implementation for saving all settings
    console.log('Save all settings');
    alert('Settings saved successfully!');
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
        // Implementation for resetting to defaults
        console.log('Reset to defaults');
    }
}

function clearCache() {
    if (confirm('Clear application cache?')) {
        // Implementation for clearing cache
        console.log('Clear cache');
        alert('Cache cleared successfully!');
    }
}

function optimizeDatabase() {
    if (confirm('Optimize database? This may take a few minutes.')) {
        // Implementation for database optimization
        console.log('Optimize database');
        alert('Database optimized successfully!');
    }
}

function cleanNotifications() {
    if (confirm('Clean old notifications? This will remove notifications older than 30 days.')) {
        // Implementation for cleaning notifications
        console.log('Clean notifications');
        alert('Old notifications cleaned successfully!');
    }
}
</script>
@endsection