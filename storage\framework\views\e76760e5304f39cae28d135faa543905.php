<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', config('app.name')); ?></title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/minimal.css')); ?>" rel="stylesheet">
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="minimal-body">
    <!-- Minimal Header -->
    <header class="minimal-header">
        <div class="header-container">
            <div class="header-brand">
                <a href="<?php echo e(route('home')); ?>" class="brand-link">
                    <span class="brand-icon">🌍</span>
                    <span class="brand-text">The Real World</span>
                </a>
            </div>
            
            <?php if(!isset($hideNavigation) || !$hideNavigation): ?>
                <nav class="header-nav">
                    <?php if(auth()->guard()->guest()): ?>
                        <a href="<?php echo e(route('login')); ?>" class="nav-link">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="nav-link primary">Register</a>
                    <?php else: ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="nav-link">Dashboard</a>
                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline-form">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="nav-link logout">Logout</button>
                        </form>
                    <?php endif; ?>
                </nav>
            <?php endif; ?>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="minimal-main">
        <!-- Flash Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success">
                <div class="alert-icon">✅</div>
                <div class="alert-content"><?php echo e(session('success')); ?></div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-error">
                <div class="alert-icon">❌</div>
                <div class="alert-content"><?php echo e(session('error')); ?></div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        <?php endif; ?>

        <?php if(session('warning')): ?>
            <div class="alert alert-warning">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content"><?php echo e(session('warning')); ?></div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        <?php endif; ?>

        <?php if(session('info')): ?>
            <div class="alert alert-info">
                <div class="alert-icon">ℹ️</div>
                <div class="alert-content"><?php echo e(session('info')); ?></div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-error">
                <div class="alert-icon">❌</div>
                <div class="alert-content">
                    <ul class="error-list">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- Minimal Footer -->
    <?php if(!isset($hideFooter) || !$hideFooter): ?>
        <footer class="minimal-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <p>&copy; <?php echo e(date('Y')); ?> The Real World. All rights reserved.</p>
                    <div class="footer-links">
                        <a href="<?php echo e(route('privacy')); ?>">Privacy</a>
                        <a href="<?php echo e(route('terms')); ?>">Terms</a>
                        <a href="<?php echo e(route('contact')); ?>">Contact</a>
                    </div>
                </div>
            </div>
        </footer>
    <?php endif; ?>
    
    <!-- Custom JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/minimal.blade.php ENDPATH**/ ?>