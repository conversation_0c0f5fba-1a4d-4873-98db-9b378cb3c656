<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($course->title); ?> - <?php echo e(config('app.name')); ?></title>
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/course-detail.css')); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="<?php echo e(route('home')); ?>">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li><a href="<?php echo e(route('courses.index')); ?>">Courses</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="#chat">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Course Hero -->
    <section class="course-hero">
        <div class="container">
            <div class="course-hero-content">
                <div class="course-info">
                    <div class="course-breadcrumb">
                        <a href="<?php echo e(route('courses.index')); ?>">Courses</a>
                        <span>/</span>
                        <a href="#"><?php echo e($course->category->name); ?></a>
                        <span>/</span>
                        <span><?php echo e($course->title); ?></span>
                    </div>
                    
                    <h1 class="course-title"><?php echo e($course->title); ?></h1>
                    <p class="course-description"><?php echo e($course->short_description); ?></p>
                    
                    <div class="course-meta">
                        <div class="meta-item">
                            <span class="meta-label">Instructor:</span>
                            <span class="meta-value"><?php echo e($course->instructor->name); ?></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Level:</span>
                            <span class="meta-value"><?php echo e(ucfirst($course->difficulty_level)); ?></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Duration:</span>
                            <span class="meta-value"><?php echo e($course->duration_hours); ?> hours</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Students:</span>
                            <span class="meta-value"><?php echo e($course->total_students); ?></span>
                        </div>
                    </div>
                    
                    <?php if($isEnrolled): ?>
                        <div class="progress-section">
                            <div class="progress-header">
                                <span>Your Progress</span>
                                <span class="progress-percentage"><?php echo e($progress); ?>%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?php echo e($progress); ?>%"></div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="course-sidebar">
                    <div class="course-preview">
                        <?php if($course->thumbnail): ?>
                            <img src="<?php echo e(Storage::url($course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>">
                        <?php else: ?>
                            <div class="course-placeholder">
                                <span class="course-icon">🎯</span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($course->video_preview): ?>
                            <div class="preview-overlay">
                                <button class="play-button" data-video="<?php echo e($course->video_preview); ?>">
                                    ▶️ Preview
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="course-pricing">
                        <?php if($course->is_free): ?>
                            <div class="price free">FREE</div>
                        <?php else: ?>
                            <div class="price">$<?php echo e($course->price); ?></div>
                        <?php endif; ?>
                        
                        <?php if($isEnrolled): ?>
                            <a href="#" class="btn btn-success btn-block">
                                ✅ Enrolled - Continue Learning
                            </a>
                        <?php else: ?>
                            <form method="POST" action="<?php echo e(route('courses.enroll', $course)); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <?php if($course->is_free): ?>
                                        Enroll for Free
                                    <?php else: ?>
                                        Enroll Now - $<?php echo e($course->price); ?>

                                    <?php endif; ?>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                    
                    <div class="course-includes">
                        <h4>This course includes:</h4>
                        <ul>
                            <li>📹 <?php echo e($course->total_lessons); ?> video lessons</li>
                            <li>📱 Mobile and desktop access</li>
                            <li>🏆 Certificate of completion</li>
                            <li>💬 Community access</li>
                            <li>🔄 Lifetime updates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="course-content">
        <div class="container">
            <div class="content-tabs">
                <button class="tab-button active" data-tab="overview">Overview</button>
                <button class="tab-button" data-tab="curriculum">Curriculum</button>
                <button class="tab-button" data-tab="reviews">Reviews</button>
            </div>
            
            <!-- Overview Tab -->
            <div class="tab-content active" id="overview">
                <div class="course-overview">
                    <div class="overview-section">
                        <h3>About This Course</h3>
                        <div class="course-full-description">
                            <?php echo nl2br(e($course->description)); ?>

                        </div>
                    </div>
                    
                    <?php if($course->what_you_learn && count($course->what_you_learn) > 0): ?>
                        <div class="overview-section">
                            <h3>What You'll Learn</h3>
                            <ul class="learning-objectives">
                                <?php $__currentLoopData = $course->what_you_learn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>✅ <?php echo e($objective); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($course->requirements && count($course->requirements) > 0): ?>
                        <div class="overview-section">
                            <h3>Requirements</h3>
                            <ul class="requirements-list">
                                <?php $__currentLoopData = $course->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>• <?php echo e($requirement); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Curriculum Tab -->
            <div class="tab-content" id="curriculum">
                <div class="course-curriculum">
                    <div class="curriculum-header">
                        <h3>Course Curriculum</h3>
                        <span class="lesson-count"><?php echo e($course->lessons->count()); ?> lessons</span>
                    </div>
                    
                    <div class="lessons-list">
                        <?php $__empty_1 = true; $__currentLoopData = $course->lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="lesson-item <?php echo e($isEnrolled ? 'accessible' : 'locked'); ?>">
                                <div class="lesson-number"><?php echo e($index + 1); ?></div>
                                <div class="lesson-content">
                                    <h4 class="lesson-title"><?php echo e($lesson->title); ?></h4>
                                    <?php if($lesson->description): ?>
                                        <p class="lesson-description"><?php echo e($lesson->description); ?></p>
                                    <?php endif; ?>
                                    <div class="lesson-meta">
                                        <?php if($lesson->video_duration): ?>
                                            <span class="lesson-duration">⏱️ <?php echo e($lesson->video_duration); ?></span>
                                        <?php endif; ?>
                                        <span class="lesson-type"><?php echo e(ucfirst($lesson->lesson_type)); ?></span>
                                    </div>
                                </div>
                                <div class="lesson-actions">
                                    <?php if($isEnrolled): ?>
                                        <a href="#" class="btn btn-sm btn-primary">Watch</a>
                                    <?php else: ?>
                                        <span class="locked-icon">🔒</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="empty-lessons">
                                <p>No lessons available yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Reviews Tab -->
            <div class="tab-content" id="reviews">
                <div class="course-reviews">
                    <div class="reviews-header">
                        <h3>Student Reviews</h3>
                        <div class="rating-summary">
                            <span class="average-rating"><?php echo e(number_format($course->rating, 1)); ?></span>
                            <div class="stars">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <span class="star <?php echo e($i <= $course->rating ? 'filled' : ''); ?>">⭐</span>
                                <?php endfor; ?>
                            </div>
                            <span class="review-count">(<?php echo e($course->rating_count); ?> reviews)</span>
                        </div>
                    </div>
                    
                    <div class="reviews-list">
                        <?php $__empty_1 = true; $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <span class="reviewer-name"><?php echo e($review->user->name); ?></span>
                                        <div class="review-rating">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <span class="star <?php echo e($i <= $review->rating ? 'filled' : ''); ?>">⭐</span>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    <span class="review-date"><?php echo e($review->created_at->diffForHumans()); ?></span>
                                </div>
                                <?php if($review->review): ?>
                                    <p class="review-text"><?php echo e($review->review); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="empty-reviews">
                                <p>No reviews yet. Be the first to review this course!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Courses -->
    <?php if($relatedCourses->count() > 0): ?>
        <section class="related-courses">
            <div class="container">
                <h3>Related Courses</h3>
                <div class="courses-grid">
                    <?php $__currentLoopData = $relatedCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="course-card">
                            <div class="course-thumbnail">
                                <?php if($relatedCourse->thumbnail): ?>
                                    <img src="<?php echo e(Storage::url($relatedCourse->thumbnail)); ?>" alt="<?php echo e($relatedCourse->title); ?>">
                                <?php else: ?>
                                    <div class="course-placeholder">
                                        <span class="course-icon">🎯</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="course-content">
                                <h4><a href="<?php echo e(route('courses.show', $relatedCourse)); ?>"><?php echo e($relatedCourse->title); ?></a></h4>
                                <p><?php echo e($relatedCourse->short_description); ?></p>
                                <div class="course-price">
                                    <?php if($relatedCourse->is_free): ?>
                                        <span class="price free">FREE</span>
                                    <?php else: ?>
                                        <span class="price">$<?php echo e($relatedCourse->price); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/course-detail.js')); ?>"></script>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/course/show.blade.php ENDPATH**/ ?>