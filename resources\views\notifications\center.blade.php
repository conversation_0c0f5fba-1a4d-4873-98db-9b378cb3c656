@extends('layouts.app')

@section('title', 'Notification Center')

@section('content')
<div class="notification-center">
    <!-- Header -->
    <div class="notification-header">
        <div class="header-content">
            <div class="header-info">
                <h1>🔔 Notification Center</h1>
                <p>Stay updated with all your important notifications</p>
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-outline" onclick="markAllAsRead()">
                    ✅ Mark All Read
                </button>
                <button type="button" class="btn btn-secondary" onclick="openSettings()">
                    ⚙️ Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Filters -->
    <div class="notification-filters">
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">
                All ({{ $notifications->count() }})
            </button>
            <button class="filter-tab" data-filter="unread">
                Unread ({{ $notifications->where('read_at', null)->count() }})
            </button>
            <button class="filter-tab" data-filter="courses">
                Courses ({{ $notifications->where('type', 'course')->count() }})
            </button>
            <button class="filter-tab" data-filter="payments">
                Payments ({{ $notifications->where('type', 'payment')->count() }})
            </button>
            <button class="filter-tab" data-filter="community">
                Community ({{ $notifications->where('type', 'community')->count() }})
            </button>
            <button class="filter-tab" data-filter="system">
                System ({{ $notifications->where('type', 'system')->count() }})
            </button>
        </div>
        
        <div class="filter-actions">
            <div class="search-box">
                <input type="text" placeholder="Search notifications..." id="notificationSearch">
                <span class="search-icon">🔍</span>
            </div>
            <select class="sort-select" id="sortSelect" onchange="sortNotifications()">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="unread">Unread First</option>
                <option value="type">By Type</option>
            </select>
        </div>
    </div>

    <!-- Notification List -->
    <div class="notification-list">
        @forelse($notifications as $notification)
            <div class="notification-item {{ $notification->read_at ? 'read' : 'unread' }}" 
                 data-type="{{ $notification->type }}" 
                 data-id="{{ $notification->id }}">
                
                <div class="notification-icon">
                    @switch($notification->type)
                        @case('course')
                            <span class="icon course">🎓</span>
                            @break
                        @case('payment')
                            <span class="icon payment">💰</span>
                            @break
                        @case('community')
                            <span class="icon community">👥</span>
                            @break
                        @case('system')
                            <span class="icon system">⚙️</span>
                            @break
                        @case('achievement')
                            <span class="icon achievement">🏆</span>
                            @break
                        @case('message')
                            <span class="icon message">💬</span>
                            @break
                        @default
                            <span class="icon default">🔔</span>
                    @endswitch
                </div>

                <div class="notification-content">
                    <div class="notification-header-item">
                        <h3 class="notification-title">{{ $notification->title }}</h3>
                        <div class="notification-meta">
                            <span class="notification-time">{{ $notification->created_at->diffForHumans() }}</span>
                            @if(!$notification->read_at)
                                <span class="unread-indicator"></span>
                            @endif
                        </div>
                    </div>
                    
                    <p class="notification-message">{{ $notification->message }}</p>
                    
                    @if($notification->data && isset($notification->data['action_url']))
                        <div class="notification-actions">
                            <a href="{{ $notification->data['action_url'] }}" class="action-btn primary">
                                {{ $notification->data['action_text'] ?? 'View Details' }}
                            </a>
                            @if(isset($notification->data['secondary_action']))
                                <button type="button" class="action-btn secondary" onclick="handleSecondaryAction('{{ $notification->id }}', '{{ $notification->data['secondary_action'] }}')">
                                    {{ $notification->data['secondary_text'] ?? 'Dismiss' }}
                                </button>
                            @endif
                        </div>
                    @endif
                    
                    @if($notification->data && isset($notification->data['preview']))
                        <div class="notification-preview">
                            @if($notification->data['preview']['type'] === 'image')
                                <img src="{{ $notification->data['preview']['url'] }}" alt="Preview" class="preview-image">
                            @elseif($notification->data['preview']['type'] === 'course')
                                <div class="course-preview">
                                    <div class="course-thumbnail">
                                        <img src="{{ $notification->data['preview']['thumbnail'] }}" alt="Course">
                                    </div>
                                    <div class="course-info">
                                        <h4>{{ $notification->data['preview']['title'] }}</h4>
                                        <p>{{ $notification->data['preview']['instructor'] }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>

                <div class="notification-controls">
                    <button type="button" class="control-btn" onclick="toggleRead('{{ $notification->id }}')" title="{{ $notification->read_at ? 'Mark as Unread' : 'Mark as Read' }}">
                        {{ $notification->read_at ? '📖' : '✅' }}
                    </button>
                    <button type="button" class="control-btn" onclick="deleteNotification('{{ $notification->id }}')" title="Delete">
                        🗑️
                    </button>
                    <button type="button" class="control-btn" onclick="toggleBookmark('{{ $notification->id }}')" title="Bookmark">
                        {{ $notification->bookmarked ? '⭐' : '☆' }}
                    </button>
                </div>
            </div>
        @empty
            <div class="empty-notifications">
                <div class="empty-icon">🔔</div>
                <h3>No Notifications</h3>
                <p>You're all caught up! New notifications will appear here.</p>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($notifications->hasPages())
        <div class="pagination-wrapper">
            {{ $notifications->links() }}
        </div>
    @endif

    <!-- Notification Settings Modal -->
    <div class="modal" id="settingsModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Notification Settings</h2>
                <button type="button" class="close-btn" onclick="closeSettings()">✕</button>
            </div>
            
            <div class="modal-body">
                <form id="notificationSettingsForm">
                    <div class="settings-section">
                        <h3>Email Notifications</h3>
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" name="email_course_updates" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <span class="toggle-title">Course Updates</span>
                                    <span class="toggle-desc">New lessons, announcements, and course changes</span>
                                </div>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" name="email_payment_confirmations" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <span class="toggle-title">Payment Confirmations</span>
                                    <span class="toggle-desc">Purchase receipts and payment notifications</span>
                                </div>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" name="email_community_activity">
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <span class="toggle-title">Community Activity</span>
                                    <span class="toggle-desc">New posts, comments, and community updates</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3>Push Notifications</h3>
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" name="push_live_streams" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <span class="toggle-title">Live Streams</span>
                                    <span class="toggle-desc">When instructors go live</span>
                                </div>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" name="push_messages" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <span class="toggle-title">Direct Messages</span>
                                    <span class="toggle-desc">New messages from instructors and students</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3>Frequency Settings</h3>
                        <div class="setting-item">
                            <label for="digest_frequency">Email Digest Frequency</label>
                            <select id="digest_frequency" name="digest_frequency">
                                <option value="immediate">Immediate</option>
                                <option value="daily" selected>Daily Digest</option>
                                <option value="weekly">Weekly Digest</option>
                                <option value="never">Never</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="quiet_hours_start">Quiet Hours</label>
                            <div class="time-range">
                                <input type="time" id="quiet_hours_start" name="quiet_hours_start" value="22:00">
                                <span>to</span>
                                <input type="time" id="quiet_hours_end" name="quiet_hours_end" value="08:00">
                            </div>
                            <small>No notifications will be sent during these hours</small>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeSettings()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">Save Settings</button>
            </div>
        </div>
    </div>
</div>

<style>
.notification-center {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.notification-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.notification-filters {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #a0a0a0;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
}

.sort-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-item {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notification-item.unread {
    border-left: 4px solid #3b82f6;
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.icon {
    font-size: 1.5rem;
}

.notification-content {
    flex: 1;
}

.notification-header-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.notification-title {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-time {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.unread-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3b82f6;
}

.notification-message {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.notification-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
}

.action-btn.primary {
    background: #3b82f6;
    color: white;
}

.action-btn.primary:hover {
    background: #1d4ed8;
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-preview {
    margin-top: 1rem;
}

.preview-image {
    max-width: 200px;
    border-radius: 8px;
}

.course-preview {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.course-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-info h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.course-info p {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin: 0;
}

.notification-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.control-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.empty-notifications {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-notifications h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-notifications p {
    color: #a0a0a0;
    font-size: 1rem;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.modal-body {
    padding: 2rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-content {
    display: flex;
    flex-direction: column;
}

.toggle-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toggle-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.setting-item label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.setting-item select,
.setting-item input[type="time"] {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.time-range {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.time-range span {
    color: #a0a0a0;
}

.setting-item small {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: block;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .notification-center {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filter-tabs {
        flex-direction: column;
    }
    
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .notification-item {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .notification-header-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .notification-controls {
        flex-direction: row;
        justify-content: center;
    }
}
</style>

<script>
// Filter functionality
document.querySelectorAll('.filter-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Remove active class from all tabs
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        // Add active class to clicked tab
        this.classList.add('active');
        
        const filter = this.dataset.filter;
        filterNotifications(filter);
    });
});

function filterNotifications(filter) {
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(notification => {
        const type = notification.dataset.type;
        const isUnread = notification.classList.contains('unread');
        
        let show = false;
        
        switch(filter) {
            case 'all':
                show = true;
                break;
            case 'unread':
                show = isUnread;
                break;
            default:
                show = type === filter;
        }
        
        notification.style.display = show ? 'grid' : 'none';
    });
}

// Search functionality
document.getElementById('notificationSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(notification => {
        const title = notification.querySelector('.notification-title').textContent.toLowerCase();
        const message = notification.querySelector('.notification-message').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || message.includes(searchTerm)) {
            notification.style.display = 'grid';
        } else {
            notification.style.display = 'none';
        }
    });
});

// Notification actions
function toggleRead(notificationId) {
    console.log('Toggle read status for notification:', notificationId);
    // Implementation for toggling read status
}

function deleteNotification(notificationId) {
    if (confirm('Delete this notification?')) {
        console.log('Delete notification:', notificationId);
        // Implementation for deleting notification
    }
}

function toggleBookmark(notificationId) {
    console.log('Toggle bookmark for notification:', notificationId);
    // Implementation for bookmarking
}

function markAllAsRead() {
    if (confirm('Mark all notifications as read?')) {
        console.log('Mark all as read');
        // Implementation for marking all as read
    }
}

function handleSecondaryAction(notificationId, action) {
    console.log('Handle secondary action:', action, 'for notification:', notificationId);
    // Implementation for secondary actions
}

function sortNotifications() {
    const sortBy = document.getElementById('sortSelect').value;
    console.log('Sort notifications by:', sortBy);
    // Implementation for sorting
}

// Settings modal
function openSettings() {
    document.getElementById('settingsModal').style.display = 'flex';
}

function closeSettings() {
    document.getElementById('settingsModal').style.display = 'none';
}

function saveSettings() {
    const formData = new FormData(document.getElementById('notificationSettingsForm'));
    console.log('Save notification settings:', Object.fromEntries(formData));
    // Implementation for saving settings
    closeSettings();
}

// Close modal when clicking outside
document.getElementById('settingsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSettings();
    }
});
</script>
@endsection
