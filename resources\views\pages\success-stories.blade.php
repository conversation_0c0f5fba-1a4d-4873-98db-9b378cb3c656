@extends('layouts.minimal')

@section('title', 'Success Stories')

@section('content')
<div class="success-page">
    <div class="page-header">
        <h1>🏆 Success Stories</h1>
        <p class="page-subtitle">Real results from Real World students</p>
    </div>

    <div class="coming-soon">
        <div class="coming-soon-icon">🚀</div>
        <h2>Success Stories Coming Soon</h2>
        <p>We're collecting amazing success stories from our community members who have transformed their lives through The Real World.</p>
        
        <div class="stats-preview">
            <div class="stat">
                <div class="stat-number">$10M+</div>
                <div class="stat-label">Student Earnings</div>
            </div>
            <div class="stat">
                <div class="stat-number">50K+</div>
                <div class="stat-label">Active Members</div>
            </div>
            <div class="stat">
                <div class="stat-number">500+</div>
                <div class="stat-label">Success Stories</div>
            </div>
        </div>
        
        <a href="{{ route('register') }}" class="btn btn-primary">Start Your Success Story</a>
    </div>
</div>

<style>
.success-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.coming-soon {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
}

.coming-soon-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.stats-preview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 2rem 0;
}

.stat {
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #22c55e;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #64748b;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .stats-preview {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
</style>
@endsection
