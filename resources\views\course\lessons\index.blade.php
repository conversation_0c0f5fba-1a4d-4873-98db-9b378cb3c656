@extends('layouts.app')

@section('title', 'Manage Lessons - ' . $course->title)

@section('content')
<div class="container">
    <div class="lesson-management-container">
        <!-- Header -->
        <div class="management-header">
            <div class="header-content">
                <div class="course-info">
                    <h1>{{ $course->title }}</h1>
                    <p>Lesson Management & Content Organization</p>
                    <div class="course-stats">
                        <span class="stat">{{ $course->lessons()->count() }} Lessons</span>
                        <span class="stat">{{ $course->lessons()->where('is_published', true)->count() }} Published</span>
                        <span class="stat">{{ $course->lessons()->sum('duration_minutes') }} min total</span>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="{{ route('courses.show', $course) }}" class="btn btn-secondary">
                        👁️ Preview Course
                    </a>
                    <a href="{{ route('courses.lessons.create', $course) }}" class="btn btn-primary">
                        ➕ Add Lesson
                    </a>
                </div>
            </div>
        </div>

        @if($lessons->count() > 0)
            <!-- Lesson Organization Tools -->
            <div class="organization-tools">
                <div class="tools-header">
                    <h2>📚 Course Content</h2>
                    <div class="tools-actions">
                        <button type="button" class="btn btn-outline" onclick="toggleReorderMode()">
                            🔄 Reorder Lessons
                        </button>
                        <button type="button" class="btn btn-outline" onclick="bulkPublish()">
                            📢 Bulk Publish
                        </button>
                        <button type="button" class="btn btn-outline" onclick="exportContent()">
                            📊 Export Content
                        </button>
                    </div>
                </div>

                <!-- Lessons List -->
                <div class="lessons-list" id="lessonsList">
                    @foreach($lessons as $index => $lesson)
                        <div class="lesson-item" data-lesson-id="{{ $lesson->id }}" data-order="{{ $lesson->sort_order }}">
                            <div class="lesson-drag-handle">
                                <span class="drag-icon">⋮⋮</span>
                            </div>
                            
                            <div class="lesson-number">
                                <span class="number">{{ $index + 1 }}</span>
                            </div>

                            <div class="lesson-content">
                                <div class="lesson-header">
                                    <h3 class="lesson-title">{{ $lesson->title }}</h3>
                                    <div class="lesson-badges">
                                        <span class="type-badge type-{{ $lesson->lesson_type }}">
                                            @if($lesson->lesson_type === 'video')
                                                🎥 Video
                                            @elseif($lesson->lesson_type === 'text')
                                                📝 Text
                                            @elseif($lesson->lesson_type === 'quiz')
                                                ❓ Quiz
                                            @elseif($lesson->lesson_type === 'assignment')
                                                📋 Assignment
                                            @else
                                                📄 Content
                                            @endif
                                        </span>
                                        
                                        <span class="status-badge status-{{ $lesson->is_published ? 'published' : 'draft' }}">
                                            @if($lesson->is_published)
                                                ✅ Published
                                            @else
                                                📝 Draft
                                            @endif
                                        </span>
                                        
                                        @if($lesson->is_free)
                                            <span class="free-badge">🆓 Free</span>
                                        @endif
                                    </div>
                                </div>

                                @if($lesson->description)
                                    <p class="lesson-description">{{ Str::limit($lesson->description, 120) }}</p>
                                @endif

                                <div class="lesson-meta">
                                    @if($lesson->duration_minutes)
                                        <span class="meta-item">
                                            <span class="meta-icon">⏱️</span>
                                            <span class="meta-text">{{ $lesson->duration_minutes }} min</span>
                                        </span>
                                    @endif
                                    
                                    @if($lesson->video_url)
                                        <span class="meta-item">
                                            <span class="meta-icon">🎥</span>
                                            <span class="meta-text">Video Content</span>
                                        </span>
                                    @endif
                                    
                                    @if($lesson->attachments()->count() > 0)
                                        <span class="meta-item">
                                            <span class="meta-icon">📎</span>
                                            <span class="meta-text">{{ $lesson->attachments()->count() }} files</span>
                                        </span>
                                    @endif
                                    
                                    <span class="meta-item">
                                        <span class="meta-icon">📅</span>
                                        <span class="meta-text">{{ $lesson->created_at ? $lesson->created_at->format('M j, Y') : 'Unknown' }}</span>
                                    </span>
                                </div>

                                @if($lesson->learning_objectives)
                                    <div class="learning-objectives">
                                        <h4>Learning Objectives:</h4>
                                        <ul>
                                            @foreach(json_decode($lesson->learning_objectives, true) ?? [] as $objective)
                                                <li>{{ $objective }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>

                            <div class="lesson-stats">
                                <div class="stat">
                                    <span class="stat-number">{{ $lesson->views ?? 0 }}</span>
                                    <span class="stat-label">Views</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-number">{{ $lesson->completions ?? 0 }}</span>
                                    <span class="stat-label">Completed</span>
                                </div>
                                @if($lesson->lesson_type === 'quiz')
                                    <div class="stat">
                                        <span class="stat-number">{{ number_format($lesson->average_score ?? 0, 1) }}%</span>
                                        <span class="stat-label">Avg Score</span>
                                    </div>
                                @endif
                            </div>

                            <div class="lesson-actions">
                                <div class="action-buttons">
                                    <a href="{{ route('courses.lessons.show', [$course, $lesson]) }}" 
                                       class="btn-icon" title="Preview Lesson">
                                        👁️
                                    </a>
                                    <a href="{{ route('courses.lessons.edit', [$course, $lesson]) }}" 
                                       class="btn-icon" title="Edit Lesson">
                                        ✏️
                                    </a>
                                    <button type="button" 
                                            class="btn-icon" 
                                            onclick="duplicateLesson({{ $lesson->id }})" 
                                            title="Duplicate Lesson">
                                        📋
                                    </button>
                                    <button type="button" 
                                            class="btn-icon toggle-publish" 
                                            onclick="togglePublish({{ $lesson->id }})" 
                                            title="{{ $lesson->is_published ? 'Unpublish' : 'Publish' }}">
                                        {{ $lesson->is_published ? '📤' : '📥' }}
                                    </button>
                                    <button type="button" 
                                            class="btn-icon btn-danger" 
                                            onclick="deleteLesson({{ $lesson->id }})" 
                                            title="Delete Lesson">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions" id="bulkActions" style="display: none;">
                    <div class="bulk-header">
                        <span class="selected-count">0 lessons selected</span>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="clearSelection()">
                            Clear Selection
                        </button>
                    </div>
                    <div class="bulk-buttons">
                        <button type="button" class="btn btn-sm btn-primary" onclick="bulkPublishSelected()">
                            📢 Publish Selected
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="bulkUnpublishSelected()">
                            📥 Unpublish Selected
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="bulkDeleteSelected()">
                            🗑️ Delete Selected
                        </button>
                    </div>
                </div>
            </div>

        @else
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h2>No Lessons Yet</h2>
                <p>Start building your course by adding your first lesson. You can include videos, text content, quizzes, and assignments.</p>
                <div class="empty-actions">
                    <a href="{{ route('courses.lessons.create', $course) }}" class="btn btn-primary">
                        ➕ Create First Lesson
                    </a>
                    <button type="button" class="btn btn-secondary" onclick="importContent()">
                        📥 Import Content
                    </button>
                </div>
            </div>
        @endif

        <!-- Course Structure Tips -->
        <div class="structure-tips">
            <h3>💡 Course Structure Tips</h3>
            <div class="tips-grid">
                <div class="tip">
                    <div class="tip-icon">🎯</div>
                    <h4>Clear Progression</h4>
                    <p>Organize lessons in a logical sequence from basic to advanced concepts.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">⏱️</div>
                    <h4>Optimal Length</h4>
                    <p>Keep lessons between 5-15 minutes for better engagement and retention.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">🎥</div>
                    <h4>Mix Content Types</h4>
                    <p>Combine videos, text, quizzes, and assignments for varied learning experiences.</p>
                </div>
                <div class="tip">
                    <div class="tip-icon">📝</div>
                    <h4>Learning Objectives</h4>
                    <p>Define clear objectives for each lesson to guide student expectations.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.lesson-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.management-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.course-info p {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.course-stats {
    display: flex;
    gap: 1rem;
}

.course-stats .stat {
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.organization-tools {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.tools-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.tools-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.tools-actions {
    display: flex;
    gap: 0.75rem;
}

.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.lesson-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: grid;
    grid-template-columns: auto auto 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
    transition: all 0.3s ease;
}

.lesson-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.lesson-drag-handle {
    cursor: grab;
    color: #a0a0a0;
    font-size: 1.25rem;
}

.lesson-drag-handle:active {
    cursor: grabbing;
}

.lesson-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
}

.lesson-content {
    flex: 1;
}

.lesson-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.lesson-title {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0;
}

.lesson-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.type-badge, .status-badge, .free-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.type-video { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
.type-text { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
.type-quiz { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
.type-assignment { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; }

.status-published { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.status-draft { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

.free-badge { background: rgba(34, 197, 94, 0.2); color: #22c55e; }

.lesson-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.lesson-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #a0a0a0;
    font-size: 0.75rem;
}

.learning-objectives {
    margin-top: 1rem;
}

.learning-objectives h4 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.learning-objectives ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.learning-objectives li {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    padding-left: 1rem;
    position: relative;
}

.learning-objectives li::before {
    content: "•";
    color: #3b82f6;
    position: absolute;
    left: 0;
}

.lesson-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
    min-width: 80px;
}

.stat {
    display: flex;
    flex-direction: column;
}

.stat-number {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
}

.stat-label {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.lesson-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    text-decoration: none;
}

.btn-icon:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.btn-icon.btn-danger:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
}

.bulk-actions {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
}

.bulk-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.selected-count {
    color: #3b82f6;
    font-weight: 500;
}

.bulk-buttons {
    display: flex;
    gap: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    margin-bottom: 2rem;
}

.empty-icon {
    font-size: 6rem;
    margin-bottom: 2rem;
    opacity: 0.5;
}

.empty-state h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.structure-tips {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
}

.structure-tips h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tip {
    text-align: center;
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.tip h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip p {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .lesson-management-container {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .tools-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .tools-actions {
        flex-direction: column;
    }
    
    .lesson-item {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .lesson-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .action-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
let reorderMode = false;
let selectedLessons = new Set();

function toggleReorderMode() {
    reorderMode = !reorderMode;
    const button = event.target;
    
    if (reorderMode) {
        button.textContent = '✅ Save Order';
        button.classList.add('btn-primary');
        button.classList.remove('btn-outline');
        // Enable drag and drop
        enableDragAndDrop();
    } else {
        button.textContent = '🔄 Reorder Lessons';
        button.classList.remove('btn-primary');
        button.classList.add('btn-outline');
        // Save new order
        saveNewOrder();
        disableDragAndDrop();
    }
}

function enableDragAndDrop() {
    // Implementation for drag and drop functionality
    console.log('Drag and drop enabled');
}

function disableDragAndDrop() {
    // Implementation for disabling drag and drop
    console.log('Drag and drop disabled');
}

function saveNewOrder() {
    // Implementation for saving new lesson order
    console.log('Save new lesson order');
}

function togglePublish(lessonId) {
    // Implementation for toggling lesson publish status
    console.log('Toggle publish for lesson:', lessonId);
}

function duplicateLesson(lessonId) {
    if (confirm('Duplicate this lesson?')) {
        // Implementation for duplicating lesson
        console.log('Duplicate lesson:', lessonId);
    }
}

function deleteLesson(lessonId) {
    if (confirm('Are you sure you want to delete this lesson? This action cannot be undone.')) {
        // Implementation for deleting lesson
        console.log('Delete lesson:', lessonId);
    }
}

function bulkPublish() {
    // Implementation for bulk publishing
    console.log('Bulk publish lessons');
}

function exportContent() {
    // Implementation for exporting content
    console.log('Export course content');
}

function importContent() {
    // Implementation for importing content
    console.log('Import course content');
}

// Bulk action functions
function bulkPublishSelected() {
    console.log('Bulk publish selected lessons');
}

function bulkUnpublishSelected() {
    console.log('Bulk unpublish selected lessons');
}

function bulkDeleteSelected() {
    if (confirm('Delete selected lessons? This action cannot be undone.')) {
        console.log('Bulk delete selected lessons');
    }
}

function clearSelection() {
    selectedLessons.clear();
    updateBulkActions();
}

function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.querySelector('.selected-count');
    
    if (selectedLessons.size > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = `${selectedLessons.size} lessons selected`;
    } else {
        bulkActions.style.display = 'none';
    }
}
</script>
@endsection
