@extends('layouts.app')

@section('title', 'Create User - Admin Panel')

@section('content')
<div class="admin-container">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <h2>Admin Panel</h2>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="{{ route('admin.dashboard') }}">
                    <span class="nav-icon">📊</span> Dashboard
                </a></li>
                <li><a href="{{ route('admin.users.index') }}" class="active">
                    <span class="nav-icon">👥</span> Users
                </a></li>
                <li><a href="{{ route('admin.courses.index') }}">
                    <span class="nav-icon">📚</span> Courses
                </a></li>
                <li><a href="{{ route('admin.payments.index') }}">
                    <span class="nav-icon">💰</span> Payments
                </a></li>
                <li><a href="{{ route('admin.analytics') }}">
                    <span class="nav-icon">📈</span> Analytics
                </a></li>
                <li><a href="{{ route('admin.settings') }}">
                    <span class="nav-icon">⚙️</span> Settings
                </a></li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1>Create New User</h1>
                    <p>Add a new user to the platform</p>
                </div>
                <div class="header-actions">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                        ← Back to Users
                    </a>
                </div>
            </div>
        </div>

        <div class="admin-content">
            <div class="create-user-container">
                <div class="create-user-card">
                    <form method="POST" action="{{ route('admin.users.store') }}" class="create-user-form">
                        @csrf

                        <!-- Basic Information -->
                        <div class="form-section">
                            <h3>Basic Information</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" 
                                           id="name" 
                                           name="name" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name') }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" 
                                           id="email" 
                                           name="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           value="{{ old('email') }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" 
                                           id="password" 
                                           name="password" 
                                           class="form-control @error('password') is-invalid @enderror" 
                                           required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="password_confirmation" class="form-label">Confirm Password *</label>
                                    <input type="password" 
                                           id="password_confirmation" 
                                           name="password_confirmation" 
                                           class="form-control" 
                                           required>
                                </div>
                            </div>
                        </div>

                        <!-- Role Assignment -->
                        <div class="form-section">
                            <h3>Role Assignment</h3>
                            
                            <div class="role-selection">
                                @foreach($roles as $role)
                                    <div class="role-option">
                                        <input type="checkbox" 
                                               id="role_{{ $role->id }}" 
                                               name="roles[]" 
                                               value="{{ $role->id }}"
                                               {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                                        <label for="role_{{ $role->id }}">
                                            <div class="role-content">
                                                <div class="role-icon">
                                                    @if($role->name === 'super-admin')
                                                        👑
                                                    @elseif($role->name === 'admin')
                                                        🛡️
                                                    @elseif($role->name === 'instructor')
                                                        🎓
                                                    @else
                                                        👤
                                                    @endif
                                                </div>
                                                <div class="role-text">
                                                    <h4>{{ ucfirst($role->name) }}</h4>
                                                    <p>{{ $role->description ?? 'Standard ' . $role->name . ' privileges' }}</p>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('roles')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Additional Information -->
                        <div class="form-section">
                            <h3>Additional Information</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="bio" class="form-label">Bio</label>
                                    <textarea id="bio" 
                                              name="bio" 
                                              class="form-control @error('bio') is-invalid @enderror" 
                                              rows="3">{{ old('bio') }}</textarea>
                                    @error('bio')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" 
                                           id="location" 
                                           name="location" 
                                           class="form-control @error('location') is-invalid @enderror" 
                                           value="{{ old('location') }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="website" class="form-label">Website</label>
                                    <input type="url" 
                                           id="website" 
                                           name="website" 
                                           class="form-control @error('website') is-invalid @enderror" 
                                           value="{{ old('website') }}">
                                    @error('website')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <div class="form-section">
                            <h3>Account Settings</h3>
                            
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           value="1" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label for="is_active">
                                        <span class="checkbox-text">Active Account</span>
                                        <span class="checkbox-desc">User can log in and access the platform</span>
                                    </label>
                                </div>

                                <div class="checkbox-item">
                                    <input type="checkbox" 
                                           id="email_verified" 
                                           name="email_verified" 
                                           value="1" 
                                           {{ old('email_verified', true) ? 'checked' : '' }}>
                                    <label for="email_verified">
                                        <span class="checkbox-text">Email Verified</span>
                                        <span class="checkbox-desc">Mark email as verified (skip verification process)</span>
                                    </label>
                                </div>

                                <div class="checkbox-item">
                                    <input type="checkbox" 
                                           id="send_welcome_email" 
                                           name="send_welcome_email" 
                                           value="1" 
                                           {{ old('send_welcome_email', true) ? 'checked' : '' }}>
                                    <label for="send_welcome_email">
                                        <span class="checkbox-text">Send Welcome Email</span>
                                        <span class="checkbox-desc">Send welcome email with login credentials</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                👤 Create User
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Help Section -->
                <div class="help-section">
                    <h3>💡 User Creation Tips</h3>
                    <div class="help-items">
                        <div class="help-item">
                            <strong>Role Selection:</strong> Choose appropriate roles based on user responsibilities. Multiple roles can be assigned.
                        </div>
                        <div class="help-item">
                            <strong>Password Security:</strong> Use strong passwords with at least 8 characters including letters, numbers, and symbols.
                        </div>
                        <div class="help-item">
                            <strong>Email Verification:</strong> Check "Email Verified" to skip the verification process for admin-created accounts.
                        </div>
                        <div class="help-item">
                            <strong>Welcome Email:</strong> The welcome email includes login credentials and platform information.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.create-user-container {
    max-width: 1000px;
    margin: 0 auto;
}

.create-user-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.role-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.role-option {
    position: relative;
}

.role-option input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.role-option label {
    display: block;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-option input[type="checkbox"]:checked + label {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.role-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-icon {
    font-size: 2rem;
}

.role-text h4 {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.role-text p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-item input[type="checkbox"] {
    margin-top: 0.25rem;
}

.checkbox-item label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.checkbox-text {
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.checkbox-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.help-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.help-section h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.help-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.help-item {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.help-item strong {
    color: #ffffff;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .role-selection {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
@endsection
