<?php

namespace App\Http\Controllers\Chat;

use App\Http\Controllers\Controller;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ChatRoomController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view chats')->only(['index', 'show']);
        $this->middleware('can:create chats')->only(['create', 'store']);
        $this->middleware('can:edit chats')->only(['edit', 'update']);
        $this->middleware('can:delete chats')->only(['destroy']);
    }

    /**
     * Display a listing of chat rooms.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get user's joined chat rooms
        $joinedRooms = $user->chatRooms()
            ->with(['course', 'creator'])
            ->where('is_active', true)
            ->orderBy('last_message_at', 'desc')
            ->get();

        // Get public chat rooms user hasn't joined
        $publicRooms = ChatRoom::where('type', 'public')
            ->where('is_active', true)
            ->whereNotIn('id', $joinedRooms->pluck('id'))
            ->with(['course', 'creator'])
            ->orderBy('member_count', 'desc')
            ->take(10)
            ->get();

        // Get course-specific rooms for enrolled courses
        $courseRooms = ChatRoom::where('type', 'course')
            ->whereHas('course.students', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('is_active', true)
            ->with(['course', 'creator'])
            ->get();

        return view('chat.index', compact('joinedRooms', 'publicRooms', 'courseRooms'));
    }

    /**
     * Show the form for creating a new chat room.
     */
    public function create()
    {
        $courses = Course::where('status', 'published')->get();
        return view('chat.create', compact('courses'));
    }

    /**
     * Store a newly created chat room.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:public,private,course',
            'course_id' => 'nullable|exists:courses,id',
            'settings' => 'nullable|array',
        ]);

        $chatRoom = new ChatRoom($request->all());
        $chatRoom->slug = Str::slug($request->name);
        $chatRoom->created_by = Auth::id();

        $chatRoom->save();

        // Auto-join creator to chat room
        $chatRoom->members()->attach(Auth::id(), [
            'role' => 'admin',
            'joined_at' => now()
        ]);

        $chatRoom->increment('member_count');

        return redirect()->route('chat.show', $chatRoom)
                        ->with('success', 'Chat room created successfully!');
    }

    /**
     * Display the specified chat room.
     */
    public function show(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user has access to this chat room
        if ($chatRoom->type === 'private' && !$chatRoom->members()->where('user_id', $user->id)->exists()) {
            abort(403, 'You do not have access to this chat room.');
        }

        if ($chatRoom->type === 'course' && !$chatRoom->course->students()->where('user_id', $user->id)->exists()) {
            abort(403, 'You must be enrolled in the course to access this chat room.');
        }

        $chatRoom->load(['course', 'creator']);

        // Check if user is a member
        $isMember = $chatRoom->members()->where('user_id', $user->id)->exists();

        // Auto-join public rooms
        if ($chatRoom->type === 'public' && !$isMember) {
            $chatRoom->members()->attach($user->id, [
                'role' => 'member',
                'joined_at' => now()
            ]);
            $chatRoom->increment('member_count');
            $isMember = true;
        }

        // Get recent messages
        $messages = $chatRoom->messages()
            ->with(['user', 'replyTo.user'])
            ->where('is_deleted', false)
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get()
            ->reverse()
            ->values();

        // Get online members
        $onlineMembers = $chatRoom->members()
            ->where('last_read_at', '>=', now()->subMinutes(5))
            ->take(10)
            ->get();

        // Mark messages as read
        if ($isMember) {
            $chatRoom->members()->updateExistingPivot($user->id, [
                'last_read_at' => now()
            ]);
        }

        return view('chat.show', compact('chatRoom', 'isMember', 'messages', 'onlineMembers'));
    }

    /**
     * Show the form for editing the chat room.
     */
    public function edit(ChatRoom $chatRoom)
    {
        $this->authorize('update', $chatRoom);
        $courses = Course::where('status', 'published')->get();
        return view('chat.edit', compact('chatRoom', 'courses'));
    }

    /**
     * Update the specified chat room.
     */
    public function update(Request $request, ChatRoom $chatRoom)
    {
        $this->authorize('update', $chatRoom);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:public,private,course',
            'course_id' => 'nullable|exists:courses,id',
            'settings' => 'nullable|array',
        ]);

        $chatRoom->fill($request->all());
        $chatRoom->slug = Str::slug($request->name);
        $chatRoom->save();

        return redirect()->route('chat.show', $chatRoom)
                        ->with('success', 'Chat room updated successfully!');
    }

    /**
     * Remove the specified chat room.
     */
    public function destroy(ChatRoom $chatRoom)
    {
        $this->authorize('delete', $chatRoom);
        $chatRoom->delete();

        return redirect()->route('chat.index')
                        ->with('success', 'Chat room deleted successfully!');
    }

    /**
     * Join a chat room.
     */
    public function join(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        if ($chatRoom->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('chat.show', $chatRoom)
                           ->with('info', 'You are already a member of this chat room.');
        }

        $chatRoom->members()->attach($user->id, [
            'role' => 'member',
            'joined_at' => now()
        ]);

        $chatRoom->increment('member_count');

        return redirect()->route('chat.show', $chatRoom)
                       ->with('success', 'Successfully joined the chat room!');
    }

    /**
     * Leave a chat room.
     */
    public function leave(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        if (!$chatRoom->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('chat.show', $chatRoom)
                           ->with('error', 'You are not a member of this chat room.');
        }

        // Prevent creator from leaving
        if ($chatRoom->created_by === $user->id) {
            return redirect()->route('chat.show', $chatRoom)
                           ->with('error', 'Chat room creator cannot leave the room.');
        }

        $chatRoom->members()->detach($user->id);
        $chatRoom->decrement('member_count');

        return redirect()->route('chat.index')
                       ->with('success', 'Successfully left the chat room.');
    }
}
