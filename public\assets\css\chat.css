/* Chat Styles */

.chat-main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.header-content p {
    font-size: 1.125rem;
    color: #a0a0a0;
}

/* Chat Layout */
.chat-layout {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.chat-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3b82f6;
}

/* Chat Rooms Grid */
.chat-rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

.chat-room-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chat-room-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.chat-room-card.active-room {
    border-color: #3b82f6;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

.chat-room-card.course-room {
    border-color: #10b981;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.room-name {
    margin-bottom: 0.25rem;
}

.room-name a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.room-name a:hover {
    color: #3b82f6;
}

.room-type {
    font-size: 0.75rem;
    color: #a0a0a0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.room-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.online-indicator {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.member-count {
    font-size: 0.875rem;
    color: #a0a0a0;
    font-weight: 500;
}

.room-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.room-course {
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.course-label {
    color: #a0a0a0;
}

.course-name,
.course-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.course-link:hover {
    color: #2563eb;
}

.room-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #a0a0a0;
}

.stat-icon {
    font-size: 0.875rem;
}

.room-actions {
    margin-top: auto;
}

.btn-block {
    width: 100%;
    text-align: center;
}

/* Chat Guidelines */
.chat-guidelines {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
}

.chat-guidelines h4 {
    color: #ffffff;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.guidelines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.guideline-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.guideline-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.guideline-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

/* Chat Room Detail Styles */
.chat-container {
    display: grid;
    grid-template-columns: 1fr 250px;
    gap: 2rem;
    height: calc(100vh - 200px);
}

.chat-area {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
}

.chat-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.chat-title {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.chat-subtitle {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
}

.message.own-message {
    background: rgba(59, 130, 246, 0.2);
    margin-left: 2rem;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.message-author {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
}

.message-time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.message-text {
    color: #a0a0a0;
    line-height: 1.5;
}

.chat-input {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.input-form {
    display: flex;
    gap: 0.75rem;
    align-items: end;
}

.message-input {
    flex: 1;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    resize: none;
    min-height: 40px;
    max-height: 120px;
}

.message-input::placeholder {
    color: #666666;
}

.send-button {
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.send-button:hover {
    background: #2563eb;
}

.send-button:disabled {
    background: #666666;
    cursor: not-allowed;
}

/* Chat Sidebar */
.chat-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.sidebar-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
}

.sidebar-title {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.online-member {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
}

.member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
}

.member-name {
    color: #ffffff;
    font-size: 0.875rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state h3 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .chat-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .chat-rooms-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .chat-rooms-grid {
        grid-template-columns: 1fr;
    }
    
    .guidelines-grid {
        grid-template-columns: 1fr;
    }
    
    .room-stats {
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .chat-main {
        padding: 1rem 0;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .chat-room-card {
        padding: 1rem;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}
