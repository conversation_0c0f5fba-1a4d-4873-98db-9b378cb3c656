<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Community;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SearchController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Global search across the platform.
     */
    public function index(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all');

        if (empty($query)) {
            return view('search.index', [
                'query' => $query,
                'type' => $type,
                'results' => collect(),
                'total' => 0
            ]);
        }

        $results = collect();
        $total = 0;

        // Search courses
        if ($type === 'all' || $type === 'courses') {
            $courses = Course::where('status', 'published')
                ->where(function($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('tags', 'like', "%{$query}%");
                })
                ->with(['instructor', 'category'])
                ->limit(10)
                ->get()
                ->map(function($course) {
                    return [
                        'type' => 'course',
                        'id' => $course->id,
                        'title' => $course->title,
                        'description' => $course->description,
                        'url' => route('courses.show', $course),
                        'meta' => [
                            'instructor' => $course->instructor->name,
                            'category' => $course->category->name,
                            'price' => $course->price,
                            'students' => $course->students_count ?? 0,
                        ],
                        'image' => $course->thumbnail,
                        'created_at' => $course->created_at,
                    ];
                });

            $results = $results->concat($courses);
            $total += $courses->count();
        }

        // Search communities
        if ($type === 'all' || $type === 'communities') {
            $communities = Community::where('is_active', true)
                ->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                })
                ->with(['creator'])
                ->limit(10)
                ->get()
                ->map(function($community) {
                    return [
                        'type' => 'community',
                        'id' => $community->id,
                        'title' => $community->name,
                        'description' => $community->description,
                        'url' => route('communities.show', $community),
                        'meta' => [
                            'creator' => $community->creator->name,
                            'members' => $community->member_count,
                            'posts' => $community->post_count,
                            'type' => ucfirst($community->type),
                        ],
                        'image' => $community->banner,
                        'created_at' => $community->created_at,
                    ];
                });

            $results = $results->concat($communities);
            $total += $communities->count();
        }

        // Search users (if admin or instructor)
        if ((Auth::user()->hasRole(['admin', 'instructor'])) && ($type === 'all' || $type === 'users')) {
            $users = User::where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('email', 'like', "%{$query}%");
                })
                ->with(['roles'])
                ->limit(10)
                ->get()
                ->map(function($user) {
                    return [
                        'type' => 'user',
                        'id' => $user->id,
                        'title' => $user->name,
                        'description' => $user->email,
                        'url' => route('admin.users.show', $user),
                        'meta' => [
                            'roles' => $user->roles->pluck('name')->implode(', '),
                            'status' => $user->is_active ? 'Active' : 'Inactive',
                            'verified' => $user->email_verified_at ? 'Yes' : 'No',
                        ],
                        'image' => null,
                        'created_at' => $user->created_at,
                    ];
                });

            $results = $results->concat($users);
            $total += $users->count();
        }

        // Sort results by relevance (title matches first, then description)
        $results = $results->sortBy(function($item) use ($query) {
            $titleMatch = stripos($item['title'], $query) !== false ? 0 : 1;
            $descMatch = stripos($item['description'], $query) !== false ? 0 : 1;
            return $titleMatch + $descMatch;
        })->values();

        return view('search.index', [
            'query' => $query,
            'type' => $type,
            'results' => $results,
            'total' => $total
        ]);
    }

    /**
     * AJAX search for autocomplete.
     */
    public function autocomplete(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = collect();

        // Course suggestions
        $courses = Course::where('status', 'published')
            ->where('title', 'like', "%{$query}%")
            ->limit(5)
            ->get(['id', 'title'])
            ->map(function($course) {
                return [
                    'type' => 'course',
                    'id' => $course->id,
                    'title' => $course->title,
                    'url' => route('courses.show', $course),
                ];
            });

        // Community suggestions
        $communities = Community::where('is_active', true)
            ->where('name', 'like', "%{$query}%")
            ->limit(5)
            ->get(['id', 'name'])
            ->map(function($community) {
                return [
                    'type' => 'community',
                    'id' => $community->id,
                    'title' => $community->name,
                    'url' => route('communities.show', $community),
                ];
            });

        $suggestions = $suggestions->concat($courses)->concat($communities);

        return response()->json($suggestions->take(10)->values());
    }

    /**
     * Search within a specific course.
     */
    public function searchCourse(Request $request, Course $course)
    {
        $query = $request->get('q', '');

        if (empty($query)) {
            return response()->json([]);
        }

        // Search lessons within the course
        $lessons = $course->lessons()
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            })
            ->get(['id', 'title', 'content', 'order'])
            ->map(function($lesson) use ($course) {
                return [
                    'type' => 'lesson',
                    'id' => $lesson->id,
                    'title' => $lesson->title,
                    'content' => substr(strip_tags($lesson->content), 0, 200) . '...',
                    'url' => route('courses.lessons.show', [$course, $lesson]),
                    'order' => $lesson->order,
                ];
            });

        return response()->json($lessons);
    }
}
