<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Welcome') - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/auth.css') }}" rel="stylesheet">
    
    @stack('styles')
</head>
<body class="guest-body">
    <!-- Background Elements -->
    <div class="auth-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
    </div>
    
    <!-- Navigation (minimal for guest) -->
    <nav class="guest-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="{{ route('home') }}" class="brand-link">
                    <span class="brand-icon">🌍</span>
                    <span class="brand-text">The Real World</span>
                </a>
            </div>
            
            <div class="nav-links">
                @guest
                    <a href="{{ route('login') }}" class="nav-link {{ request()->routeIs('login') ? 'active' : '' }}">
                        Login
                    </a>
                    <a href="{{ route('register') }}" class="nav-link {{ request()->routeIs('register') ? 'active' : '' }}">
                        Register
                    </a>
                @else
                    <a href="{{ route('dashboard') }}" class="nav-link">
                        Dashboard
                    </a>
                @endguest
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="guest-main">
        <div class="auth-container">
            <!-- Flash Messages -->
            @if (session('status'))
                <div class="alert alert-success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">{{ session('status') }}</div>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">{{ session('error') }}</div>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">
                        <ul class="error-list">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            @endif
            
            <!-- Page Content -->
            @yield('content')
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="guest-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>The Real World</h4>
                    <p>Escape the matrix through education and community.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('courses.index') }}">Courses</a></li>
                        <li><a href="{{ route('communities.index') }}">Community</a></li>
                        <li><a href="{{ route('about') }}">About</a></li>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul class="footer-links">
                        <li><a href="{{ route('help') }}">Help Center</a></li>
                        <li><a href="{{ route('privacy') }}">Privacy Policy</a></li>
                        <li><a href="{{ route('terms') }}">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; {{ date('Y') }} The Real World. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Custom JS -->
    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/auth.js') }}"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
        
        // Form validation enhancement
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = submitBtn.innerHTML.replace(/Login|Register|Submit/, 'Processing...');
                }
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
