<?php

namespace App\Services;

use App\Models\Course;
use App\Models\User;
use App\Models\Payment;
use Stripe\StripeClient;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Illuminate\Support\Facades\Log;

class StripePaymentService
{
    protected $stripe;

    public function __construct(StripeClient $stripe)
    {
        $this->stripe = $stripe;
    }

    /**
     * Create Stripe payment intent.
     */
    public function createPaymentIntent(User $user, Course $course): array
    {
        try {
            // Ensure user has Stripe customer ID
            if (!$user->stripe_id) {
                $customer = $this->stripe->customers->create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);

                $user->update(['stripe_id' => $customer->id]);
            }

            // Create payment intent
            $paymentIntent = $this->stripe->paymentIntents->create([
                'amount' => $course->price * 100, // Convert to cents
                'currency' => 'usd',
                'customer' => $user->stripe_id,
                'metadata' => [
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                    'course_title' => $course->title,
                ],
                'description' => "Enrollment in course: {$course->title}",
                'setup_future_usage' => 'off_session', // For saving payment method
            ]);

            Log::info('Stripe payment intent created', [
                'payment_intent_id' => $paymentIntent->id,
                'user_id' => $user->id,
                'course_id' => $course->id,
                'amount' => $course->price,
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $course->price,
                'currency' => 'USD',
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create Stripe payment intent', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Confirm payment intent.
     */
    public function confirmPaymentIntent(string $paymentIntentId): PaymentIntent
    {
        try {
            return $this->stripe->paymentIntents->retrieve($paymentIntentId);
        } catch (\Exception $e) {
            Log::error('Failed to confirm Stripe payment intent', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Process refund.
     */
    public function processRefund(Payment $payment, float $amount = null): bool
    {
        try {
            $refundAmount = $amount ? ($amount * 100) : ($payment->amount * 100);

            $refund = $this->stripe->refunds->create([
                'payment_intent' => $payment->transaction_id,
                'amount' => $refundAmount,
                'reason' => 'requested_by_customer',
                'metadata' => [
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                    'course_id' => $payment->course_id,
                ],
            ]);

            // Update payment record
            $payment->update([
                'status' => 'refunded',
                'payment_data' => array_merge($payment->payment_data ?? [], [
                    'refund_id' => $refund->id,
                    'refund_amount' => $refundAmount / 100,
                    'refunded_at' => now()->toISOString(),
                ]),
            ]);

            Log::info('Stripe refund processed', [
                'refund_id' => $refund->id,
                'payment_id' => $payment->id,
                'amount' => $refundAmount / 100,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to process Stripe refund', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get user's payment methods.
     */
    public function getPaymentMethods(User $user): array
    {
        try {
            if (!$user->stripe_id) {
                return [];
            }

            $paymentMethods = $this->stripe->paymentMethods->all([
                'customer' => $user->stripe_id,
                'type' => 'card',
            ]);

            return array_map(function ($pm) {
                return [
                    'id' => $pm->id,
                    'type' => 'card',
                    'brand' => $pm->card->brand,
                    'last_four' => $pm->card->last4,
                    'exp_month' => $pm->card->exp_month,
                    'exp_year' => $pm->card->exp_year,
                    'created' => $pm->created,
                ];
            }, $paymentMethods->data);

        } catch (\Exception $e) {
            Log::error('Failed to get Stripe payment methods', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Save payment method for user.
     */
    public function savePaymentMethod(User $user, string $paymentMethodId): bool
    {
        try {
            // Ensure user has Stripe customer ID
            if (!$user->stripe_id) {
                $customer = $this->stripe->customers->create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);

                $user->update(['stripe_id' => $customer->id]);
            }

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach($paymentMethodId, [
                'customer' => $user->stripe_id,
            ]);

            Log::info('Stripe payment method saved', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethodId,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to save Stripe payment method', [
                'user_id' => $user->id,
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Delete payment method.
     */
    public function deletePaymentMethod(string $paymentMethodId): bool
    {
        try {
            $this->stripe->paymentMethods->detach($paymentMethodId);

            Log::info('Stripe payment method deleted', [
                'payment_method_id' => $paymentMethodId,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to delete Stripe payment method', [
                'payment_method_id' => $paymentMethodId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Handle Stripe webhook.
     */
    public function handleWebhook(array $payload, string $signature): bool
    {
        try {
            $event = \Stripe\Webhook::constructEvent(
                json_encode($payload),
                $signature,
                config('services.stripe.webhook_secret')
            );

            Log::info('Stripe webhook received', [
                'event_type' => $event->type,
                'event_id' => $event->id,
            ]);

            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event->data->object);
                    break;

                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event->data->object);
                    break;

                case 'customer.subscription.created':
                case 'customer.subscription.updated':
                case 'customer.subscription.deleted':
                    // Handle subscription events if needed
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', [
                        'event_type' => $event->type,
                    ]);
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to handle Stripe webhook', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Handle successful payment intent.
     */
    protected function handlePaymentIntentSucceeded($paymentIntent): void
    {
        $userId = $paymentIntent->metadata->user_id ?? null;
        $courseId = $paymentIntent->metadata->course_id ?? null;

        if ($userId && $courseId) {
            $user = User::find($userId);
            $course = Course::find($courseId);

            if ($user && $course) {
                ProcessPayment::dispatch($user, $course, $paymentIntent->id, 'stripe');
            }
        }
    }

    /**
     * Handle failed payment intent.
     */
    protected function handlePaymentIntentFailed($paymentIntent): void
    {
        $userId = $paymentIntent->metadata->user_id ?? null;
        $courseId = $paymentIntent->metadata->course_id ?? null;

        if ($userId && $courseId) {
            Payment::create([
                'user_id' => $userId,
                'course_id' => $courseId,
                'payment_method' => 'stripe',
                'amount' => $paymentIntent->amount / 100,
                'currency' => strtoupper($paymentIntent->currency),
                'status' => 'failed',
                'transaction_id' => $paymentIntent->id,
                'payment_data' => [
                    'stripe_payment_intent' => $paymentIntent,
                    'failed_at' => now()->toISOString(),
                ],
            ]);
        }
    }
}
