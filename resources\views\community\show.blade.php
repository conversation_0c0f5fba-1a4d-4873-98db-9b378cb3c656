<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $community->name }} - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/community.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}">Courses</a></li>
                <li><a href="{{ route('communities.index') }}" class="active">Community</a></li>
                <li><a href="{{ route('chat.index') }}">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="community-detail-main">
        <div class="container">
            <!-- Community Header -->
            <div class="community-header">
                @if($community->banner)
                    <div class="community-banner">
                        <img src="{{ Storage::url($community->banner) }}" alt="{{ $community->name }}">
                    </div>
                @endif
                
                <div class="community-info">
                    <div class="community-meta">
                        <h1>{{ $community->name }}</h1>
                        <p class="community-description">{{ $community->description }}</p>
                        
                        <div class="community-stats">
                            <div class="stat">
                                <span class="stat-value">{{ number_format($stats['total_members']) }}</span>
                                <span class="stat-label">Members</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">{{ number_format($stats['total_posts']) }}</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">{{ number_format($stats['active_members']) }}</span>
                                <span class="stat-label">Active This Week</span>
                            </div>
                        </div>
                        
                        @if($community->course)
                            <div class="community-course">
                                <span class="course-label">Related Course:</span>
                                <a href="{{ route('courses.show', $community->course) }}" class="course-link">
                                    {{ $community->course->title }}
                                </a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="community-actions">
                        @if($isMember)
                            <button class="btn btn-success" disabled>
                                ✅ Member
                            </button>
                            <form method="POST" action="{{ route('communities.leave', $community) }}" style="display: inline;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-secondary" 
                                        onclick="return confirm('Are you sure you want to leave this community?')">
                                    Leave Community
                                </button>
                            </form>
                        @else
                            <form method="POST" action="{{ route('communities.join', $community) }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-primary">
                                    Join Community
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Community Content -->
            <div class="community-content-layout">
                <!-- Main Content -->
                <div class="community-posts">
                    @if($isMember)
                        <!-- Create Post Form -->
                        <div class="create-post-card">
                            <form method="POST" action="{{ route('communities.posts.store', $community) }}" class="create-post-form">
                                @csrf
                                <div class="form-group">
                                    <textarea name="content" placeholder="Share something with the community..." 
                                              class="post-textarea" rows="3" required></textarea>
                                </div>
                                <div class="form-actions">
                                    <div class="post-options">
                                        <label class="file-upload">
                                            📎 Attach File
                                            <input type="file" name="attachments[]" multiple style="display: none;">
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Post</button>
                                </div>
                            </form>
                        </div>
                    @endif

                    <!-- Posts List -->
                    <div class="posts-list">
                        @forelse($posts as $post)
                            <div class="post-card">
                                <div class="post-header">
                                    <div class="post-author">
                                        <div class="author-avatar">
                                            {{ substr($post->user->name, 0, 1) }}
                                        </div>
                                        <div class="author-info">
                                            <h4>{{ $post->user->name }}</h4>
                                            <span class="post-time">{{ $post->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                    
                                    @if($post->is_pinned)
                                        <div class="post-badge pinned">📌 Pinned</div>
                                    @endif
                                </div>
                                
                                <div class="post-content">
                                    @if($post->title)
                                        <h3 class="post-title">{{ $post->title }}</h3>
                                    @endif
                                    <div class="post-text">{{ $post->content }}</div>
                                    
                                    @if($post->attachments)
                                        <div class="post-attachments">
                                            @foreach($post->attachments as $attachment)
                                                <div class="attachment">
                                                    <a href="{{ Storage::url($attachment['path']) }}" target="_blank">
                                                        📎 {{ $attachment['name'] }}
                                                    </a>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="post-actions">
                                    <button class="post-action like-btn" data-post-id="{{ $post->id }}">
                                        👍 <span class="like-count">{{ $post->likes_count }}</span>
                                    </button>
                                    <button class="post-action reply-btn" data-post-id="{{ $post->id }}">
                                        💬 Reply ({{ $post->replies_count }})
                                    </button>
                                    <button class="post-action share-btn">
                                        🔗 Share
                                    </button>
                                </div>
                                
                                <!-- Replies -->
                                @if($post->replies->count() > 0)
                                    <div class="post-replies">
                                        @foreach($post->replies->take(3) as $reply)
                                            <div class="reply-item">
                                                <div class="reply-author">{{ $reply->user->name }}</div>
                                                <div class="reply-content">{{ $reply->content }}</div>
                                                <div class="reply-time">{{ $reply->created_at->diffForHumans() }}</div>
                                            </div>
                                        @endforeach
                                        
                                        @if($post->replies->count() > 3)
                                            <button class="load-more-replies">
                                                Load {{ $post->replies->count() - 3 }} more replies
                                            </button>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        @empty
                            <div class="empty-posts">
                                <div class="empty-icon">💬</div>
                                <h3>No posts yet</h3>
                                <p>Be the first to start a conversation in this community!</p>
                                @if($isMember)
                                    <button class="btn btn-primary" onclick="document.querySelector('.post-textarea').focus()">
                                        Create First Post
                                    </button>
                                @endif
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    @if($posts->hasPages())
                        <div class="pagination-wrapper">
                            {{ $posts->links() }}
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="community-sidebar">
                    <!-- Community Rules -->
                    <div class="sidebar-card">
                        <h4>Community Rules</h4>
                        <ul class="rules-list">
                            <li>🤝 Be respectful to all members</li>
                            <li>💡 Share valuable insights and knowledge</li>
                            <li>🚫 No spam, advertising, or off-topic content</li>
                            <li>🎯 Stay focused on growth and success</li>
                            <li>💪 Support each other's journey</li>
                        </ul>
                    </div>

                    <!-- Recent Members -->
                    <div class="sidebar-card">
                        <h4>Recent Members</h4>
                        <div class="recent-members">
                            @foreach($community->members()->latest('pivot_joined_at')->take(5)->get() as $member)
                                <div class="member-item">
                                    <div class="member-avatar">{{ substr($member->name, 0, 1) }}</div>
                                    <div class="member-info">
                                        <div class="member-name">{{ $member->name }}</div>
                                        <div class="member-joined">Joined {{ $member->pivot->joined_at->diffForHumans() }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Community Stats -->
                    <div class="sidebar-card">
                        <h4>Community Activity</h4>
                        <div class="activity-stats">
                            <div class="activity-stat">
                                <span class="stat-number">{{ $stats['total_posts'] }}</span>
                                <span class="stat-label">Total Posts</span>
                            </div>
                            <div class="activity-stat">
                                <span class="stat-number">{{ $stats['active_members'] }}</span>
                                <span class="stat-label">Active This Week</span>
                            </div>
                            <div class="activity-stat">
                                <span class="stat-number">{{ $community->created_at->diffInDays(now()) }}</span>
                                <span class="stat-label">Days Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/community.js') }}"></script>
    <script>
        // Initialize community page
        document.addEventListener('DOMContentLoaded', function() {
            // Handle post likes
            document.querySelectorAll('.like-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const postId = this.getAttribute('data-post-id');
                    togglePostLike(postId);
                });
            });
            
            // Handle reply buttons
            document.querySelectorAll('.reply-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const postId = this.getAttribute('data-post-id');
                    showReplyForm(postId);
                });
            });
        });
        
        function togglePostLike(postId) {
            fetch(`/communities/posts/${postId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const likeButton = document.querySelector(`[data-post-id="${postId}"] .like-count`);
                    likeButton.textContent = data.likes_count;
                }
            })
            .catch(error => console.error('Error:', error));
        }
        
        function showReplyForm(postId) {
            // Implementation for reply form
            console.log('Show reply form for post:', postId);
        }
    </script>
</body>
</html>
