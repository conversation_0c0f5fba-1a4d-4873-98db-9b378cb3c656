<?php

namespace App\Jobs;

use App\Models\Course;
use App\Models\Payment;
use App\Models\User;
use App\Events\CourseEnrolled;
use App\Models\CourseEnrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class ProcessPayment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $course;
    protected $paymentIntentId;
    protected $paymentMethod;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(User $user, Course $course, string $paymentIntentId, string $paymentMethod = 'stripe')
    {
        $this->user = $user;
        $this->course = $course;
        $this->paymentIntentId = $paymentIntentId;
        $this->paymentMethod = $paymentMethod;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            // Verify payment with Stripe
            $stripe = new StripeClient(config('services.stripe.secret'));
            $paymentIntent = $stripe->paymentIntents->retrieve($this->paymentIntentId);

            if ($paymentIntent->status !== 'succeeded') {
                throw new \Exception('Payment not successful. Status: ' . $paymentIntent->status);
            }

            // Create payment record
            $payment = Payment::create([
                'user_id' => $this->user->id,
                'course_id' => $this->course->id,
                'payment_method' => $this->paymentMethod,
                'amount' => $this->course->price,
                'currency' => 'USD',
                'status' => 'completed',
                'transaction_id' => $this->paymentIntentId,
                'payment_data' => [
                    'stripe_payment_intent' => $paymentIntent->toArray(),
                    'processed_at' => now()->toISOString(),
                ],
            ]);

            // Check if user is already enrolled
            $existingEnrollment = CourseEnrollment::where('user_id', $this->user->id)
                ->where('course_id', $this->course->id)
                ->first();

            if (!$existingEnrollment) {
                // Create course enrollment
                $enrollment = CourseEnrollment::create([
                    'user_id' => $this->user->id,
                    'course_id' => $this->course->id,
                    'enrolled_at' => now(),
                    'payment_id' => $payment->id,
                    'status' => 'active',
                ]);

                // Update course student count
                $this->course->increment('students_count');

                // Fire course enrolled event
                event(new CourseEnrolled($this->user, $this->course));

                Log::info('Payment processed and enrollment created', [
                    'user_id' => $this->user->id,
                    'course_id' => $this->course->id,
                    'payment_id' => $payment->id,
                    'enrollment_id' => $enrollment->id,
                ]);
            } else {
                Log::info('Payment processed but user already enrolled', [
                    'user_id' => $this->user->id,
                    'course_id' => $this->course->id,
                    'payment_id' => $payment->id,
                    'existing_enrollment_id' => $existingEnrollment->id,
                ]);
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Payment processing failed', [
                'user_id' => $this->user->id,
                'course_id' => $this->course->id,
                'payment_intent_id' => $this->paymentIntentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Create failed payment record
            Payment::create([
                'user_id' => $this->user->id,
                'course_id' => $this->course->id,
                'payment_method' => $this->paymentMethod,
                'amount' => $this->course->price,
                'currency' => 'USD',
                'status' => 'failed',
                'transaction_id' => $this->paymentIntentId,
                'payment_data' => [
                    'error' => $e->getMessage(),
                    'failed_at' => now()->toISOString(),
                ],
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessPayment job failed permanently', [
            'user_id' => $this->user->id,
            'course_id' => $this->course->id,
            'payment_intent_id' => $this->paymentIntentId,
            'error' => $exception->getMessage(),
        ]);

        // TODO: Send notification to admin about failed payment processing
        // TODO: Send notification to user about payment issue
    }
}
