<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\PaymentMethod as StripePaymentMethod;
use Stripe\Refund;

class StripeController extends Controller
{
    public function __construct()
    {
        // Set Stripe API key
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create payment intent for Stripe.
     */
    public function createPaymentIntent(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'amount' => 'required|numeric|min:0.50',
        ]);

        try {
            $user = Auth::user();

            // Get or create Stripe customer
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $request->amount * 100, // Convert to cents
                'currency' => 'usd',
                'customer' => $stripeCustomer->id,
                'setup_future_usage' => 'on_session', // Save payment method for future use
                'metadata' => [
                    'course_id' => $request->course_id,
                    'user_id' => $user->id,
                ],
            ]);

            return response()->json([
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Stripe payment intent creation failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment intent: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process Stripe payment.
     */
    public function processPayment(Payment $payment, Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        try {
            $user = Auth::user();

            // Get or create Stripe customer
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $payment->amount * 100, // Convert to cents
                'currency' => strtolower($payment->currency),
                'customer' => $stripeCustomer->id,
                'payment_method' => $request->payment_method_id,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('payment.success', $payment),
                'metadata' => [
                    'payment_id' => $payment->id,
                    'course_id' => $payment->course_id,
                    'user_id' => $payment->user_id,
                ],
            ]);

            // Update payment with Stripe payment intent ID
            $payment->update([
                'payment_intent_id' => $paymentIntent->id,
                'gateway_response' => $paymentIntent->toArray(),
            ]);

            return $this->handlePaymentIntentResponse($paymentIntent, $payment);

        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Stripe webhook.
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook_secret');

        try {
            $event = \Stripe\Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid Stripe webhook payload: ' . $e->getMessage());
            return response('Invalid payload', 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('Invalid Stripe webhook signature: ' . $e->getMessage());
            return response('Invalid signature', 400);
        }

        // Handle the event
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event['data']['object']);
                break;

            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event['data']['object']);
                break;

            case 'charge.dispute.created':
                $this->handleChargeDispute($event['data']['object']);
                break;

            default:
                Log::info('Unhandled Stripe webhook event: ' . $event['type']);
        }

        return response('Webhook handled', 200);
    }

    /**
     * Process Stripe refund.
     */
    public function processRefund(Payment $payment)
    {
        try {
            $refund = Refund::create([
                'payment_intent' => $payment->payment_intent_id,
                'amount' => $payment->amount * 100, // Convert to cents
                'reason' => 'requested_by_customer',
                'metadata' => [
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                ],
            ]);

            // Update payment status
            $payment->update([
                'status' => 'refunded',
                'refunded_at' => now(),
                'refund_id' => $refund->id,
            ]);

            // Remove user from course
            $payment->course->students()->detach($payment->user_id);

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully.',
                'refund_id' => $refund->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Stripe refund processing failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save payment method for future use.
     */
    public function savePaymentMethod(Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
            'is_default' => 'boolean',
        ]);

        try {
            $user = Auth::user();
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Attach payment method to customer
            $paymentMethod = StripePaymentMethod::retrieve($request->payment_method_id);
            $paymentMethod->attach(['customer' => $stripeCustomer->id]);

            // Save to database
            $savedPaymentMethod = PaymentMethod::create([
                'user_id' => $user->id,
                'gateway' => 'stripe',
                'gateway_payment_method_id' => $request->payment_method_id,
                'type' => $paymentMethod->type,
                'last_four' => $paymentMethod->card->last4 ?? null,
                'brand' => $paymentMethod->card->brand ?? null,
                'exp_month' => $paymentMethod->card->exp_month ?? null,
                'exp_year' => $paymentMethod->card->exp_year ?? null,
                'is_default' => $request->is_default ?? false,
                'is_active' => true,
            ]);

            // Set as default if requested
            if ($request->is_default) {
                PaymentMethod::where('user_id', $user->id)
                    ->where('id', '!=', $savedPaymentMethod->id)
                    ->update(['is_default' => false]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment method saved successfully.',
                'payment_method' => $savedPaymentMethod,
            ]);

        } catch (\Exception $e) {
            Log::error('Save payment method failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to save payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get or create Stripe customer.
     */
    private function getOrCreateStripeCustomer($user)
    {
        if ($user->stripe_customer_id) {
            try {
                return Customer::retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                // Customer doesn't exist, create new one
            }
        }

        // Create new Stripe customer
        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        // Save customer ID to user
        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Handle payment intent response.
     */
    private function handlePaymentIntentResponse($paymentIntent, $payment)
    {
        if ($paymentIntent->status === 'succeeded') {
            // Payment succeeded
            $payment->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Enroll user in course
            $payment->course->students()->attach($payment->user_id, [
                'enrolled_at' => now(),
                'progress' => 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment successful! You are now enrolled in the course.',
                'redirect_url' => route('courses.show', $payment->course)
            ]);

        } elseif ($paymentIntent->status === 'requires_action') {
            // Requires additional authentication
            return response()->json([
                'success' => false,
                'requires_action' => true,
                'payment_intent' => [
                    'id' => $paymentIntent->id,
                    'client_secret' => $paymentIntent->client_secret,
                ],
            ]);

        } else {
            // Payment failed
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment failed: ' . ($paymentIntent->last_payment_error->message ?? 'Unknown error')
            ], 400);
        }
    }

    /**
     * Handle successful payment intent webhook.
     */
    private function handlePaymentIntentSucceeded($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent['id'])->first();

        if ($payment && $payment->status !== 'completed') {
            $payment->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Enroll user in course if not already enrolled
            if (!$payment->course->students()->where('user_id', $payment->user_id)->exists()) {
                $payment->course->students()->attach($payment->user_id, [
                    'enrolled_at' => now(),
                    'progress' => 0,
                ]);
            }
        }
    }

    /**
     * Handle failed payment intent webhook.
     */
    private function handlePaymentIntentFailed($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent['id'])->first();

        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentIntent['last_payment_error']['message'] ?? 'Payment failed',
            ]);
        }
    }

    /**
     * Handle charge dispute webhook.
     */
    private function handleChargeDispute($dispute)
    {
        // Handle dispute logic here
        Log::warning('Stripe dispute created', ['dispute_id' => $dispute['id']]);

        // You might want to:
        // 1. Notify administrators
        // 2. Suspend user access
        // 3. Create dispute record in database
    }
}
