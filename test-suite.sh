#!/bin/bash

# The Real World LMS - Comprehensive Testing Suite
# This script runs all tests and quality checks for the platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Test counter
run_test() {
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log "Running test: $1"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Copy test environment file
    if [ ! -f ".env.testing" ]; then
        cp .env.example .env.testing
        
        # Configure test database
        sed -i 's/DB_DATABASE=.*/DB_DATABASE=the_real_world_testing/' .env.testing
        sed -i 's/APP_ENV=.*/APP_ENV=testing/' .env.testing
        sed -i 's/APP_DEBUG=.*/APP_DEBUG=true/' .env.testing
    fi
    
    # Generate application key for testing
    php artisan key:generate --env=testing
    
    # Create test database
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS the_real_world_testing;"
    
    # Run migrations for test database
    php artisan migrate:fresh --env=testing --seed
    
    success "Test environment setup complete"
}

# Run PHP unit tests
run_php_tests() {
    run_test "PHP Unit Tests"
    
    if php artisan test --env=testing; then
        success "PHP unit tests passed"
    else
        error "PHP unit tests failed"
    fi
}

# Run feature tests
run_feature_tests() {
    run_test "Feature Tests"
    
    if php artisan test --testsuite=Feature --env=testing; then
        success "Feature tests passed"
    else
        error "Feature tests failed"
    fi
}

# Test database migrations
test_migrations() {
    run_test "Database Migrations"
    
    # Test fresh migration
    if php artisan migrate:fresh --env=testing; then
        success "Fresh migrations successful"
    else
        error "Fresh migrations failed"
        return
    fi
    
    # Test rollback
    if php artisan migrate:rollback --env=testing; then
        success "Migration rollback successful"
    else
        error "Migration rollback failed"
        return
    fi
    
    # Test migration again
    if php artisan migrate --env=testing; then
        success "Re-migration successful"
    else
        error "Re-migration failed"
    fi
}

# Test database seeders
test_seeders() {
    run_test "Database Seeders"
    
    if php artisan db:seed --env=testing; then
        success "Database seeding successful"
    else
        error "Database seeding failed"
    fi
}

# Code quality checks
run_code_quality_checks() {
    log "Running code quality checks..."
    
    # PHP CS Fixer (if installed)
    if command -v php-cs-fixer &> /dev/null; then
        run_test "PHP CS Fixer"
        if php-cs-fixer fix --dry-run --diff; then
            success "Code style check passed"
        else
            warning "Code style issues found (run php-cs-fixer fix to fix them)"
        fi
    fi
    
    # PHPStan (if installed)
    if command -v phpstan &> /dev/null; then
        run_test "PHPStan Static Analysis"
        if phpstan analyse; then
            success "Static analysis passed"
        else
            error "Static analysis failed"
        fi
    fi
    
    # Check for TODO/FIXME comments
    run_test "TODO/FIXME Check"
    TODO_COUNT=$(grep -r "TODO\|FIXME" app/ resources/ --exclude-dir=vendor | wc -l)
    if [ "$TODO_COUNT" -gt 0 ]; then
        warning "Found $TODO_COUNT TODO/FIXME comments"
    else
        success "No TODO/FIXME comments found"
    fi
}

# Security tests
run_security_tests() {
    log "Running security tests..."
    
    # Check for hardcoded secrets
    run_test "Hardcoded Secrets Check"
    if grep -r "password\|secret\|key" app/ --include="*.php" | grep -v "config\|env\|Hash::make" | grep -E "(=|:)\s*['\"][^'\"]{8,}['\"]"; then
        error "Potential hardcoded secrets found"
    else
        success "No hardcoded secrets detected"
    fi
    
    # Check file permissions
    run_test "File Permissions Check"
    if [ -w "storage" ] && [ -w "bootstrap/cache" ]; then
        success "Storage directories are writable"
    else
        error "Storage directories are not writable"
    fi
    
    # Check .env file security
    run_test "Environment File Security"
    if [ -f ".env" ] && [ "$(stat -c %a .env)" = "600" ]; then
        success ".env file has secure permissions"
    else
        warning ".env file permissions should be 600"
    fi
}

# Performance tests
run_performance_tests() {
    log "Running performance tests..."
    
    # Test route loading time
    run_test "Route Performance"
    START_TIME=$(date +%s%N)
    php artisan route:list > /dev/null
    END_TIME=$(date +%s%N)
    DURATION=$(( (END_TIME - START_TIME) / 1000000 ))
    
    if [ "$DURATION" -lt 1000 ]; then
        success "Route loading time: ${DURATION}ms"
    else
        warning "Route loading time is slow: ${DURATION}ms"
    fi
    
    # Test config caching
    run_test "Config Caching"
    if php artisan config:cache && php artisan config:clear; then
        success "Config caching works"
    else
        error "Config caching failed"
    fi
}

# API tests
run_api_tests() {
    log "Running API tests..."
    
    # Start test server
    php artisan serve --env=testing &
    SERVER_PID=$!
    sleep 3
    
    # Test API endpoints
    run_test "API Health Check"
    if curl -s http://localhost:8000/api/health | grep -q "ok"; then
        success "API health check passed"
    else
        error "API health check failed"
    fi
    
    # Test authentication endpoints
    run_test "Authentication API"
    REGISTER_RESPONSE=$(curl -s -X POST http://localhost:8000/api/register \
        -H "Content-Type: application/json" \
        -d '{"name":"Test User","email":"<EMAIL>","password":"password123","password_confirmation":"password123"}')
    
    if echo "$REGISTER_RESPONSE" | grep -q "token"; then
        success "User registration API works"
    else
        error "User registration API failed"
    fi
    
    # Stop test server
    kill $SERVER_PID
}

# Frontend tests
run_frontend_tests() {
    log "Running frontend tests..."
    
    # Check if Node.js dependencies are installed
    if [ ! -d "node_modules" ]; then
        log "Installing Node.js dependencies..."
        npm install
    fi
    
    # Run JavaScript tests (if configured)
    if [ -f "package.json" ] && grep -q "test" package.json; then
        run_test "JavaScript Tests"
        if npm test; then
            success "JavaScript tests passed"
        else
            error "JavaScript tests failed"
        fi
    fi
    
    # Build assets
    run_test "Asset Building"
    if npm run build; then
        success "Asset building successful"
    else
        error "Asset building failed"
    fi
    
    # Check for JavaScript errors
    run_test "JavaScript Syntax Check"
    JS_ERRORS=$(find public/assets/js -name "*.js" -exec node -c {} \; 2>&1 | wc -l)
    if [ "$JS_ERRORS" -eq 0 ]; then
        success "No JavaScript syntax errors"
    else
        error "JavaScript syntax errors found"
    fi
}

# Integration tests
run_integration_tests() {
    log "Running integration tests..."
    
    # Test email functionality
    run_test "Email Integration"
    if php artisan tinker --execute="Mail::raw('Test email', function(\$message) { \$message->to('<EMAIL>')->subject('Test'); }); echo 'Email test completed';" | grep -q "completed"; then
        success "Email integration test passed"
    else
        warning "Email integration test failed (check mail configuration)"
    fi
    
    # Test cache functionality
    run_test "Cache Integration"
    if php artisan tinker --execute="Cache::put('test', 'value', 60); echo Cache::get('test');" | grep -q "value"; then
        success "Cache integration test passed"
    else
        error "Cache integration test failed"
    fi
    
    # Test queue functionality
    run_test "Queue Integration"
    if php artisan queue:work --once --timeout=10 > /dev/null 2>&1; then
        success "Queue integration test passed"
    else
        warning "Queue integration test failed (no jobs in queue)"
    fi
}

# Load testing (basic)
run_load_tests() {
    log "Running basic load tests..."
    
    # Start test server
    php artisan serve --env=testing &
    SERVER_PID=$!
    sleep 3
    
    run_test "Basic Load Test"
    
    # Simple concurrent request test
    for i in {1..10}; do
        curl -s http://localhost:8000 > /dev/null &
    done
    wait
    
    success "Basic load test completed"
    
    # Stop test server
    kill $SERVER_PID
}

# Generate test report
generate_report() {
    log "Generating test report..."
    
    REPORT_FILE="test-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $REPORT_FILE << EOF
The Real World LMS - Test Report
Generated: $(date)

Test Summary:
=============
Total Tests: $TOTAL_TESTS
Passed: $PASSED_TESTS
Failed: $FAILED_TESTS
Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%

Test Environment:
================
PHP Version: $(php -r "echo PHP_VERSION;")
Laravel Version: $(php artisan --version)
Database: MySQL
Node.js Version: $(node --version)
NPM Version: $(npm --version)

Test Categories:
===============
✓ Unit Tests
✓ Feature Tests
✓ Database Tests
✓ Code Quality
✓ Security Tests
✓ Performance Tests
✓ API Tests
✓ Frontend Tests
✓ Integration Tests
✓ Load Tests

Recommendations:
===============
EOF

    if [ $FAILED_TESTS -gt 0 ]; then
        echo "❌ $FAILED_TESTS tests failed. Please review and fix the issues." >> $REPORT_FILE
    else
        echo "✅ All tests passed! The application is ready for deployment." >> $REPORT_FILE
    fi
    
    echo "" >> $REPORT_FILE
    echo "For detailed logs, check the console output above." >> $REPORT_FILE
    
    log "Test report saved to: $REPORT_FILE"
}

# Cleanup
cleanup() {
    log "Cleaning up test environment..."
    
    # Remove test database
    mysql -u root -e "DROP DATABASE IF EXISTS the_real_world_testing;"
    
    # Clear test caches
    php artisan cache:clear --env=testing
    php artisan config:clear --env=testing
    
    success "Cleanup completed"
}

# Main test execution
main() {
    log "🧪 Starting The Real World LMS Test Suite..."
    
    setup_test_environment
    run_php_tests
    run_feature_tests
    test_migrations
    test_seeders
    run_code_quality_checks
    run_security_tests
    run_performance_tests
    run_api_tests
    run_frontend_tests
    run_integration_tests
    run_load_tests
    generate_report
    cleanup
    
    log "🎉 Test suite completed!"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        success "All tests passed! 🚀"
        exit 0
    else
        error "$FAILED_TESTS tests failed. Please fix the issues before deployment."
        exit 1
    fi
}

# Script usage
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "The Real World LMS Test Suite"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h    Show this help message"
    echo "  --quick       Run only essential tests"
    echo "  --full        Run complete test suite (default)"
    echo ""
    exit 0
fi

# Run tests
if [ "$1" = "--quick" ]; then
    log "Running quick test suite..."
    setup_test_environment
    run_php_tests
    run_feature_tests
    cleanup
else
    main
fi
