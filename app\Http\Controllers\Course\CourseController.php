<?php

namespace App\Http\Controllers\Course;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CourseController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view courses')->only(['index', 'show']);
        $this->middleware('can:create courses')->only(['create', 'store']);
        $this->middleware('can:edit courses')->only(['edit', 'update']);
        $this->middleware('can:delete courses')->only(['destroy']);
    }

    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['instructor', 'category'])
            ->published()
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // Difficulty filter
        if ($request->has('difficulty') && $request->difficulty) {
            $query->where('difficulty_level', $request->difficulty);
        }

        // Price filter
        if ($request->has('price_filter')) {
            if ($request->price_filter === 'free') {
                $query->where('is_free', true);
            } elseif ($request->price_filter === 'paid') {
                $query->where('is_free', false);
            }
        }

        $courses = $query->paginate(12);
        $categories = CourseCategory::where('is_active', true)->get();

        return view('course.index', compact('courses', 'categories'));
    }

    /**
     * Show the form for creating a new course.
     */
    public function create()
    {
        $categories = CourseCategory::where('is_active', true)->get();
        return view('course.create', compact('categories'));
    }

    /**
     * Store a newly created course.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:course_categories,id',
            'price' => 'required|numeric|min:0',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_preview' => 'nullable|url',
            'requirements' => 'nullable|array',
            'what_you_learn' => 'nullable|array',
        ]);

        $course = new Course($request->all());
        $course->slug = Str::slug($request->title);
        $course->instructor_id = Auth::id();
        $course->is_free = $request->price == 0;
        $course->status = 'draft';

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('course-thumbnails', 'public');
            $course->thumbnail = $path;
        }

        $course->save();

        return redirect()->route('courses.show', $course)
                        ->with('success', 'Course created successfully!');
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        $course->load(['instructor', 'category', 'lessons' => function($query) {
            $query->where('is_published', true)->orderBy('sort_order');
        }]);

        $isEnrolled = Auth::check() && $course->students()->where('user_id', Auth::id())->exists();
        $progress = 0;

        if ($isEnrolled) {
            $progress = $course->getCompletionPercentage(Auth::user());
        }

        $reviews = $course->reviews()->with('user')->latest()->take(5)->get();
        $relatedCourses = Course::where('category_id', $course->category_id)
                               ->where('id', '!=', $course->id)
                               ->published()
                               ->take(4)
                               ->get();

        return view('course.show', compact('course', 'isEnrolled', 'progress', 'reviews', 'relatedCourses'));
    }

    /**
     * Show the form for editing the course.
     */
    public function edit(Course $course)
    {
        $this->authorize('update', $course);
        $categories = CourseCategory::where('is_active', true)->get();
        return view('course.edit', compact('course', 'categories'));
    }

    /**
     * Update the specified course.
     */
    public function update(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:course_categories,id',
            'price' => 'required|numeric|min:0',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_preview' => 'nullable|url',
            'requirements' => 'nullable|array',
            'what_you_learn' => 'nullable|array',
        ]);

        $course->fill($request->all());
        $course->slug = Str::slug($request->title);
        $course->is_free = $request->price == 0;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail
            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }
            $path = $request->file('thumbnail')->store('course-thumbnails', 'public');
            $course->thumbnail = $path;
        }

        $course->save();

        return redirect()->route('courses.show', $course)
                        ->with('success', 'Course updated successfully!');
    }

    /**
     * Remove the specified course.
     */
    public function destroy(Course $course)
    {
        $this->authorize('delete', $course);

        // Delete thumbnail
        if ($course->thumbnail) {
            Storage::disk('public')->delete($course->thumbnail);
        }

        $course->delete();

        return redirect()->route('courses.index')
                        ->with('success', 'Course deleted successfully!');
    }
}
