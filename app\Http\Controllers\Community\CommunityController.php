<?php

namespace App\Http\Controllers\Community;

use App\Http\Controllers\Controller;
use App\Models\Community;
use App\Models\CommunityPost;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CommunityController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view communities')->only(['index', 'show']);
        $this->middleware('can:create communities')->only(['create', 'store']);
        $this->middleware('can:edit communities')->only(['edit', 'update']);
        $this->middleware('can:delete communities')->only(['destroy']);
    }

    /**
     * Display a listing of communities.
     */
    public function index(Request $request)
    {
        $query = Community::with(['course', 'creator'])
            ->where('is_active', true)
            ->orderBy('member_count', 'desc');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Type filter
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        $communities = $query->paginate(12);

        // Get popular communities for sidebar
        $popularCommunities = Community::where('is_active', true)
            ->orderBy('member_count', 'desc')
            ->take(5)
            ->get();

        return view('community.index', compact('communities', 'popularCommunities'));
    }

    /**
     * Show the form for creating a new community.
     */
    public function create()
    {
        $courses = Course::where('status', 'published')->get();
        return view('community.create', compact('courses'));
    }

    /**
     * Store a newly created community.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:public,private,course-specific',
            'course_id' => 'nullable|exists:courses,id',
            'rules' => 'nullable|array',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $community = new Community($request->all());
        $community->slug = Str::slug($request->name);
        $community->created_by = Auth::id();

        // Handle banner upload
        if ($request->hasFile('banner')) {
            $path = $request->file('banner')->store('community-banners', 'public');
            $community->banner = $path;
        }

        $community->save();

        // Auto-join creator to community
        $community->members()->attach(Auth::id(), [
            'role' => 'admin',
            'joined_at' => now()
        ]);

        $community->increment('member_count');

        return redirect()->route('communities.show', $community)
                        ->with('success', 'Community created successfully!');
    }

    /**
     * Display the specified community.
     */
    public function show(Community $community)
    {
        $community->load(['course', 'creator']);

        // Check if user is a member
        $isMember = Auth::check() && $community->members()->where('user_id', Auth::id())->exists();

        // Get recent posts
        $posts = $community->posts()
            ->with(['user', 'parent'])
            ->where('is_approved', true)
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get community stats
        $stats = [
            'total_posts' => $community->posts()->count(),
            'active_members' => $community->members()->where('last_active_at', '>=', now()->subDays(7))->count(),
            'total_members' => $community->member_count,
        ];

        return view('community.show', compact('community', 'isMember', 'posts', 'stats'));
    }

    /**
     * Show the form for editing the community.
     */
    public function edit(Community $community)
    {
        $this->authorize('update', $community);
        $courses = Course::where('status', 'published')->get();
        return view('community.edit', compact('community', 'courses'));
    }

    /**
     * Update the specified community.
     */
    public function update(Request $request, Community $community)
    {
        $this->authorize('update', $community);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:public,private,course-specific',
            'course_id' => 'nullable|exists:courses,id',
            'rules' => 'nullable|array',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $community->fill($request->all());
        $community->slug = Str::slug($request->name);

        // Handle banner upload
        if ($request->hasFile('banner')) {
            if ($community->banner) {
                Storage::disk('public')->delete($community->banner);
            }
            $path = $request->file('banner')->store('community-banners', 'public');
            $community->banner = $path;
        }

        $community->save();

        return redirect()->route('communities.show', $community)
                        ->with('success', 'Community updated successfully!');
    }

    /**
     * Remove the specified community.
     */
    public function destroy(Community $community)
    {
        $this->authorize('delete', $community);

        if ($community->banner) {
            Storage::disk('public')->delete($community->banner);
        }

        $community->delete();

        return redirect()->route('communities.index')
                        ->with('success', 'Community deleted successfully!');
    }

    /**
     * Join a community.
     */
    public function join(Community $community)
    {
        $user = Auth::user();

        if ($community->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('communities.show', $community)
                           ->with('info', 'You are already a member of this community.');
        }

        $community->members()->attach($user->id, [
            'role' => 'member',
            'joined_at' => now()
        ]);

        $community->increment('member_count');

        return redirect()->route('communities.show', $community)
                       ->with('success', 'Successfully joined the community!');
    }

    /**
     * Leave a community.
     */
    public function leave(Community $community)
    {
        $user = Auth::user();

        if (!$community->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('communities.show', $community)
                           ->with('error', 'You are not a member of this community.');
        }

        // Prevent creator from leaving
        if ($community->created_by === $user->id) {
            return redirect()->route('communities.show', $community)
                           ->with('error', 'Community creator cannot leave the community.');
        }

        $community->members()->detach($user->id);
        $community->decrement('member_count');

        return redirect()->route('communities.index')
                       ->with('success', 'Successfully left the community.');
    }
}
