<?php

namespace App\Http\Controllers\Community;

use App\Http\Controllers\Controller;
use App\Models\Community;
use App\Models\CommunityPost;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CommunityController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:view communities')->only(['index', 'show']);
        $this->middleware('can:create communities')->only(['create', 'store']);
        $this->middleware('can:edit communities')->only(['edit', 'update']);
        $this->middleware('can:delete communities')->only(['destroy']);
    }

    /**
     * Display a listing of communities.
     */
    public function index(Request $request)
    {
        $query = Community::with(['course', 'creator'])
            ->where('is_active', true)
            ->orderBy('member_count', 'desc');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Type filter
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        $communities = $query->paginate(12);

        // Get popular communities for sidebar
        $popularCommunities = Community::where('is_active', true)
            ->orderBy('member_count', 'desc')
            ->take(5)
            ->get();

        return view('community.index', compact('communities', 'popularCommunities'));
    }

    /**
     * Show the form for creating a new community.
     */
    public function create()
    {
        $courses = Course::where('status', 'published')->get();
        return view('community.create', compact('courses'));
    }

    /**
     * Store a newly created community.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:public,private,course-specific',
            'course_id' => 'nullable|exists:courses,id',
            'rules' => 'nullable|array',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $community = new Community($request->all());
        $community->slug = Str::slug($request->name);
        $community->created_by = Auth::id();

        // Handle banner upload
        if ($request->hasFile('banner')) {
            $path = $request->file('banner')->store('community-banners', 'public');
            $community->banner = $path;
        }

        $community->save();

        // Auto-join creator to community
        $community->members()->attach(Auth::id(), [
            'role' => 'admin',
            'joined_at' => now()
        ]);

        $community->increment('member_count');

        return redirect()->route('communities.show', $community)
                        ->with('success', 'Community created successfully!');
    }

    /**
     * Display the specified community.
     */
    public function show(Community $community)
    {
        $community->load(['course', 'creator']);

        // Check if user is a member
        $isMember = Auth::check() && $community->members()->where('user_id', Auth::id())->exists();

        // Get recent posts
        $posts = $community->posts()
            ->with(['user', 'parent'])
            ->where('is_approved', true)
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get community stats
        $stats = [
            'total_posts' => $community->posts()->count(),
            'active_members' => $community->members()->where('last_active_at', '>=', now()->subDays(7))->count(),
            'total_members' => $community->member_count,
        ];

        return view('community.show', compact('community', 'isMember', 'posts', 'stats'));
    }

    /**
     * Show the form for editing the community.
     */
    public function edit(Community $community)
    {
        $this->authorize('update', $community);
        $courses = Course::where('status', 'published')->get();
        return view('community.edit', compact('community', 'courses'));
    }

    /**
     * Update the specified community.
     */
    public function update(Request $request, Community $community)
    {
        $this->authorize('update', $community);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:public,private,course-specific',
            'course_id' => 'nullable|exists:courses,id',
            'rules' => 'nullable|array',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $community->fill($request->all());
        $community->slug = Str::slug($request->name);

        // Handle banner upload
        if ($request->hasFile('banner')) {
            if ($community->banner) {
                Storage::disk('public')->delete($community->banner);
            }
            $path = $request->file('banner')->store('community-banners', 'public');
            $community->banner = $path;
        }

        $community->save();

        return redirect()->route('communities.show', $community)
                        ->with('success', 'Community updated successfully!');
    }

    /**
     * Remove the specified community.
     */
    public function destroy(Community $community)
    {
        $this->authorize('delete', $community);

        if ($community->banner) {
            Storage::disk('public')->delete($community->banner);
        }

        $community->delete();

        return redirect()->route('communities.index')
                        ->with('success', 'Community deleted successfully!');
    }

    /**
     * Join a community.
     */
    public function join(Community $community)
    {
        $user = Auth::user();

        if ($community->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('communities.show', $community)
                           ->with('info', 'You are already a member of this community.');
        }

        $community->members()->attach($user->id, [
            'role' => 'member',
            'joined_at' => now()
        ]);

        $community->increment('member_count');

        return redirect()->route('communities.show', $community)
                       ->with('success', 'Successfully joined the community!');
    }

    /**
     * Leave a community.
     */
    public function leave(Community $community)
    {
        $user = Auth::user();

        if (!$community->members()->where('user_id', $user->id)->exists()) {
            return redirect()->route('communities.show', $community)
                           ->with('error', 'You are not a member of this community.');
        }

        // Prevent creator from leaving
        if ($community->created_by === $user->id) {
            return redirect()->route('communities.show', $community)
                           ->with('error', 'Community creator cannot leave the community.');
        }

        $community->members()->detach($user->id);
        $community->decrement('member_count');

        return redirect()->route('communities.index')
                       ->with('success', 'Successfully left the community.');
    }

    /**
     * Store a new post in the community.
     */
    public function storePost(Request $request, Community $community)
    {
        // Check if user is a member
        $isMember = Auth::check() && $community->members()->where('user_id', Auth::id())->exists();

        if (!$isMember) {
            return redirect()->route('communities.show', $community)
                ->with('error', 'You must be a member to post in this community.');
        }

        $request->validate([
            'content' => 'required|string|max:5000',
            'title' => 'nullable|string|max:255',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
        ]);

        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('community-attachments', 'public');
                $attachments[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
        }

        $post = CommunityPost::create([
            'community_id' => $community->id,
            'user_id' => Auth::id(),
            'title' => $request->title,
            'content' => $request->content,
            'attachments' => !empty($attachments) ? $attachments : null,
            'post_type' => 'post',
            'is_approved' => true, // Auto-approve for now
        ]);

        // Update community post count
        $community->increment('post_count');

        return redirect()->route('communities.show', $community)
            ->with('success', 'Post created successfully!');
    }

    /**
     * Get posts for a community.
     */
    public function posts(Request $request, Community $community)
    {
        $posts = $community->posts()
            ->with(['user', 'replies.user'])
            ->whereNull('parent_id') // Only top-level posts
            ->where('is_approved', true)
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $posts->items(),
                'pagination' => [
                    'current_page' => $posts->currentPage(),
                    'last_page' => $posts->lastPage(),
                    'per_page' => $posts->perPage(),
                    'total' => $posts->total(),
                ],
            ]);
        }

        return view('community.posts', compact('community', 'posts'));
    }

    /**
     * Update a community post.
     */
    public function updatePost(Request $request, Community $community, CommunityPost $post)
    {
        // Check if user owns the post or is admin
        if ($post->user_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $request->validate([
            'content' => 'required|string|max:5000',
            'title' => 'nullable|string|max:255',
        ]);

        $post->update([
            'title' => $request->title,
            'content' => $request->content,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Post updated successfully',
            'post' => $post->load('user')
        ]);
    }

    /**
     * Delete a community post.
     */
    public function destroyPost(Community $community, CommunityPost $post)
    {
        // Check if user owns the post or is admin
        if ($post->user_id !== Auth::id() && !Auth::user()->hasRole(['admin', 'super-admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $post->delete();

        // Update community post count
        $community->decrement('post_count');

        return response()->json([
            'success' => true,
            'message' => 'Post deleted successfully'
        ]);
    }
}
