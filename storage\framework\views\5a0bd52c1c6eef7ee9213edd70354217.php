<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', config('app.name', 'The Real World')); ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'The Real World - Escape the matrix through education and community'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', 'education, courses, community, learning, skills'); ?>">
    <meta name="author" content="The Real World">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $__env->yieldContent('og_title', config('app.name')); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('og_description', 'Escape the matrix through education and community'); ?>">
    <meta property="og:image" content="<?php echo $__env->yieldContent('og_image', asset('assets/images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $__env->yieldContent('twitter_title', config('app.name')); ?>">
    <meta name="twitter:description" content="<?php echo $__env->yieldContent('twitter_description', 'Escape the matrix through education and community'); ?>">
    <meta name="twitter:image" content="<?php echo $__env->yieldContent('twitter_image', asset('assets/images/twitter-card.jpg')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Custom CSS -->
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-sans antialiased">
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner" style="display: none;">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
        <?php echo $__env->make('layouts.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- Page Heading -->
        <?php if(isset($header)): ?>
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <?php echo e($header); ?>

                </div>
            </header>
        <?php endif; ?>

        <!-- Page Content -->
        <main>
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="alert alert-success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content"><?php echo e(session('success')); ?></div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content"><?php echo e(session('error')); ?></div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('warning')): ?>
                <div class="alert alert-warning">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content"><?php echo e(session('warning')); ?></div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="alert alert-info">
                    <div class="alert-icon">ℹ️</div>
                    <div class="alert-content"><?php echo e(session('info')); ?></div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="alert alert-error">
                    <div class="alert-icon">❌</div>
                    <div class="alert-content">
                        <ul class="error-list">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            <?php endif; ?>

            <!-- Main Content -->
            <?php if (! empty(trim($__env->yieldContent('content')))): ?>
                <?php echo $__env->yieldContent('content'); ?>
            <?php else: ?>
                <?php echo e($slot ?? ''); ?>

            <?php endif; ?>
        </main>
    </div>

    <!-- Custom JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>

    <script>
        // Global app functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });

            // Loading spinner for AJAX requests
            const showLoading = () => document.getElementById('loading-spinner').style.display = 'flex';
            const hideLoading = () => document.getElementById('loading-spinner').style.display = 'none';

            // Global AJAX setup
            if (window.fetch) {
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    showLoading();
                    return originalFetch.apply(this, args)
                        .finally(() => hideLoading());
                };
            }

            // Form submission loading states
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = 'Processing...';

                        // Re-enable after 10 seconds as fallback
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                        }, 10000);
                    }
                });
            });
        });

        // Global utility functions
        window.showAlert = function(type, message) {
            const alertHtml = `
                <div class="alert alert-${type}">
                    <div class="alert-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</div>
                    <div class="alert-content">${message}</div>
                    <button type="button" class="alert-close" onclick="this.parentElement.remove()">✕</button>
                </div>
            `;

            const main = document.querySelector('main');
            if (main) {
                main.insertAdjacentHTML('afterbegin', alertHtml);

                // Auto-hide after 5 seconds
                const newAlert = main.querySelector('.alert');
                setTimeout(() => {
                    newAlert.style.opacity = '0';
                    setTimeout(() => newAlert.remove(), 300);
                }, 5000);
            }
        };
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\Work Space\THS\LMS\resources\views/layouts/app.blade.php ENDPATH**/ ?>