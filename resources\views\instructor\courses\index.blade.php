@extends('layouts.app')

@section('title', 'My Courses')

@section('content')
<div class="instructor-courses">
    <!-- Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-info">
                <h1>📚 My Courses</h1>
                <p>Manage and track your course content</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('courses.create') }}" class="btn btn-primary">
                    ➕ Create New Course
                </a>
            </div>
        </div>
    </div>

    <!-- Course Stats -->
    <div class="course-stats">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📚</div>
                <div class="stat-content">
                    <h3>Total Courses</h3>
                    <p class="stat-value">{{ $courses->total() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3>Published</h3>
                    <p class="stat-value">{{ $courses->where('status', 'published')->count() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📝</div>
                <div class="stat-content">
                    <h3>Drafts</h3>
                    <p class="stat-value">{{ $courses->where('status', 'draft')->count() }}</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>Total Students</h3>
                    <p class="stat-value">{{ $courses->sum('enrollments_count') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Course List -->
    <div class="courses-list">
        <div class="list-header">
            <h2>Your Courses</h2>
            <div class="list-controls">
                <div class="search-box">
                    <input type="text" placeholder="Search courses..." id="courseSearch">
                    <span class="search-icon">🔍</span>
                </div>
                <select class="filter-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                    <option value="pending">Pending Review</option>
                </select>
            </div>
        </div>

        @if($courses->count() > 0)
            <div class="courses-grid">
                @foreach($courses as $course)
                    <div class="course-card" data-status="{{ $course->status }}">
                        <div class="course-thumbnail">
                            @if($course->thumbnail)
                                <img src="{{ Storage::url($course->thumbnail) }}" alt="{{ $course->title }}">
                            @else
                                <div class="thumbnail-placeholder">📚</div>
                            @endif
                            
                            <div class="course-status {{ $course->status }}">
                                @switch($course->status)
                                    @case('published')
                                        ✅ Published
                                        @break
                                    @case('draft')
                                        📝 Draft
                                        @break
                                    @case('pending')
                                        ⏳ Pending Review
                                        @break
                                    @default
                                        📋 {{ ucfirst($course->status) }}
                                @endswitch
                            </div>
                        </div>

                        <div class="course-content">
                            <div class="course-header">
                                <h3 class="course-title">
                                    <a href="{{ route('courses.show', $course) }}">{{ $course->title }}</a>
                                </h3>
                                <div class="course-category">{{ $course->category->name }}</div>
                            </div>

                            <p class="course-description">{{ Str::limit($course->short_description, 100) }}</p>

                            <div class="course-stats">
                                <div class="stat">
                                    <span class="stat-icon">👥</span>
                                    <span class="stat-text">{{ $course->enrollments_count }} students</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">📖</span>
                                    <span class="stat-text">{{ $course->lessons_count }} lessons</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-text">{{ number_format($course->rating ?? 0, 1) }}</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-icon">💰</span>
                                    <span class="stat-text">${{ number_format($course->price, 2) }}</span>
                                </div>
                            </div>

                            <div class="course-actions">
                                <a href="{{ route('courses.edit', $course) }}" class="action-btn edit">
                                    ✏️ Edit
                                </a>
                                <a href="{{ route('instructor.courses.students', $course) }}" class="action-btn students">
                                    👥 Students
                                </a>
                                <a href="{{ route('instructor.lessons.index', $course) }}" class="action-btn lessons">
                                    📖 Lessons
                                </a>
                                
                                @if($course->status === 'draft')
                                    <form method="POST" action="{{ route('instructor.courses.publish', $course) }}" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="action-btn publish">
                                            🚀 Publish
                                        </button>
                                    </form>
                                @elseif($course->status === 'published')
                                    <form method="POST" action="{{ route('instructor.courses.unpublish', $course) }}" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="action-btn unpublish">
                                            📝 Unpublish
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>

                        <div class="course-footer">
                            <small class="course-updated">
                                Updated {{ $course->updated_at->diffForHumans() }}
                            </small>
                            <div class="course-revenue">
                                Revenue: ${{ number_format($course->total_revenue ?? 0, 2) }}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                {{ $courses->links() }}
            </div>
        @else
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <h3>No Courses Yet</h3>
                <p>Start creating your first course to share your knowledge with the world!</p>
                <a href="{{ route('courses.create') }}" class="btn btn-primary">
                    ➕ Create Your First Course
                </a>
            </div>
        @endif
    </div>
</div>

<style>
.instructor-courses {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-info h1 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-info p {
    color: #a0a0a0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.course-stats {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-content h3 {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: #3b82f6;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.courses-list {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.list-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.list-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0a0a0;
}

.filter-select {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.course-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.course-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.course-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.course-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.course-status.published {
    background: rgba(34, 197, 94, 0.9);
    color: white;
}

.course-status.draft {
    background: rgba(245, 158, 11, 0.9);
    color: white;
}

.course-status.pending {
    background: rgba(59, 130, 246, 0.9);
    color: white;
}

.course-content {
    padding: 1.5rem;
}

.course-title a {
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
}

.course-title a:hover {
    color: #3b82f6;
}

.course-category {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    margin-top: 0.5rem;
}

.course-description {
    color: #a0a0a0;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 1rem 0;
}

.course-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-icon {
    font-size: 1rem;
}

.stat-text {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.course-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn.edit {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.action-btn.students {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.action-btn.lessons {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.action-btn.publish {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.action-btn.unpublish {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.action-btn:hover {
    transform: translateY(-1px);
    opacity: 0.9;
}

.course-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.course-updated {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.course-revenue {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 600;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .instructor-courses {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .list-controls {
        justify-content: space-between;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .course-stats {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Search functionality
document.getElementById('courseSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        const title = card.querySelector('.course-title a').textContent.toLowerCase();
        const description = card.querySelector('.course-description').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// Filter functionality
document.getElementById('statusFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        const status = card.dataset.status;
        
        if (filterValue === '' || status === filterValue) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});
</script>
@endsection
