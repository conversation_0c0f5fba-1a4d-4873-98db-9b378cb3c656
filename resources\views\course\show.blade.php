<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $course->title }} - {{ config('app.name') }}</title>
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/course-detail.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="{{ route('home') }}">The Real World</a>
            </div>
            <ul class="navbar-nav">
                <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('courses.index') }}">Courses</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="#chat">Chat</a></li>
                <li><a href="#profile">Profile</a></li>
                <li>
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-sm">Logout</button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Course Hero -->
    <section class="course-hero">
        <div class="container">
            <div class="course-hero-content">
                <div class="course-info">
                    <div class="course-breadcrumb">
                        <a href="{{ route('courses.index') }}">Courses</a>
                        <span>/</span>
                        <a href="#">{{ $course->category->name }}</a>
                        <span>/</span>
                        <span>{{ $course->title }}</span>
                    </div>
                    
                    <h1 class="course-title">{{ $course->title }}</h1>
                    <p class="course-description">{{ $course->short_description }}</p>
                    
                    <div class="course-meta">
                        <div class="meta-item">
                            <span class="meta-label">Instructor:</span>
                            <span class="meta-value">{{ $course->instructor->name }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Level:</span>
                            <span class="meta-value">{{ ucfirst($course->difficulty_level) }}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Duration:</span>
                            <span class="meta-value">{{ $course->duration_hours }} hours</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Students:</span>
                            <span class="meta-value">{{ $course->total_students }}</span>
                        </div>
                    </div>
                    
                    @if($isEnrolled)
                        <div class="progress-section">
                            <div class="progress-header">
                                <span>Your Progress</span>
                                <span class="progress-percentage">{{ $progress }}%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {{ $progress }}%"></div>
                            </div>
                        </div>
                    @endif
                </div>
                
                <div class="course-sidebar">
                    <div class="course-preview">
                        @if($course->thumbnail)
                            <img src="{{ Storage::url($course->thumbnail) }}" alt="{{ $course->title }}">
                        @else
                            <div class="course-placeholder">
                                <span class="course-icon">🎯</span>
                            </div>
                        @endif
                        
                        @if($course->video_preview)
                            <div class="preview-overlay">
                                <button class="play-button" data-video="{{ $course->video_preview }}">
                                    ▶️ Preview
                                </button>
                            </div>
                        @endif
                    </div>
                    
                    <div class="course-pricing">
                        @if($course->is_free)
                            <div class="price free">FREE</div>
                        @else
                            <div class="price">${{ $course->price }}</div>
                        @endif
                        
                        @if($isEnrolled)
                            <a href="#" class="btn btn-success btn-block">
                                ✅ Enrolled - Continue Learning
                            </a>
                        @else
                            <form method="POST" action="{{ route('courses.enroll', $course) }}">
                                @csrf
                                <button type="submit" class="btn btn-primary btn-block">
                                    @if($course->is_free)
                                        Enroll for Free
                                    @else
                                        Enroll Now - ${{ $course->price }}
                                    @endif
                                </button>
                            </form>
                        @endif
                    </div>
                    
                    <div class="course-includes">
                        <h4>This course includes:</h4>
                        <ul>
                            <li>📹 {{ $course->total_lessons }} video lessons</li>
                            <li>📱 Mobile and desktop access</li>
                            <li>🏆 Certificate of completion</li>
                            <li>💬 Community access</li>
                            <li>🔄 Lifetime updates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="course-content">
        <div class="container">
            <div class="content-tabs">
                <button class="tab-button active" data-tab="overview">Overview</button>
                <button class="tab-button" data-tab="curriculum">Curriculum</button>
                <button class="tab-button" data-tab="reviews">Reviews</button>
            </div>
            
            <!-- Overview Tab -->
            <div class="tab-content active" id="overview">
                <div class="course-overview">
                    <div class="overview-section">
                        <h3>About This Course</h3>
                        <div class="course-full-description">
                            {!! nl2br(e($course->description)) !!}
                        </div>
                    </div>
                    
                    @if($course->what_you_learn && count($course->what_you_learn) > 0)
                        <div class="overview-section">
                            <h3>What You'll Learn</h3>
                            <ul class="learning-objectives">
                                @foreach($course->what_you_learn as $objective)
                                    <li>✅ {{ $objective }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    @if($course->requirements && count($course->requirements) > 0)
                        <div class="overview-section">
                            <h3>Requirements</h3>
                            <ul class="requirements-list">
                                @foreach($course->requirements as $requirement)
                                    <li>• {{ $requirement }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Curriculum Tab -->
            <div class="tab-content" id="curriculum">
                <div class="course-curriculum">
                    <div class="curriculum-header">
                        <h3>Course Curriculum</h3>
                        <span class="lesson-count">{{ $course->lessons->count() }} lessons</span>
                    </div>
                    
                    <div class="lessons-list">
                        @forelse($course->lessons as $index => $lesson)
                            <div class="lesson-item {{ $isEnrolled ? 'accessible' : 'locked' }}">
                                <div class="lesson-number">{{ $index + 1 }}</div>
                                <div class="lesson-content">
                                    <h4 class="lesson-title">{{ $lesson->title }}</h4>
                                    @if($lesson->description)
                                        <p class="lesson-description">{{ $lesson->description }}</p>
                                    @endif
                                    <div class="lesson-meta">
                                        @if($lesson->video_duration)
                                            <span class="lesson-duration">⏱️ {{ $lesson->video_duration }}</span>
                                        @endif
                                        <span class="lesson-type">{{ ucfirst($lesson->lesson_type) }}</span>
                                    </div>
                                </div>
                                <div class="lesson-actions">
                                    @if($isEnrolled)
                                        <a href="#" class="btn btn-sm btn-primary">Watch</a>
                                    @else
                                        <span class="locked-icon">🔒</span>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="empty-lessons">
                                <p>No lessons available yet.</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
            
            <!-- Reviews Tab -->
            <div class="tab-content" id="reviews">
                <div class="course-reviews">
                    <div class="reviews-header">
                        <h3>Student Reviews</h3>
                        <div class="rating-summary">
                            <span class="average-rating">{{ number_format($course->rating, 1) }}</span>
                            <div class="stars">
                                @for($i = 1; $i <= 5; $i++)
                                    <span class="star {{ $i <= $course->rating ? 'filled' : '' }}">⭐</span>
                                @endfor
                            </div>
                            <span class="review-count">({{ $course->rating_count }} reviews)</span>
                        </div>
                    </div>
                    
                    <div class="reviews-list">
                        @forelse($reviews as $review)
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <span class="reviewer-name">{{ $review->user->name }}</span>
                                        <div class="review-rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <span class="star {{ $i <= $review->rating ? 'filled' : '' }}">⭐</span>
                                            @endfor
                                        </div>
                                    </div>
                                    <span class="review-date">{{ $review->created_at->diffForHumans() }}</span>
                                </div>
                                @if($review->review)
                                    <p class="review-text">{{ $review->review }}</p>
                                @endif
                            </div>
                        @empty
                            <div class="empty-reviews">
                                <p>No reviews yet. Be the first to review this course!</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Courses -->
    @if($relatedCourses->count() > 0)
        <section class="related-courses">
            <div class="container">
                <h3>Related Courses</h3>
                <div class="courses-grid">
                    @foreach($relatedCourses as $relatedCourse)
                        <div class="course-card">
                            <div class="course-thumbnail">
                                @if($relatedCourse->thumbnail)
                                    <img src="{{ Storage::url($relatedCourse->thumbnail) }}" alt="{{ $relatedCourse->title }}">
                                @else
                                    <div class="course-placeholder">
                                        <span class="course-icon">🎯</span>
                                    </div>
                                @endif
                            </div>
                            <div class="course-content">
                                <h4><a href="{{ route('courses.show', $relatedCourse) }}">{{ $relatedCourse->title }}</a></h4>
                                <p>{{ $relatedCourse->short_description }}</p>
                                <div class="course-price">
                                    @if($relatedCourse->is_free)
                                        <span class="price free">FREE</span>
                                    @else
                                        <span class="price">${{ $relatedCourse->price }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <script src="{{ asset('assets/js/app.js') }}"></script>
    <script src="{{ asset('assets/js/course-detail.js') }}"></script>
</body>
</html>
