<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\EmailVerificationController;
use App\Http\Controllers\Auth\PasswordResetController;
use App\Http\Controllers\Auth\ProfileController;
use App\Http\Controllers\Course\CourseController;
use App\Http\Controllers\Course\EnrollmentController;
use App\Http\Controllers\Community\CommunityController;
use App\Http\Controllers\Chat\ChatRoomController;
use App\Http\Controllers\Chat\MessageController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\CourseManagementController;
use App\Http\Controllers\Admin\RoleManagementController;
use App\Http\Controllers\Admin\PaymentManagementController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\PaymentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Landing Page
Route::get('/', function () {
    return view('landing.index');
})->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);

    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);

    Route::get('/forgot-password', [PasswordResetController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLinkEmail'])->name('password.email');

    Route::get('/reset-password/{token}', [PasswordResetController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [PasswordResetController::class, 'reset'])->name('password.update');
});

// Email Verification Routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [EmailVerificationController::class, 'show'])->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [EmailVerificationController::class, 'verify'])->name('verification.verify');
    Route::post('/email/verification-notification', [EmailVerificationController::class, 'resend'])->name('verification.send');
});

// Authenticated Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard.index');
    })->name('dashboard');

    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Course Routes
    Route::resource('courses', CourseController::class);
    Route::post('/courses/{course}/enroll', [EnrollmentController::class, 'enroll'])->name('courses.enroll');
    Route::delete('/courses/{course}/unenroll', [EnrollmentController::class, 'destroy'])->name('courses.unenroll');
    Route::get('/my-courses', [EnrollmentController::class, 'index'])->name('my-courses');
    Route::get('/courses/{course}/enrollment', [EnrollmentController::class, 'show'])->name('course.enrollment');
    Route::get('/courses/{course}/stats', [EnrollmentController::class, 'statistics'])->name('course.stats');

    // Community Routes
    Route::resource('communities', CommunityController::class);
    Route::post('/communities/{community}/join', [CommunityController::class, 'join'])->name('communities.join');
    Route::delete('/communities/{community}/leave', [CommunityController::class, 'leave'])->name('communities.leave');

    // Community Posts Routes
    Route::post('/communities/{community}/posts', [CommunityController::class, 'storePost'])->name('communities.posts.store');
    Route::get('/communities/{community}/posts', [CommunityController::class, 'posts'])->name('communities.posts.index');
    Route::put('/communities/{community}/posts/{post}', [CommunityController::class, 'updatePost'])->name('communities.posts.update');
    Route::delete('/communities/{community}/posts/{post}', [CommunityController::class, 'destroyPost'])->name('communities.posts.destroy');

    // Chat Routes
    Route::get('/chat', [ChatRoomController::class, 'index'])->name('chat.index');
    Route::resource('chat', ChatRoomController::class)->except(['index']);
    Route::post('/chat/{chatRoom}/join', [ChatRoomController::class, 'join'])->name('chat.join');
    Route::delete('/chat/{chatRoom}/leave', [ChatRoomController::class, 'leave'])->name('chat.leave');
    Route::post('/chat/{chatRoom}/messages', [MessageController::class, 'store'])->name('chat.messages.store');
    Route::get('/chat/{chatRoom}/messages', [MessageController::class, 'index'])->name('chat.messages.index');
    Route::put('/messages/{message}', [MessageController::class, 'update'])->name('messages.update');
    Route::delete('/messages/{message}', [MessageController::class, 'destroy'])->name('messages.destroy');

    // Search Routes
    Route::get('/search', [SearchController::class, 'index'])->name('search.index');
    Route::get('/search/autocomplete', [SearchController::class, 'autocomplete'])->name('search.autocomplete');
    Route::get('/courses/{course}/search', [SearchController::class, 'searchCourse'])->name('search.course');

    // Payment Routes
    Route::prefix('payment')->name('payment.')->group(function () {
        Route::get('/courses/{course}/options', [PaymentController::class, 'showOptions'])->name('options');
        Route::post('/courses/{course}/process', [PaymentController::class, 'process'])->name('process');
        Route::get('/success', [PaymentController::class, 'success'])->name('success');
        Route::get('/cancel', [PaymentController::class, 'cancel'])->name('cancel');
    });
});

// Instructor Routes (Protected by instructor middleware)
Route::middleware(['auth', 'instructor'])->prefix('instructor')->name('instructor.')->group(function () {
    // Instructor Dashboard
    Route::get('/', [InstructorController::class, 'dashboard'])->name('dashboard');

    // Course Management for Instructors
    Route::get('/courses', [InstructorController::class, 'courses'])->name('courses.index');
    Route::get('/courses/create', [CourseController::class, 'create'])->name('courses.create')->middleware('can:create courses');
    Route::post('/courses', [CourseController::class, 'store'])->name('courses.store')->middleware('can:create courses');
    Route::get('/courses/{course}/edit', [CourseController::class, 'edit'])->name('courses.edit')->middleware('can:update,course');
    Route::put('/courses/{course}', [CourseController::class, 'update'])->name('courses.update')->middleware('can:update,course');
    Route::delete('/courses/{course}', [CourseController::class, 'destroy'])->name('courses.destroy')->middleware('can:delete,course');

    // Course Publishing
    Route::post('/courses/{course}/publish', [InstructorController::class, 'publishCourse'])->name('courses.publish')->middleware('can:publish,course');
    Route::post('/courses/{course}/unpublish', [InstructorController::class, 'unpublishCourse'])->name('courses.unpublish')->middleware('can:publish,course');

    // Lesson Management
    Route::get('/courses/{course}/lessons', [InstructorController::class, 'lessons'])->name('lessons.index');
    Route::get('/courses/{course}/lessons/create', [LessonController::class, 'create'])->name('lessons.create')->middleware('can:create lessons');
    Route::post('/courses/{course}/lessons', [LessonController::class, 'store'])->name('lessons.store')->middleware('can:create lessons');
    Route::get('/lessons/{lesson}/edit', [LessonController::class, 'edit'])->name('lessons.edit')->middleware('can:edit lessons');
    Route::put('/lessons/{lesson}', [LessonController::class, 'update'])->name('lessons.update')->middleware('can:edit lessons');
    Route::delete('/lessons/{lesson}', [LessonController::class, 'destroy'])->name('lessons.destroy')->middleware('can:delete lessons');

    // Student Management
    Route::get('/courses/{course}/students', [InstructorController::class, 'students'])->name('courses.students')->middleware('can:manageEnrollments,course');
    Route::get('/students', [InstructorController::class, 'allStudents'])->name('students.index');

    // Analytics for Instructors
    Route::get('/analytics', [InstructorController::class, 'analytics'])->name('analytics')->middleware('can:view analytics');
    Route::get('/courses/{course}/analytics', [InstructorController::class, 'courseAnalytics'])->name('courses.analytics')->middleware('can:viewAnalytics,course');

    // Earnings
    Route::get('/earnings', [InstructorController::class, 'earnings'])->name('earnings')->middleware('can:view payments');

    // Live Streaming
    Route::get('/streams', [InstructorController::class, 'streams'])->name('streams.index')->middleware('can:create streams');
    Route::get('/streams/create', [StreamController::class, 'create'])->name('streams.create')->middleware('can:create streams');
    Route::post('/streams', [StreamController::class, 'store'])->name('streams.store')->middleware('can:create streams');
    Route::get('/streams/{stream}', [StreamController::class, 'show'])->name('streams.show')->middleware('can:manage streams');
    Route::put('/streams/{stream}', [StreamController::class, 'update'])->name('streams.update')->middleware('can:manage streams');
    Route::delete('/streams/{stream}', [StreamController::class, 'destroy'])->name('streams.destroy')->middleware('can:manage streams');
});

// Admin Routes (Protected by admin middleware)
Route::middleware(['auth', 'can:access admin panel'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', [AdminController::class, 'index'])->name('dashboard');
    Route::get('/system-status', [AdminController::class, 'systemStatus'])->name('system-status');

    // User Management
    Route::resource('users', UserManagementController::class)->middleware('can:view users');
    Route::post('/users/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('users.toggle-status')->middleware('can:ban users');
    Route::post('/users/{user}/impersonate', [UserManagementController::class, 'impersonate'])->name('users.impersonate')->middleware('can:impersonate,user');
    Route::post('/stop-impersonating', [UserManagementController::class, 'stopImpersonating'])->name('stop-impersonating');
    Route::get('/users-export', [UserManagementController::class, 'export'])->name('users.export')->middleware('can:export reports');

    // Course Management
    Route::resource('courses', CourseManagementController::class)->middleware('can:view courses');
    Route::post('/courses/{course}/toggle-status', [CourseManagementController::class, 'toggleStatus'])->name('courses.toggle-status')->middleware('can:approve courses');
    Route::get('/courses-export', [CourseManagementController::class, 'export'])->name('courses.export')->middleware('can:export reports');

    // Payment Management
    Route::resource('payments', PaymentManagementController::class)->only(['index', 'show'])->middleware('can:view payments');
    Route::post('/payments/{payment}/refund', [PaymentManagementController::class, 'processRefund'])->name('payments.refund')->middleware('can:refund,payment');
    Route::get('/payments-export', [PaymentManagementController::class, 'export'])->name('payments.export')->middleware('can:export reports');

    // Analytics
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics')->middleware('can:view detailed analytics');
    Route::get('/analytics/revenue', [AnalyticsController::class, 'revenueAnalytics'])->name('analytics.revenue')->middleware('can:view financial analytics');
    Route::get('/analytics/users', [AnalyticsController::class, 'userAnalytics'])->name('analytics.users')->middleware('can:view user analytics');
    Route::get('/analytics/courses', [AnalyticsController::class, 'courseAnalytics'])->name('analytics.courses')->middleware('can:view detailed analytics');

    // Settings
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');

    // Role Management
    Route::resource('roles', RoleManagementController::class)->middleware('can:manage roles');
    Route::post('/roles/assign', [RoleManagementController::class, 'assignRole'])->name('roles.assign')->middleware('can:manage roles');
    Route::post('/roles/remove', [RoleManagementController::class, 'removeRole'])->name('roles.remove')->middleware('can:manage roles');
    Route::get('/roles/{role}/users', [RoleManagementController::class, 'getRoleUsers'])->name('roles.users')->middleware('can:manage roles');
    Route::post('/roles/bulk-assign', [RoleManagementController::class, 'bulkAssignRoles'])->name('roles.bulk-assign')->middleware('can:manage roles');
    Route::get('/roles-export', [RoleManagementController::class, 'export'])->name('roles.export')->middleware('can:export reports');
});
