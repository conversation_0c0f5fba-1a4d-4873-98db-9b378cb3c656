<?php

return array (
  'ADP' => 
  array (
    0 => 'ADP',
    1 => 0,
    2 => 0,
  ),
  'AED' => 
  array (
    0 => 'AED',
  ),
  'AFA' => 
  array (
    0 => 'AFA',
  ),
  'AFN' => 
  array (
    0 => 'AFN',
    1 => 0,
    2 => 0,
  ),
  'ALK' => 
  array (
    0 => 'ALK',
  ),
  'ALL' => 
  array (
    0 => 'ALL',
    1 => 0,
    2 => 0,
  ),
  'AMD' => 
  array (
    0 => 'AMD',
    1 => 2,
    2 => 0,
  ),
  'ANG' => 
  array (
    0 => 'ANG',
  ),
  'AOA' => 
  array (
    0 => 'AOA',
  ),
  'AOK' => 
  array (
    0 => 'AOK',
  ),
  'AON' => 
  array (
    0 => 'AON',
  ),
  'AOR' => 
  array (
    0 => 'AOR',
  ),
  'ARA' => 
  array (
    0 => 'ARA',
  ),
  'ARL' => 
  array (
    0 => 'ARL',
  ),
  'ARM' => 
  array (
    0 => 'ARM',
  ),
  'ARP' => 
  array (
    0 => 'ARP',
  ),
  'ARS' => 
  array (
    0 => 'ARS',
  ),
  'ATS' => 
  array (
    0 => 'ATS',
  ),
  'AUD' => 
  array (
    0 => 'A$',
  ),
  'AWG' => 
  array (
    0 => 'AWG',
  ),
  'AZM' => 
  array (
    0 => 'AZM',
  ),
  'AZN' => 
  array (
    0 => 'AZN',
  ),
  'BAD' => 
  array (
    0 => 'BAD',
  ),
  'BAM' => 
  array (
    0 => 'BAM',
  ),
  'BAN' => 
  array (
    0 => 'BAN',
  ),
  'BBD' => 
  array (
    0 => 'BBD',
  ),
  'BDT' => 
  array (
    0 => 'BDT',
  ),
  'BEC' => 
  array (
    0 => 'BEC',
  ),
  'BEF' => 
  array (
    0 => 'BEF',
  ),
  'BEL' => 
  array (
    0 => 'BEL',
  ),
  'BGL' => 
  array (
    0 => 'BGL',
  ),
  'BGM' => 
  array (
    0 => 'BGM',
  ),
  'BGN' => 
  array (
    0 => 'BGN',
  ),
  'BGO' => 
  array (
    0 => 'BGO',
  ),
  'BHD' => 
  array (
    0 => 'BHD',
    1 => 3,
    2 => 0,
  ),
  'BIF' => 
  array (
    0 => 'BIF',
    1 => 0,
    2 => 0,
  ),
  'BMD' => 
  array (
    0 => 'BMD',
  ),
  'BND' => 
  array (
    0 => 'BND',
  ),
  'BOB' => 
  array (
    0 => 'BOB',
  ),
  'BOL' => 
  array (
    0 => 'BOL',
  ),
  'BOP' => 
  array (
    0 => 'BOP',
  ),
  'BOV' => 
  array (
    0 => 'BOV',
  ),
  'BRB' => 
  array (
    0 => 'BRB',
  ),
  'BRC' => 
  array (
    0 => 'BRC',
  ),
  'BRE' => 
  array (
    0 => 'BRE',
  ),
  'BRL' => 
  array (
    0 => 'R$',
  ),
  'BRN' => 
  array (
    0 => 'BRN',
  ),
  'BRR' => 
  array (
    0 => 'BRR',
  ),
  'BRZ' => 
  array (
    0 => 'BRZ',
  ),
  'BSD' => 
  array (
    0 => 'BSD',
  ),
  'BTN' => 
  array (
    0 => 'BTN',
  ),
  'BUK' => 
  array (
    0 => 'BUK',
  ),
  'BWP' => 
  array (
    0 => 'BWP',
  ),
  'BYB' => 
  array (
    0 => 'BYB',
  ),
  'BYN' => 
  array (
    0 => 'BYN',
    1 => 2,
    2 => 0,
  ),
  'BYR' => 
  array (
    0 => 'BYR',
    1 => 0,
    2 => 0,
  ),
  'BZD' => 
  array (
    0 => 'BZD',
  ),
  'CAD' => 
  array (
    0 => 'CA$',
    1 => 2,
    2 => 0,
  ),
  'CDF' => 
  array (
    0 => 'CDF',
  ),
  'CHE' => 
  array (
    0 => 'CHE',
  ),
  'CHF' => 
  array (
    0 => 'CHF',
    1 => 2,
    2 => 0,
  ),
  'CHW' => 
  array (
    0 => 'CHW',
  ),
  'CLE' => 
  array (
    0 => 'CLE',
  ),
  'CLF' => 
  array (
    0 => 'CLF',
    1 => 4,
    2 => 0,
  ),
  'CLP' => 
  array (
    0 => 'CLP',
    1 => 0,
    2 => 0,
  ),
  'CNH' => 
  array (
    0 => 'CNH',
  ),
  'CNX' => 
  array (
    0 => 'CNX',
  ),
  'CNY' => 
  array (
    0 => 'CN¥',
  ),
  'COP' => 
  array (
    0 => 'COP',
    1 => 2,
    2 => 0,
  ),
  'COU' => 
  array (
    0 => 'COU',
  ),
  'CRC' => 
  array (
    0 => 'CRC',
    1 => 2,
    2 => 0,
  ),
  'CSD' => 
  array (
    0 => 'CSD',
  ),
  'CSK' => 
  array (
    0 => 'CSK',
  ),
  'CUC' => 
  array (
    0 => 'CUC',
  ),
  'CUP' => 
  array (
    0 => 'CUP',
  ),
  'CVE' => 
  array (
    0 => 'CVE',
  ),
  'CYP' => 
  array (
    0 => 'CYP',
  ),
  'CZK' => 
  array (
    0 => 'CZK',
    1 => 2,
    2 => 0,
  ),
  'DDM' => 
  array (
    0 => 'DDM',
  ),
  'DEM' => 
  array (
    0 => 'DEM',
  ),
  'DJF' => 
  array (
    0 => 'DJF',
    1 => 0,
    2 => 0,
  ),
  'DKK' => 
  array (
    0 => 'DKK',
    1 => 2,
    2 => 0,
  ),
  'DOP' => 
  array (
    0 => 'DOP',
  ),
  'DZD' => 
  array (
    0 => 'DZD',
  ),
  'ECS' => 
  array (
    0 => 'ECS',
  ),
  'ECV' => 
  array (
    0 => 'ECV',
  ),
  'EEK' => 
  array (
    0 => 'EEK',
  ),
  'EGP' => 
  array (
    0 => 'EGP',
  ),
  'ERN' => 
  array (
    0 => 'ERN',
  ),
  'ESA' => 
  array (
    0 => 'ESA',
  ),
  'ESB' => 
  array (
    0 => 'ESB',
  ),
  'ESP' => 
  array (
    0 => 'ESP',
    1 => 0,
    2 => 0,
  ),
  'ETB' => 
  array (
    0 => 'ETB',
  ),
  'EUR' => 
  array (
    0 => '€',
  ),
  'FIM' => 
  array (
    0 => 'FIM',
  ),
  'FJD' => 
  array (
    0 => 'FJD',
  ),
  'FKP' => 
  array (
    0 => 'FKP',
  ),
  'FRF' => 
  array (
    0 => 'FRF',
  ),
  'GBP' => 
  array (
    0 => '£',
  ),
  'GEK' => 
  array (
    0 => 'GEK',
  ),
  'GEL' => 
  array (
    0 => 'GEL',
  ),
  'GHC' => 
  array (
    0 => 'GHC',
  ),
  'GHS' => 
  array (
    0 => 'GHS',
  ),
  'GIP' => 
  array (
    0 => 'GIP',
  ),
  'GMD' => 
  array (
    0 => 'GMD',
  ),
  'GNF' => 
  array (
    0 => 'GNF',
    1 => 0,
    2 => 0,
  ),
  'GNS' => 
  array (
    0 => 'GNS',
  ),
  'GQE' => 
  array (
    0 => 'GQE',
  ),
  'GRD' => 
  array (
    0 => 'GRD',
  ),
  'GTQ' => 
  array (
    0 => 'GTQ',
  ),
  'GWE' => 
  array (
    0 => 'GWE',
  ),
  'GWP' => 
  array (
    0 => 'GWP',
  ),
  'GYD' => 
  array (
    0 => 'GYD',
    1 => 2,
    2 => 0,
  ),
  'HKD' => 
  array (
    0 => 'HK$',
  ),
  'HNL' => 
  array (
    0 => 'HNL',
  ),
  'HRD' => 
  array (
    0 => 'HRD',
  ),
  'HRK' => 
  array (
    0 => 'HRK',
  ),
  'HTG' => 
  array (
    0 => 'HTG',
  ),
  'HUF' => 
  array (
    0 => 'HUF',
    1 => 2,
    2 => 0,
  ),
  'IDR' => 
  array (
    0 => 'IDR',
    1 => 2,
    2 => 0,
  ),
  'IEP' => 
  array (
    0 => 'IEP',
  ),
  'ILP' => 
  array (
    0 => 'ILP',
  ),
  'ILR' => 
  array (
    0 => 'ILR',
  ),
  'ILS' => 
  array (
    0 => '₪',
  ),
  'INR' => 
  array (
    0 => '₹',
  ),
  'IQD' => 
  array (
    0 => 'IQD',
    1 => 0,
    2 => 0,
  ),
  'IRR' => 
  array (
    0 => 'IRR',
    1 => 0,
    2 => 0,
  ),
  'ISJ' => 
  array (
    0 => 'ISJ',
  ),
  'ISK' => 
  array (
    0 => 'ISK',
    1 => 0,
    2 => 0,
  ),
  'ITL' => 
  array (
    0 => 'ITL',
    1 => 0,
    2 => 0,
  ),
  'JMD' => 
  array (
    0 => 'JMD',
  ),
  'JOD' => 
  array (
    0 => 'JOD',
    1 => 3,
    2 => 0,
  ),
  'JPY' => 
  array (
    0 => '¥',
    1 => 0,
    2 => 0,
  ),
  'KES' => 
  array (
    0 => 'KES',
  ),
  'KGS' => 
  array (
    0 => 'KGS',
  ),
  'KHR' => 
  array (
    0 => 'KHR',
  ),
  'KMF' => 
  array (
    0 => 'KMF',
    1 => 0,
    2 => 0,
  ),
  'KPW' => 
  array (
    0 => 'KPW',
    1 => 0,
    2 => 0,
  ),
  'KRH' => 
  array (
    0 => 'KRH',
  ),
  'KRO' => 
  array (
    0 => 'KRO',
  ),
  'KRW' => 
  array (
    0 => '₩',
    1 => 0,
    2 => 0,
  ),
  'KWD' => 
  array (
    0 => 'KWD',
    1 => 3,
    2 => 0,
  ),
  'KYD' => 
  array (
    0 => 'KYD',
  ),
  'KZT' => 
  array (
    0 => 'KZT',
  ),
  'LAK' => 
  array (
    0 => 'LAK',
    1 => 0,
    2 => 0,
  ),
  'LBP' => 
  array (
    0 => 'LBP',
    1 => 0,
    2 => 0,
  ),
  'LKR' => 
  array (
    0 => 'LKR',
  ),
  'LRD' => 
  array (
    0 => 'LRD',
  ),
  'LSL' => 
  array (
    0 => 'LSL',
  ),
  'LTL' => 
  array (
    0 => 'LTL',
  ),
  'LTT' => 
  array (
    0 => 'LTT',
  ),
  'LUC' => 
  array (
    0 => 'LUC',
  ),
  'LUF' => 
  array (
    0 => 'LUF',
    1 => 0,
    2 => 0,
  ),
  'LUL' => 
  array (
    0 => 'LUL',
  ),
  'LVL' => 
  array (
    0 => 'LVL',
  ),
  'LVR' => 
  array (
    0 => 'LVR',
  ),
  'LYD' => 
  array (
    0 => 'LYD',
    1 => 3,
    2 => 0,
  ),
  'MAD' => 
  array (
    0 => 'MAD',
  ),
  'MAF' => 
  array (
    0 => 'MAF',
  ),
  'MCF' => 
  array (
    0 => 'MCF',
  ),
  'MDC' => 
  array (
    0 => 'MDC',
  ),
  'MDL' => 
  array (
    0 => 'MDL',
  ),
  'MGA' => 
  array (
    0 => 'MGA',
    1 => 0,
    2 => 0,
  ),
  'MGF' => 
  array (
    0 => 'MGF',
    1 => 0,
    2 => 0,
  ),
  'MKD' => 
  array (
    0 => 'MKD',
  ),
  'MKN' => 
  array (
    0 => 'MKN',
  ),
  'MLF' => 
  array (
    0 => 'MLF',
  ),
  'MMK' => 
  array (
    0 => 'MMK',
    1 => 0,
    2 => 0,
  ),
  'MNT' => 
  array (
    0 => 'MNT',
    1 => 2,
    2 => 0,
  ),
  'MOP' => 
  array (
    0 => 'MOP',
  ),
  'MRO' => 
  array (
    0 => 'MRO',
    1 => 0,
    2 => 0,
  ),
  'MRU' => 
  array (
    0 => 'MRU',
  ),
  'MTL' => 
  array (
    0 => 'MTL',
  ),
  'MTP' => 
  array (
    0 => 'MTP',
  ),
  'MUR' => 
  array (
    0 => 'MUR',
    1 => 2,
    2 => 0,
  ),
  'MVP' => 
  array (
    0 => 'MVP',
  ),
  'MVR' => 
  array (
    0 => 'MVR',
  ),
  'MWK' => 
  array (
    0 => 'MWK',
  ),
  'MXN' => 
  array (
    0 => 'MX$',
  ),
  'MXP' => 
  array (
    0 => 'MXP',
  ),
  'MXV' => 
  array (
    0 => 'MXV',
  ),
  'MYR' => 
  array (
    0 => 'MYR',
  ),
  'MZE' => 
  array (
    0 => 'MZE',
  ),
  'MZM' => 
  array (
    0 => 'MZM',
  ),
  'MZN' => 
  array (
    0 => 'MZN',
  ),
  'NAD' => 
  array (
    0 => 'NAD',
  ),
  'NGN' => 
  array (
    0 => 'NGN',
  ),
  'NIC' => 
  array (
    0 => 'NIC',
  ),
  'NIO' => 
  array (
    0 => 'NIO',
  ),
  'NLG' => 
  array (
    0 => 'NLG',
  ),
  'NOK' => 
  array (
    0 => 'NOK',
    1 => 2,
    2 => 0,
  ),
  'NPR' => 
  array (
    0 => 'NPR',
  ),
  'NZD' => 
  array (
    0 => 'NZ$',
  ),
  'OMR' => 
  array (
    0 => 'OMR',
    1 => 3,
    2 => 0,
  ),
  'PAB' => 
  array (
    0 => 'PAB',
  ),
  'PEI' => 
  array (
    0 => 'PEI',
  ),
  'PEN' => 
  array (
    0 => 'PEN',
  ),
  'PES' => 
  array (
    0 => 'PES',
  ),
  'PGK' => 
  array (
    0 => 'PGK',
  ),
  'PHP' => 
  array (
    0 => '₱',
  ),
  'PKR' => 
  array (
    0 => 'PKR',
    1 => 2,
    2 => 0,
  ),
  'PLN' => 
  array (
    0 => 'PLN',
  ),
  'PLZ' => 
  array (
    0 => 'PLZ',
  ),
  'PTE' => 
  array (
    0 => 'PTE',
  ),
  'PYG' => 
  array (
    0 => 'PYG',
    1 => 0,
    2 => 0,
  ),
  'QAR' => 
  array (
    0 => 'QAR',
  ),
  'RHD' => 
  array (
    0 => 'RHD',
  ),
  'ROL' => 
  array (
    0 => 'ROL',
  ),
  'RON' => 
  array (
    0 => 'RON',
  ),
  'RSD' => 
  array (
    0 => 'RSD',
    1 => 0,
    2 => 0,
  ),
  'RUB' => 
  array (
    0 => 'RUB',
  ),
  'RUR' => 
  array (
    0 => 'RUR',
  ),
  'RWF' => 
  array (
    0 => 'RWF',
    1 => 0,
    2 => 0,
  ),
  'SAR' => 
  array (
    0 => 'SAR',
  ),
  'SBD' => 
  array (
    0 => 'SBD',
  ),
  'SCR' => 
  array (
    0 => 'SCR',
  ),
  'SDD' => 
  array (
    0 => 'SDD',
  ),
  'SDG' => 
  array (
    0 => 'SDG',
  ),
  'SDP' => 
  array (
    0 => 'SDP',
  ),
  'SEK' => 
  array (
    0 => 'SEK',
    1 => 2,
    2 => 0,
  ),
  'SGD' => 
  array (
    0 => 'SGD',
  ),
  'SHP' => 
  array (
    0 => 'SHP',
  ),
  'SIT' => 
  array (
    0 => 'SIT',
  ),
  'SKK' => 
  array (
    0 => 'SKK',
  ),
  'SLE' => 
  array (
    0 => 'SLE',
    1 => 2,
    2 => 0,
  ),
  'SLL' => 
  array (
    0 => 'SLL',
    1 => 0,
    2 => 0,
  ),
  'SOS' => 
  array (
    0 => 'SOS',
    1 => 0,
    2 => 0,
  ),
  'SRD' => 
  array (
    0 => 'SRD',
  ),
  'SRG' => 
  array (
    0 => 'SRG',
  ),
  'SSP' => 
  array (
    0 => 'SSP',
  ),
  'STD' => 
  array (
    0 => 'STD',
    1 => 0,
    2 => 0,
  ),
  'STN' => 
  array (
    0 => 'STN',
  ),
  'SUR' => 
  array (
    0 => 'SUR',
  ),
  'SVC' => 
  array (
    0 => 'SVC',
  ),
  'SYP' => 
  array (
    0 => 'SYP',
    1 => 0,
    2 => 0,
  ),
  'SZL' => 
  array (
    0 => 'SZL',
  ),
  'THB' => 
  array (
    0 => 'THB',
  ),
  'TJR' => 
  array (
    0 => 'TJR',
  ),
  'TJS' => 
  array (
    0 => 'TJS',
  ),
  'TMM' => 
  array (
    0 => 'TMM',
    1 => 0,
    2 => 0,
  ),
  'TMT' => 
  array (
    0 => 'TMT',
  ),
  'TND' => 
  array (
    0 => 'TND',
    1 => 3,
    2 => 0,
  ),
  'TOP' => 
  array (
    0 => 'TOP',
  ),
  'TPE' => 
  array (
    0 => 'TPE',
  ),
  'TRL' => 
  array (
    0 => 'TRL',
    1 => 0,
    2 => 0,
  ),
  'TRY' => 
  array (
    0 => 'TRY',
  ),
  'TTD' => 
  array (
    0 => 'TTD',
  ),
  'TWD' => 
  array (
    0 => 'NT$',
    1 => 2,
    2 => 0,
  ),
  'TZS' => 
  array (
    0 => 'TZS',
    1 => 2,
    2 => 0,
  ),
  'UAH' => 
  array (
    0 => 'UAH',
  ),
  'UAK' => 
  array (
    0 => 'UAK',
  ),
  'UGS' => 
  array (
    0 => 'UGS',
  ),
  'UGX' => 
  array (
    0 => 'UGX',
    1 => 0,
    2 => 0,
  ),
  'USD' => 
  array (
    0 => '$',
  ),
  'USN' => 
  array (
    0 => 'USN',
  ),
  'USS' => 
  array (
    0 => 'USS',
  ),
  'UYI' => 
  array (
    0 => 'UYI',
    1 => 0,
    2 => 0,
  ),
  'UYP' => 
  array (
    0 => 'UYP',
  ),
  'UYU' => 
  array (
    0 => 'UYU',
  ),
  'UYW' => 
  array (
    0 => 'UYW',
    1 => 4,
    2 => 0,
  ),
  'UZS' => 
  array (
    0 => 'UZS',
    1 => 2,
    2 => 0,
  ),
  'VEB' => 
  array (
    0 => 'VEB',
  ),
  'VED' => 
  array (
    0 => 'VED',
  ),
  'VEF' => 
  array (
    0 => 'VEF',
    1 => 2,
    2 => 0,
  ),
  'VES' => 
  array (
    0 => 'VES',
  ),
  'VND' => 
  array (
    0 => '₫',
    1 => 0,
    2 => 0,
  ),
  'VNN' => 
  array (
    0 => 'VNN',
  ),
  'VUV' => 
  array (
    0 => 'VUV',
    1 => 0,
    2 => 0,
  ),
  'WST' => 
  array (
    0 => 'WST',
  ),
  'XAF' => 
  array (
    0 => 'FCFA',
    1 => 0,
    2 => 0,
  ),
  'XCD' => 
  array (
    0 => 'EC$',
  ),
  'XCG' => 
  array (
    0 => 'Cg.',
  ),
  'XEU' => 
  array (
    0 => 'XEU',
  ),
  'XFO' => 
  array (
    0 => 'XFO',
  ),
  'XFU' => 
  array (
    0 => 'XFU',
  ),
  'XOF' => 
  array (
    0 => 'F CFA',
    1 => 0,
    2 => 0,
  ),
  'XPF' => 
  array (
    0 => 'CFPF',
    1 => 0,
    2 => 0,
  ),
  'XRE' => 
  array (
    0 => 'XRE',
  ),
  'YDD' => 
  array (
    0 => 'YDD',
  ),
  'YER' => 
  array (
    0 => 'YER',
    1 => 0,
    2 => 0,
  ),
  'YUD' => 
  array (
    0 => 'YUD',
  ),
  'YUM' => 
  array (
    0 => 'YUM',
  ),
  'YUN' => 
  array (
    0 => 'YUN',
  ),
  'YUR' => 
  array (
    0 => 'YUR',
  ),
  'ZAL' => 
  array (
    0 => 'ZAL',
  ),
  'ZAR' => 
  array (
    0 => 'ZAR',
  ),
  'ZMK' => 
  array (
    0 => 'ZMK',
    1 => 0,
    2 => 0,
  ),
  'ZMW' => 
  array (
    0 => 'ZMW',
  ),
  'ZRN' => 
  array (
    0 => 'ZRN',
  ),
  'ZRZ' => 
  array (
    0 => 'ZRZ',
  ),
  'ZWD' => 
  array (
    0 => 'ZWD',
    1 => 0,
    2 => 0,
  ),
  'ZWG' => 
  array (
    0 => 'ZWG',
  ),
  'ZWL' => 
  array (
    0 => 'ZWL',
  ),
  'ZWR' => 
  array (
    0 => 'ZWR',
  ),
  'DEFAULT' => 
  array (
    1 => 2,
    2 => 0,
  ),
);
