@extends('layouts.app')

@section('title', 'My Profile')

@section('content')
<div class="container">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar-section">
                <div class="avatar-container">
                    @if(Auth::user()->avatar)
                        <img src="{{ Storage::url(Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}" class="profile-avatar">
                    @else
                        <div class="avatar-placeholder">
                            {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                        </div>
                    @endif
                    <button type="button" class="avatar-upload-btn" onclick="document.getElementById('avatar-upload').click()">
                        📷
                    </button>
                    <input type="file" id="avatar-upload" accept="image/*" style="display: none;" onchange="uploadAvatar(this)">
                </div>
            </div>

            <div class="profile-info">
                <h1>{{ Auth::user()->name }}</h1>
                <p class="profile-email">{{ Auth::user()->email }}</p>
                <div class="profile-badges">
                    @foreach(Auth::user()->roles as $role)
                        <span class="role-badge role-{{ $role->name }}">
                            @if($role->name === 'super-admin')
                                👑 {{ ucfirst($role->name) }}
                            @elseif($role->name === 'admin')
                                🛡️ {{ ucfirst($role->name) }}
                            @elseif($role->name === 'instructor')
                                🎓 {{ ucfirst($role->name) }}
                            @else
                                👤 {{ ucfirst($role->name) }}
                            @endif
                        </span>
                    @endforeach
                </div>
                <div class="profile-stats">
                    <div class="stat">
                        <span class="stat-number">{{ Auth::user()->enrollments()->count() }}</span>
                        <span class="stat-label">Courses Enrolled</span>
                    </div>
                    @if(Auth::user()->hasRole('instructor'))
                        <div class="stat">
                            <span class="stat-number">{{ Auth::user()->courses()->count() }}</span>
                            <span class="stat-label">Courses Created</span>
                        </div>
                    @endif
                    <div class="stat">
                        <span class="stat-number">{{ Auth::user()->communityMemberships()->count() }}</span>
                        <span class="stat-label">Communities</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Tabs -->
        <div class="profile-tabs">
            <button class="tab-btn active" data-tab="personal">
                <span class="tab-icon">👤</span>
                <span class="tab-text">Personal Info</span>
            </button>
            <button class="tab-btn" data-tab="security">
                <span class="tab-icon">🔒</span>
                <span class="tab-text">Security</span>
            </button>
            <button class="tab-btn" data-tab="preferences">
                <span class="tab-icon">⚙️</span>
                <span class="tab-text">Preferences</span>
            </button>
            <button class="tab-btn" data-tab="activity">
                <span class="tab-icon">📊</span>
                <span class="tab-text">Activity</span>
            </button>
        </div>

        <!-- Profile Content -->
        <div class="profile-content">
            <!-- Personal Information Tab -->
            <div class="tab-panel active" id="personal-panel">
                <div class="panel-header">
                    <h2>Personal Information</h2>
                    <p>Update your personal details and profile information</p>
                </div>

                <form class="profile-form" id="personal-form" method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('PUT')

                    <div class="form-section">
                        <h3>Basic Information</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" id="name" name="name" class="form-control @error('name') is-invalid @enderror"
                                       value="{{ old('name', Auth::user()->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" id="email" name="email" class="form-control @error('email') is-invalid @enderror"
                                       value="{{ old('email', Auth::user()->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea id="bio" name="bio" class="form-control @error('bio') is-invalid @enderror"
                                      rows="4" placeholder="Tell us about yourself...">{{ old('bio', Auth::user()->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" id="location" name="location" class="form-control @error('location') is-invalid @enderror"
                                       value="{{ old('location', Auth::user()->location) }}" placeholder="City, Country">
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" id="website" name="website" class="form-control @error('website') is-invalid @enderror"
                                       value="{{ old('website', Auth::user()->website) }}" placeholder="https://yourwebsite.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="twitter" class="form-label">Twitter</label>
                                <input type="text" id="twitter" name="twitter" class="form-control @error('twitter') is-invalid @enderror"
                                       value="{{ old('twitter', Auth::user()->twitter) }}" placeholder="@username">
                                @error('twitter')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="linkedin" class="form-label">LinkedIn</label>
                                <input type="text" id="linkedin" name="linkedin" class="form-control @error('linkedin') is-invalid @enderror"
                                       value="{{ old('linkedin', Auth::user()->linkedin) }}" placeholder="linkedin.com/in/username">
                                @error('linkedin')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            💾 Save Changes
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            🔄 Reset
                        </button>
                    </div>
                </form>
            </div>

            <!-- Security Tab -->
            <div class="tab-panel" id="security-panel">
                <div class="panel-header">
                    <h2>Security Settings</h2>
                    <p>Manage your password and security preferences</p>
                </div>

                <form class="profile-form" id="security-form" method="POST" action="{{ route('profile.password') }}">
                    @csrf
                    @method('PUT')

                    <div class="form-section">
                        <h3>Change Password</h3>

                        <div class="form-group">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" id="current_password" name="current_password"
                                   class="form-control @error('current_password') is-invalid @enderror" required>
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" id="password" name="password"
                                       class="form-control @error('password') is-invalid @enderror" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" id="password_confirmation" name="password_confirmation"
                                       class="form-control" required>
                            </div>
                        </div>

                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <ul>
                                <li>At least 8 characters long</li>
                                <li>Contains at least one uppercase letter</li>
                                <li>Contains at least one lowercase letter</li>
                                <li>Contains at least one number</li>
                                <li>Contains at least one special character</li>
                            </ul>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            🔒 Update Password
                        </button>
                    </div>
                </form>

                <div class="security-info">
                    <div class="info-section">
                        <h3>Account Security</h3>
                        <div class="security-items">
                            <div class="security-item">
                                <div class="security-icon">
                                    @if(Auth::user()->email_verified_at)
                                        ✅
                                    @else
                                        ⚠️
                                    @endif
                                </div>
                                <div class="security-details">
                                    <h4>Email Verification</h4>
                                    <p>
                                        @if(Auth::user()->email_verified_at)
                                            Your email is verified
                                        @else
                                            Your email is not verified
                                        @endif
                                    </p>
                                </div>
                                @if(!Auth::user()->email_verified_at)
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="resendVerification()">
                                        Resend
                                    </button>
                                @endif
                            </div>

                            <div class="security-item">
                                <div class="security-icon">🔐</div>
                                <div class="security-details">
                                    <h4>Two-Factor Authentication</h4>
                                    <p>Add an extra layer of security to your account</p>
                                </div>
                                <button type="button" class="btn btn-sm btn-secondary" onclick="setup2FA()">
                                    Setup
                                </button>
                            </div>

                            <div class="security-item">
                                <div class="security-icon">📱</div>
                                <div class="security-details">
                                    <h4>Active Sessions</h4>
                                    <p>Manage your active login sessions</p>
                                </div>
                                <button type="button" class="btn btn-sm btn-secondary" onclick="manageSessions()">
                                    Manage
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preferences Tab -->
            <div class="tab-panel" id="preferences-panel">
                <div class="panel-header">
                    <h2>Preferences</h2>
                    <p>Customize your experience and notification settings</p>
                </div>

                <form class="profile-form" id="preferences-form" method="POST" action="{{ route('profile.preferences') }}">
                    @csrf
                    @method('PUT')

                    <div class="form-section">
                        <h3>Notification Preferences</h3>

                        <div class="preference-options">
                            <div class="preference-item">
                                <label class="toggle-label">
                                    <input type="checkbox" name="email_notifications" checked>
                                    <span class="toggle-slider"></span>
                                    <div class="toggle-content">
                                        <span class="toggle-title">Email Notifications</span>
                                        <span class="toggle-desc">Receive important updates via email</span>
                                    </div>
                                </label>
                            </div>

                            <div class="preference-item">
                                <label class="toggle-label">
                                    <input type="checkbox" name="course_updates" checked>
                                    <span class="toggle-slider"></span>
                                    <div class="toggle-content">
                                        <span class="toggle-title">Course Updates</span>
                                        <span class="toggle-desc">Get notified about new lessons and announcements</span>
                                    </div>
                                </label>
                            </div>

                            <div class="preference-item">
                                <label class="toggle-label">
                                    <input type="checkbox" name="community_notifications" checked>
                                    <span class="toggle-slider"></span>
                                    <div class="toggle-content">
                                        <span class="toggle-title">Community Notifications</span>
                                        <span class="toggle-desc">Receive notifications from community discussions</span>
                                    </div>
                                </label>
                            </div>

                            <div class="preference-item">
                                <label class="toggle-label">
                                    <input type="checkbox" name="marketing_emails">
                                    <span class="toggle-slider"></span>
                                    <div class="toggle-content">
                                        <span class="toggle-title">Marketing Emails</span>
                                        <span class="toggle-desc">Receive promotional content and special offers</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Display Preferences</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="timezone" class="form-label">Timezone</label>
                                <select id="timezone" name="timezone" class="form-control">
                                    <option value="UTC">UTC</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Chicago">Central Time</option>
                                    <option value="America/Denver">Mountain Time</option>
                                    <option value="America/Los_Angeles" selected>Pacific Time</option>
                                    <option value="Europe/London">London</option>
                                    <option value="Europe/Paris">Paris</option>
                                    <option value="Asia/Tokyo">Tokyo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="language" class="form-label">Language</label>
                                <select id="language" name="language" class="form-control">
                                    <option value="en" selected>English</option>
                                    <option value="es">Español</option>
                                    <option value="fr">Français</option>
                                    <option value="de">Deutsch</option>
                                    <option value="pt">Português</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            💾 Save Preferences
                        </button>
                    </div>
                </form>
            </div>

            <!-- Activity Tab -->
            <div class="tab-panel" id="activity-panel">
                <div class="panel-header">
                    <h2>Recent Activity</h2>
                    <p>Your recent actions and achievements on the platform</p>
                </div>

                <div class="activity-timeline">
                    @forelse(Auth::user()->activities()->latest()->take(20)->get() as $activity)
                        <div class="activity-item">
                            <div class="activity-icon">
                                @if($activity->type === 'course_enrolled')
                                    📚
                                @elseif($activity->type === 'payment_made')
                                    💰
                                @elseif($activity->type === 'community_joined')
                                    👥
                                @elseif($activity->type === 'post_created')
                                    📝
                                @else
                                    📋
                                @endif
                            </div>
                            <div class="activity-content">
                                <p>{{ $activity->description }}</p>
                                <span class="activity-time">{{ $activity->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    @empty
                        <div class="no-activity">
                            <div class="no-activity-icon">📊</div>
                            <h3>No Recent Activity</h3>
                            <p>Start exploring courses and joining communities to see your activity here!</p>
                            <a href="{{ route('courses.index') }}" class="btn btn-primary">Browse Courses</a>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Danger Zone -->
        <div class="danger-zone">
            <div class="danger-header">
                <h3>⚠️ Danger Zone</h3>
                <p>Irreversible and destructive actions</p>
            </div>

            <div class="danger-actions">
                <div class="danger-action">
                    <div class="action-info">
                        <h4>Delete Account</h4>
                        <p>Permanently delete your account and all associated data. This action cannot be undone.</p>
                    </div>
                    <button type="button" class="btn btn-danger" onclick="deleteAccount()">
                        🗑️ Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.profile-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.avatar-container {
    position: relative;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: bold;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.avatar-upload-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #3b82f6;
    border: 3px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.avatar-upload-btn:hover {
    background: #1d4ed8;
    transform: scale(1.1);
}

.profile-info h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-email {
    color: #a0a0a0;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.profile-badges {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.role-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.role-super-admin {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.role-admin {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.role-instructor {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.role-student {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.profile-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.profile-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 0.5rem;
}

.tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: #a0a0a0;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.tab-icon {
    font-size: 1.25rem;
}

.profile-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h2 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.panel-header p {
    color: #a0a0a0;
    font-size: 1rem;
    margin: 0;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
    color: #a0a0a0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.password-requirements {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.password-requirements h4 {
    color: #f59e0b;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.password-requirements li::before {
    content: "•";
    color: #f59e0b;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.security-info {
    margin-top: 2rem;
}

.security-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.security-icon {
    font-size: 1.5rem;
}

.security-details {
    flex: 1;
}

.security-details h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.security-details p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.preference-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.preference-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
    background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-content {
    display: flex;
    flex-direction: column;
}

.toggle-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.toggle-desc {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.activity-icon {
    font-size: 1.25rem;
    margin-top: 0.25rem;
}

.activity-content p {
    color: #ffffff;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.no-activity {
    text-align: center;
    padding: 3rem;
}

.no-activity-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-activity h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-activity p {
    color: #a0a0a0;
    margin-bottom: 2rem;
}

.danger-zone {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 20px;
    padding: 2rem;
}

.danger-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(239, 68, 68, 0.3);
}

.danger-header h3 {
    color: #ef4444;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.danger-header p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

.danger-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-info h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.action-info p {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin: 0;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .profile-tabs {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .danger-action {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
</style>

<script>
// Tab functionality
document.querySelectorAll('.tab-btn').forEach(tab => {
    tab.addEventListener('click', function() {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(p => p.classList.remove('active'));

        // Add active class to clicked tab and corresponding panel
        this.classList.add('active');
        document.getElementById(this.dataset.tab + '-panel').classList.add('active');
    });
});

// Avatar upload
function uploadAvatar(input) {
    if (input.files && input.files[0]) {
        const formData = new FormData();
        formData.append('avatar', input.files[0]);

        // Implementation for avatar upload
        console.log('Upload avatar');
        alert('Avatar upload functionality would be implemented here');
    }
}

// Form functions
function resetForm() {
    if (confirm('Reset all changes? Unsaved changes will be lost.')) {
        document.getElementById('personal-form').reset();
    }
}

function resendVerification() {
    // Implementation for resending verification email
    console.log('Resend verification');
    alert('Verification email sent!');
}

function setup2FA() {
    // Implementation for 2FA setup
    console.log('Setup 2FA');
    alert('2FA setup would be implemented here');
}

function manageSessions() {
    // Implementation for session management
    console.log('Manage sessions');
    alert('Session management would be implemented here');
}

function deleteAccount() {
    if (confirm('Are you absolutely sure you want to delete your account? This action cannot be undone and will permanently delete all your data.')) {
        if (confirm('This is your final warning. Type "DELETE" to confirm account deletion.')) {
            // Implementation for account deletion
            console.log('Delete account');
            alert('Account deletion would be implemented here');
        }
    }
}

// Success/error message handling
@if(session('success'))
    alert('{{ session('success') }}');
@endif

@if(session('error'))
    alert('{{ session('error') }}');
@endif
</script>
@endsection